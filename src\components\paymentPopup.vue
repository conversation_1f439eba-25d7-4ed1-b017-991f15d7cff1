<!-- 扫码支付和充值的弹窗 -->
<template>
	<view>
		<u-popup :show="paymentShow" :round="20" @close="payClose" @open="open" mode="center">
						<view class="main">
							<view class="header d-cc">
								<view class="title">扫码支付</view>
								<view class="iconfont icon-close11 title_close" @click="payClose"></view>
							</view>
							<view class="payImg d-cc">
								<image :src="payImg"></image>
							</view>	
						</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		name:"paymentPopup",
		props:{
			paymentShow:{
				type:Boolean,
				default:false
			},
			payImg: {
				type:String,
				default:''
			}
		},
		data() {
			return {
				
			};
		},
		methods:{
			payClose() {
				this.$emit('payClose', false);
			},
		}
	}
</script>

<style lang="scss" scoped>
	.main {
		width: 640rpx;
		// height: 1024rpx;
		border-radius: 20rpx;
		margin: 0 auto;
		background: #fff;
		.header {
			width: 100%;
			height: 80rpx;
			line-height: 80rpx;
			position: relative;
			.title_close {
				position: absolute;
				right:20rpx;
			}
		}
		.payImg {
			image {
				width:250rpx;
				height:250rpx;
			}
		}
	}
</style>
