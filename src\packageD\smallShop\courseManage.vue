<template>
  <!-- 课程管理 -->
  <view>
    <!-- 搜索框与一级 start -->
    <view class="head">
      <u-row
        justify="space-between"
        gutter="40"
        customStyle="margin-bottom: 20rpx"
      >
        <u-col span="9">
          <u-search
            height="70"
            searchIconSize="40"
            color="#666666"
            bgColor="#eff0f1"
            v-model="formData.title"
            placeholder="搜索课程"
            :showAction="false"
            @change="search"
          ></u-search>
        </u-col>
        <u-col span="3">
          <u-button
            type="error"
            size="small"
            shape="circle"
            text="一键同步"
            @click="importCourseProduct"
          ></u-button>
        </u-col>
      </u-row>
      <u-row justify="space-between">
        <u-col span="12">
          <!-- 一级分类-tabs start -->
          <view class="con-tabs">
            <u-tabs
              lineWidth="100rpx"
              lineColor="#f56c6c"
              itemStyle="padding-right: 20rpx; height: 68rpx"
              :list="firstTab"
              :current="firstIndex"
              :scrollable="true"
              :activeStyle="{ color: '#f14e4e' }"
              :inactiveStyle="{ color: '#0c0d0e' }"
              @change="onChangeFirstTab"
            >
              <view slot="right" @click="tapShow = !tapShow">
                <u-icon
                  size="30"
                  bold
                  :name="tapShow ? 'arrow-up' : 'arrow-down'"
                  color="#8F8F8F"
                ></u-icon>
              </view>
            </u-tabs>
            <!-- 一级分类全部展开 start -->
            <transition name="fade">
              <view v-if="tapShow" class="con-drawer">
                <template v-for="(item, index) in firstTab">
                  <view
                    class="btn-2"
                    :key="index"
                    v-bind:class="{ activeSub: isActiveSub === index }"
                    @click="onClickSubBtn(index, item)"
                  >
                    {{ item.name }}
                  </view>
                </template>
              </view>
            </transition>
            <!-- 一级分类全部展开 end -->
          </view>
          <!-- 一级分类-tabs end -->
        </u-col>
      </u-row>
    </view>
    <!-- 搜索框与一级 end -->
    <!-- 筛选 start -->
    <view class="d-f-c w100">
      <view class="vh6 d-f w100">
        <view class="con-sort">
          <SortButtonGroup v-model="sortForm" @change="onChangeSort">
            <SortButton value="price">价格</SortButton>
            <SortButton value="sales">销量</SortButton>
          </SortButtonGroup>
        </view>
      </view>
    </view>
    <!-- 筛选 end -->
    <!-- 课程商品 -->
    <view
      class="bg-white p-20 mb-20 ml-20 swhb radius10 mr-20 mt-30"
      v-for="item in productList"
      :key="item.id"
      @click="jumpCurriculum(item.id)"
    >
      <view class="d-f">
        <view>
          <u--image
            :showLoading="true"
            :src="item.image_url"
            width="150rpx"
            height="150rpx"
            radius="10"
          ></u--image>
        </view>
        <view class="ml-20">
          <view style="width: 501rpx" class="fs-2 ell">
            {{ item.title }}
          </view>
          <view>
            <view class="d-bf mt_17 fs-1-5" style="width: 510rpx">
              <view class="fs-1">
                <view class="chapter-count">
                  <text>
                    {{ item.curriculum.chapter_count }}章{{
                      item.curriculum.subsection_count
                    }}节
                  </text>
                </view>
              </view>
            </view>
          </view>
          <!-- 显示价格 -->
          <view class="fw-b c-f14e4e mt-10 price">
            <text>
              {{ '¥' + toYuan(item.agreement_price) }}
            </text>
            <text class="shop-price">
              {{ '¥' + toYuan(item.shop_price) }}
            </text>
          </view>
        </view>
      </view>
    </view>
    <view class="d-cc fs-1 c-5e5e5e mb-25 mt-25">
      <view v-if="productList.length === total">暂无更多~</view>
      <view v-if="productList.length == 0">暂无数据~</view>
    </view>
  </view>
</template>

<script>
import SortButton from '../smallShop/components/sortButton.vue'
import SortButtonGroup from '../smallShop/components/sortButtonGroup.vue'

export default {
  name: 'courseManage',
  components: { SortButton, SortButtonGroup },
  data() {
    return {
      productSetting: {}, // 商品价格设置
      firstTab: [{ id: 'all', name: '全部' }], // 分类Tabs
      firstIndex: 0, // 一级分类索引
      tapShow: false, // 展开一级drawer
      isActiveSub: 0, // 样式开关：drawer效果-按钮
      sortForm: {
        value: 'price', // 价格price, 销量sales
        sort: '2', // 1为升序，2为降序
      },
      page: 1, // 当前页数
      pageSize: 15, // 每页条数
      total: null, // 总数
      productList: [], // 商品列表
      formData: {
        title: '', // 名称搜索
        category1_id: null, // 切换TabsID
        sort_by: 2, // 切换分类
      },
    }
  },

  onLoad() {
    this.getCourseCategorys()
    this.getCourseProductList()
    this.getProductSetting()
  },

  // 触底加载
  async onReachBottom() {
    if (this.productList.length < this.total) {
      this.page = this.page + 1
      const data = {
        page: this.page,
        pageSize: this.pageSize,
        title: this.formData.title,
        sort_by: this.formData.sort_by,
        category1_id: this.formData.category1_id,
      }
      const res = await this.post(
        '/api/curriculum/getMyCourseProductList',
        data,
      )
      if (res.code === 0) {
        this.productList.push(...res.data.list)
        this.total = res.data.total
      }
    }
  },

  methods: {
    // 获取商品设置
    async getProductSetting() {
      const { data } = await this.get(
        '/api/smallShop/setting/getShopProductSetting',
      )
      this.productSetting = data.setting.value
    },
    // 获取课程商品列表
    async getCourseProductList() {
      const data = {
        page: this.page,
        pageSize: this.pageSize,
        title: this.formData.title,
        sort_by: this.formData.sort_by,
        category1_id: this.formData.category1_id,
      }
      const res = await this.post(
        '/api/curriculum/getMyCourseProductList',
        data,
      )
      if (res.code === 0) {
        if (res.data.total === 0) {
          this.productList = []
        } else {
          this.productList = res.data.list
        }
        this.total = res.data.total
      }
    },
    // 获取课程分类
    async getCourseCategorys() {
      const res = await this.post('/api/curriculum/getCourseCategorys')
      if (res.code === 0) {
        res.data.forEach(item => {
          this.firstTab.push(item)
        })
      }
    },
    // 搜索商品
    search() {
      this.page = 1
      this.getCourseProductList()
    },
    // 跳转课程
    jumpCurriculum(id) {
      uni.navigateTo({
        url: '/packageA/commodity/commodity_details/commodity_details?id=' + id,
      })
    },
    // 一节分类所有按钮
    onClickSubBtn(index, item) {
      this.isActiveSub = index // 更改button样式
      this.onChangeFirstTab({ index, ...item })
    },
    // 切换一级分类
    onChangeFirstTab(obj) {
      this.firstIndex = obj.index // 一级标签切换索引
      this.parent_id = this.firstTab[this.firstIndex].id
      // this.level = 2
      // this.getCategoryServe()
      // this.onShowFilter(obj.id)
      console.log('obj.id', obj.id)
      if (obj.id === 'all') {
        this.formData.category1_id = null
      } else {
        this.formData.category1_id = obj.id
      }
      this.search()

      // this.categoryId('category1_id', 1)
    },
    // 按排序条件查询时的入参
    onChangeSort(val) {
      if (val.value === 'sales' && val.sort === '1') {
        this.formData.sort_by = 5 // 销量升
      } else if (val.value === 'sales' && val.sort == '2') {
        this.formData.sort_by = 4 // 销量降
      } else if (val.value === 'price' && val.sort === '1') {
        this.formData.sort_by = 3 // 价格升
      } else {
        this.formData.sort_by = 2 // 价格降
      }
      this.search()
    },
    // 一键同步
    async importCourseProduct() {
      this.formData.title = ''
      const data = {
        price_proportion: 10000,
        price_type: 0,
      }
      // 协议价 agreement_price => 2 建议零售价 origin_price => 0 指导价 guide_price => 1  营销价 market_price=> activity_price =>3
      if (this.productSetting.is_activity_price) {
        data.price_proportion = this.productSetting.activity_price_ratio * 100
        data.price_type = 3
      }
      if (this.productSetting.is_agreement_price) {
        data.price_proportion = this.productSetting.agreement_price_ratio * 100
        data.price_type = 2
      }
      if (this.productSetting.is_guide_price) {
        data.price_proportion = this.productSetting.guide_price_ratio * 100
        data.price_type = 1
      }
      if (this.productSetting.is_origin_price) {
        data.price_proportion = this.productSetting.origin_price_ratio * 100
        data.price_type = 0
      }
      const res = await this.post('/api/curriculum/importCourseProduct', data)
      if (res.code === 0) {
        this.getCourseProductList()
        this.toast('同步成功')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.head {
  padding: 40rpx 30rpx 0 30rpx;
  background-color: #fff;
}

.con-tabs {
  padding-bottom: 12rpx;
  // background: pink;
  .con-drawer {
    display: flex;
    flex-wrap: wrap;
    padding: 20rpx 0;
    // background-color: orange;
    .btn-2 {
      margin: 10rpx 40rpx 0 0;
      padding: 0 18rpx;
      height: 56rpx;
      line-height: 56rpx;
      text-align: center;
      color: #666;
      border: 1px solid white;
      border-radius: 10px;
    }
    .activeSub {
      color: #ea4e4c !important;
      background-color: #f8d8d4;
      border: 1px solid #ea4e4c;
      transition: color 0.2s linear, background-color 0.2s linear,
        border-color 0.2s linear;
    }
  }
}

.con-sort {
  margin-top: 30rpx;
  width: 24vw;
  margin-left: 40rpx;
}

.chapter-count {
  background-color: #fcecd8;
  border-radius: 5rpx;
  margin-top: 10rpx;
  color: #fb8621;
  text {
    padding: 5rpx 10rpx;
  }
}

.price {
  font-size: 32rpx;
  margin-top: 40rpx;
  .shop-price {
    margin-left: 10rpx;
    color: rgba(0, 0, 0, 0.4);
    text-decoration: line-through;
  }
}
</style>
