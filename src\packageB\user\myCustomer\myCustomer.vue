<!-- 我的客户 -->
<template>
	<view>
		<!-- 搜索框 -->
		<view class="d-bf pl-20 pr_20 pt_14 pb_14 bg-white p-20">
			<view class="d-cc">
				<view class="iconfont icon-member-left" style="color: #747474;font-size: 20rpx;" @tap="navigateBack">
				</view>
				<view class="ml-15">
					<view>
						<view style="width:690rpx;">
							<u-search height="60" bgColor="#eff0f1" color="#666666" @custom='getUserChildList' @search='getUserChildList' searchIconSize="40" placeholder="搜索昵称" v-model="paging.keyword"></u-search>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!--  -->
		<view class="p-20">
			<view class="fs-1 ml-15" style="color:#828282;">总数:{{total}}</view>
			<view class="bg-white p-20 mt-20 radius15 mb-20" v-for="(item,index) in list" :key="index">
				<view class="d-f">
					<view class="d-cc">{{item.nickname}}</view>
					<view class="c-f14e4e radius30 fs-1 d-cc ml-15 cs1">{{item.id}}</view>
				</view>
				<view class="mt-20 fs-1" style="color: #7f7f7f;">
					{{formatDateTime(item.created_at,6)}}
				</view>
				<!-- <view style="background-color: #E4E7ED;width: 670rpx;height: 0.5rpx;" class="mt-20"></view> -->
				<view class="d-bf mt-30 fs-1-5 mb-10">
					<view>等级：{{item.level.name}}</view>
					<view style="margin-right: 130rpx;">客户数量：{{item.child_count}}</view>
				</view>
			</view>
			<view class="d-cc fs-1 c-5e5e5e mb-25">
				<view v-if="list.length != 0&&readMore">暂无更多~</view>
				<view v-if="list.length == 0">暂无数据~</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				paging: {
					page: 1,
					pageSize: 10,
					keyword: ''
				},
				list: {}, //客户列表
				list2: {}, //下拉刷新
				total: '', //客户总数
			}
		},
		onLoad() {
			this.getUserChildList("first")
		},
		computed: {
			readMore() { //为了适配小程序端{{}}不能直接读取vuex值的问题
				return this.$store.state.homeNoMore
			}
		},
		methods: {
			getUserChildList(item) { //获取客户列表
				if (item != "refresh") {
					this.paging.page = 1
				}
				let params = {
					page: this.paging.page,
					pageSize: this.paging.pageSize
				}
				if(this.paging.keyword){
					params.keyword = this.paging.keyword
				}
				this.post('/api/user/getUserChildList', params, true).then(data => {
					this.list2 = data.data
					if (this.list2.page < this.list2.total / this.list2.pageSize) { //判断是不是最后一页
						this.$store.commit("upHomeNoMore", false)
						this.paging.page += 1
					} else {
						this.$store.commit("upHomeNoMore", true)
					}
					if (item == "refresh") { //刷新
						this.list = [...this.list,...data.data.list]
					} else {
						this.list = data.data.list
					}
					if (item == "first") { //第一次进入
						this.total = data.data.total
					}
				})
			},
			onReachBottom() {
				if (!this.$store.state.homeNoMore) {
					this.getUserChildList("refresh")
				}
			},
		}
	}
</script>

<style>
	.cs1 {
		background-color: #FEEDED;
		padding: 10rpx 20rpx 10rpx 20rpx;
	}
</style>
