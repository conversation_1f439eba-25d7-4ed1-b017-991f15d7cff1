<!-- 竖向商品展示组件 -->
<template>
	<view>
		<!-- 竖向展示-->
		<view class="d-bf2 m-20 " style="flex-flow: wrap;">
			<view class="bg-white swhb2 radius10" v-for="(item,index) in list" :key="index"
				@click="goCommodity_details(item.id)">
				<view>
					<u--image :showLoading="true" width="350rpx" height="350rpx" :src="item.thumb">
					</u--image>
				</view>
				<view class="p-20 fs-0">
					<view class="fw-b ell fs-1">{{item.title}}</view>
					<view v-if="item.is_display!=0">
						<view class="c-7e7e7e fs-1-5 mt_17">
							<text>已售</text><text class="ml-15">{{item.sales}}</text>
						</view>
						<view class="mt-20" v-if="page_setup_setting.pc_list_setting.is_web_super_wholesal_price === 0">
							<text style="color:#3e3e3e;">{{page_setup_setting.language_setting.super_wholesal_price === '' ? '超级批发价' : page_setup_setting.language_setting.super_wholesal_price}}</text>
							<text v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.id)" class="c-f14e4e ml-15">{{'¥'+getSuperPrice(item)}}</text>
							<text v-else-if="userLevelPriceTitle" class="c-f14e4e ml-15">{{'¥'+userLevelPriceTitle}}</text>
							<text v-else class="c-f14e4e ml-15">{{checkNull(user)?'¥'+getSuperPrice(item):'价格登录可见'}}</text>
						</view>
						<view class="mt-15" v-if="page_setup_setting.pc_list_setting.is_web_price === 0">
							<text>{{page_setup_setting.language_setting.price === '' ? '批发价' : page_setup_setting.language_setting.price}}</text>
							<text v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.id)" class="ml-15 c-f14d4d">{{'¥'+item.price}}</text>
							<text v-else-if="userLevelPriceTitle" class="ml-15 c-f14d4d">{{'¥'+userLevelPriceTitle}}</text>
							<text v-else class="ml-15 c-f14d4d">{{checkNull(user)?'¥'+item.price:'价格登录可见'}}</text>
						</view>
						<view class="mt-15" v-if="page_setup_setting.pc_list_setting.is_web_suggested_retail_price === 0">
							<text>{{page_setup_setting.language_setting.suggested_retail_price === '' ? '建议零售价' : page_setup_setting.language_setting.suggested_retail_price}}</text>
							<text class="ml-15 c-f14d4d">¥{{item.origin_price}}</text>
						</view>
						<view class="pt_8 pb_7 pl_19 pr_18 mt-20 make" v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.id)">
							单笔赚 ¥{{toYuan(item.level_profit)}}
						</view>
						<view class="pt_8 pb_7 pl_19 pr_18 mt-20 make" v-else-if="userLevelPriceTitle">
							单笔赚 ¥{{userLevelPriceTitle}}
						</view>
						<view class="pt_8 pb_7 pl_19 pr_18 mt-20 make" v-else>
							单笔赚 {{checkNull(user) ? "¥"+toYuan(item.level_profit) : '价格登录可见'}}
						</view>
					</view>
					<view v-else style="background-color: #F0F0F0;width:100rpx;"
						class="c-888 fs-0 d-cc radius30 pt_5 pb_5 mt-15">已失效</view>
				</view>
			</view>
		</view>
		<view class="d-cc c-5e5e5e mb-25 fs-1">
			<view v-if="list.length != 0">暂无更多~</view>
			<!-- <view v-if="list.length != 0 && type === 'details' && orderLength > 3" class="d-cc">
				<text class="c-777 font_size12" @click="viewMore">查看更多</text>
				<text class="iconfont icon-member-bottom font_size17 ml_10"></text>
			</view> -->
			<view v-if="list.length == 0">暂无数据~</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			list: { //商品信息
				type: Array
			},
		},
		data() {
			return {
				user: uni.getStorageSync('user'),
				page_setup_setting:{
					pc_list_setting:{},
					details_setting:{},
					language_setting:{}
				}
			}
		},
		computed: {
			userLevelPriceTitle(){
				return this.$store.state.userLevelPriceTitle || ""
			}
		},
		created(){
			this.get('/api/home/<USER>', {}, true).then(data => {
				this.page_setup_setting = data.data.page_setup_setting
			})
		},
		methods: {
			goCommodity_details(id) { //跳转到商品详情页面
				this.navTo('/packageA/commodity/commodity_details/commodity_details?id=' + id)
			},
		}
	}
</script>

<style>
	.swhb2 {
		width: 350rpx;
		margin-bottom: 20rpx;
		border-radius: 10rpx;
	}

	.make {
		background-color: #fde3b7;
		border-radius: 30rpx;
		display: inline-block;
	}
</style>
