<!-- 区域分红 -->
<template>
	<view>
		<view style="background: #f15353;" class="pt_40 pb_40 pl_20 pr_20 d-bf">
			<view class="fs-1-5 c-white d-ef" v-if="shareAgency.level === 1 ">代理区域：{{shareAgency.province}}</view>
			<view class="fs-1-5 c-white d-ef" v-if="shareAgency.level === 2 ">代理区域：{{shareAgency.province}}{{shareAgency.city}}</view>
			<view class="fs-1-5 c-white d-ef" v-if="shareAgency.level === 3 ">
				代理区域：{{shareAgency.province}}{{shareAgency.city}}{{shareAgency.county}}
			</view>
			<view class="fs-1-5 c-white d-ef" v-if="shareAgency.level === 4 ">
				代理区域：{{shareAgency.province}}{{shareAgency.city}}{{shareAgency.county}}{{shareAgency.town}}
			</view>
			<view class="fs-1-5 c-white d-ef">分红比例{{dividends}}%</view>
		</view>
		<!--  -->
		<view style="background-color:#FFFFFF;border-bottom: 0.0625rem solid #e2e2e2"
			class="d-bf  pl_60 pt_40 pb_20 mt-20">
			<view class="d-cc" v-for="(item,index) in list3" :key="index">
				<view class="d-cc-c pr_60">
					<view style="color: #ffa800">{{item.value | processing}}</view>
					<view class="mt-40 fw-b fs-1">{{item.name}}</view>
				</view>
				<view 
					style="background-color: #e2e2e2;width: 1rpx;height: 110rpx;" 
					:class="index+1 === list3.length ? 'angle' : ''">
					
				</view>
			</view>
			<view>
				<view class="careat">
				</view>
				<view class="d-cc-c mr-60">
					<view style="color: #fc6a70;">{{month | processing}}</view>
					<view class="mt-40 fw-b fs-1">本月累计分红</view>
				</view>
			</view>
		</view>
		<!--  -->
		<view class="d-bf pt_20 pb_20 pr_100 pl_100 mt-20 bg-white">
			<view class="d-cc-c ml-40">
				<view class="iconfont icon-weitixian" style="font-size: 50rpx;color: #999;"></view>
				<view class="mt-25 fs-0-5">
					未结算佣金
				</view>
				<view class="mt-20" style="color: #f15353"><text class="fw-b">{{list4.wait_settle_amount | processing}}</text>元
				</view>
			</view>
			<view class="d-cc-c mr-40">
				<view class="iconfont icon-yijiesuan" style="font-size: 50rpx;color: #999;"></view>
				<view class="mt-25 fs-0-5">
					已结算佣金
				</view>
				<view class="mt-20" style="color: #f15353"><text class="fw-b">{{list4.finish_settle_amount | processing}}</text>元
				</view>
			</view>
		</view>
		<!--  -->
		<view  class="mt-20 bg-white">
			<view>
				<u-tabs :list="list1" :scrollable="false" lineHeight="7rpx" lineWidth="75rpx" lineColor="#ee0a24"
					:activeStyle="{transform: 'scale(0.95)',color: '#323233'}"
					:inactiveStyle="{ transform: 'scale(0.95)'}" @click="getAgencyDays">
				</u-tabs>
			</view>
		</view>
		<!--  -->
		<view>
			<uni-collapse @change="getAgencyBydate($event,'default')"  accordion>
				<uni-collapse-item :title="item.date" v-for="(item,index) in list5" :key="index" title-border="none"
					:border="false"  :open="index===0?true:false">
					<view class="p-20" v-for="(item1,index1) in list6" :key="index1">
						<view class="d-bf">
							<view>
								<view>订单号：{{item1.order_info && item1.order_info.order_sn}}</view>
							</view>
							<view style="color: #20b86a;">分红+{{item1.amount | processing}}</view>
						</view>
						<view class="d-bf mt-15 fs-1" style="color: #8c8c8c;">
							<view>
								<view>时间{{item1.created_at}}</view>
							</view>
							<view v-if="item1.status == 0">未结算</view>
							<view v-if="item1.status == 1">已结算</view>
							<view v-if="item1.status == -1">失效</view>
						</view>
						<view style="color: #8c8c8c;" class="mt_10 fs-1" v-if="item1.level === 1 ">
							<view>代理区域:{{item1.province}}</view>
						</view>
						<view style="color: #8c8c8c;" class="mt_10 fs-1" v-if="item1.level === 2 ">
							<view>代理区域:{{item1.province}} {{item1.city}}</view>
						</view>
						<view style="color: #8c8c8c;" class="mt_10 fs-1" v-if="item1.level === 3 ">
							<view>代理区域:{{item1.province}} {{item1.city}} {{item1.county}}</view>
						</view>
						<view style="color: #8c8c8c;" class="mt_10 fs-1" v-if="item1.level === 4 ">
							<view>代理区域:{{item1.province}} {{item1.city}} {{item1.county}} {{item1.town}}</view>
						</view>
					</view>
					<u-line></u-line>
					<view class="d-cc pb-15 pt-15" v-if="orderPage" @click="getAgencyBydate(index,'refresh')">
						<view class="fs-0 loadMore">加载更多+</view>
					</view>
				</uni-collapse-item>
			</uni-collapse>
			<view class="d-cc mt-30 c-666666" v-if="list5.length == 0">暂无数据~</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				valueCheck: false,
				taBstatus:'',
				dateDefault:['0'],
				shareAgency:{},
				dividends: '', //分红比例
				month: '', //本月累计分红
				list: [{}], //分红信息
				list1: [{
						name: '全部',
						
					}, {
						name: '已结算',
						status:0
					}, {
						name: '未结算',
						status:1
					},
					{
						name: '已失效',
						status:-1
					},
				],
				list3: [],
				list4: {}, //佣金信息
				list5: [], // 订单时间
				list6: [], //订单信息
				orderPage: false, //订单信息是否是最后一页
				list7: { //根据日期获取所有分红数据分页
					page: 1,
					pageSize: 10,
				},
			}
		},
		async onLoad() {
			this.getAwardListValue()
			await this.getAgencyDays();
			let urlApi = `/api/areaAgency/getAgencyBydate?date=${this.list5[0].date}&page=${this.list7.page}&pageSize=
			${this.list7.pageSize}`;
			if(this.taBstatus !== '') {
				urlApi += '&status=' + this.taBstatus;
			}
			this.getAgencyData(urlApi,'default');//默认显示第一个月数据
		},
		filters:{
			processing(price) {
				return price? price : '0.00';
			}
		},
		methods: {
			getAwardListValue() { //获取代理数据
				this.get('/api/areaAgency/getAwardList', {}, true).then(data => {
					let res = data.data;
					this.list = data.data;
					let statistic = res.statistic;
					let {month,today,week,yesterday} = statistic;
					this.$set(this.list3, 0, {
						name: '今日',
						value: this.toYuan(today)
					})
					this.$set(this.list3, 1, {
						name: '昨日',
						value: this.toYuan(yesterday)
					})
					this.$set(this.list3, 2, {
						name: '本周',
						value: this.toYuan(week)
					})
					this.shareAgency = this.list.agency;
					this.dividends = this.list.agency.special_ratio / 100
					this.list4 = data.data.agency
					this.list4.finish_settle_amount = this.toYuan(this.list4.finish_settle_amount);
					this.list4.wait_settle_amount = this.toYuan(this.list4.wait_settle_amount);
					this.month = this.toYuan(month)
				})
			},
			async getAgencyDays(item) { //获取代理订单时间
				let url = '';
				if (item != undefined) {
					if (item.name == "已结算") {
						url = '?status=' + 1
						this.taBstatus = 1;
					} else if (item.name == "未结算") {
						url = '?status=' + 0;
						this.taBstatus = 0;
					} else if (item.name == "已失效") {
						url = '?status=' + -1;
						this.taBstatus = -1;
					} else {
						this.taBstatus = '';
					}
				}
				this.list5 = []
				let result =  await	this.get('/api/areaAgency/getAgencyDays' + url, {}, true).then(data => {
					this.list5 = data.data
				})
				if (item != undefined && this.list5[0]) {
					let urlApi = `/api/areaAgency/getAgencyBydate?date=${this.list5[0].date}&page=${this.list7.page}&pageSize=
					${this.list7.pageSize}`;
					if(this.taBstatus !== '') {
						urlApi += '&status=' + this.taBstatus;
					}
					this.getAgencyData(urlApi,'default');//默认显示第一个月数据
				}
				return result;
			},

			getAgencyBydate(e, type) { //根据日期获取所有分红数据
				console.log(e);
				if (e.length == 0) {
					return
				}
				if (type == "default") { //判断是否是点击加载更多
					this.list7.page = 1
				}else{
					this.list7.page++
				}
				let urlApi = `/api/areaAgency/getAgencyBydate?date=${this.list5[e].date}&page=${this.list7.page}&pageSize=
					${this.list7.pageSize}`;
				if(this.taBstatus !== '') {
					urlApi += '&status=' + this.taBstatus;
				}
				this.getAgencyData(urlApi,type);
			},
			getUrlApi() {
				
			},
			getAgencyData(urlApi,type) {
				this.get(urlApi, {}, true).then(data => {
					let total = Number(data.data.total);
					let pageSize = Number(data.data.pageSize);
					if (data.data.page < data.data.total / data.data.pageSize) { //是否显示加载更多
						this.orderPage = true
					} else {
						this.orderPage = false
					}
					if (type == "default") { //判断是否是点击加载更多
						this.list6 = data.data.list
						this.list6.forEach((item) => {
							item.created_at = this.formatDateTime(item.created_at,6);
							item.amount = this.toYuan(item.amount);
						})
					} else {
						this.list6 = [...this.list6, ...data.data.list]
					}
				})
			}
		}
	}
</script>

<style>
	.angle {
		position: relative;
	}
	.angle::before {
		content: '';
		width: 0.375rem;
		height: 0.375rem;
		border: 0.0625rpx solid #ddd;
		border-left: 0;
		border-bottom: 0;
		-webkit-transform: rotate(45deg);
		transform: rotate(45deg);
		background: #fff;
		position: absolute;
		bottom: calc(50% - 6rpx);
		
		right: -8rpx;
	}

	.loadMore {
		border-radius: 5rpx;
		padding: 10rpx;
		border: #D5D5D5 solid 1rpx;
		width: 120rpx;
	}
</style>
