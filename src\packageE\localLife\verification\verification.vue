<template>
  <view style="background-color: #F5F5F5;padding-top: 20rpx;box-sizing: border-box">
    <view class="box f fac pl-25 pr-25">
      <view>
        <u-image :src="scanCodeData.image_url" radius="16rpx" width="180rpx" height="180rpx"></u-image>
      </view>
      <view class="ml-25 mt-10">
        <view class="f">
          <view class="title" style="width: 370rpx;margin-right: 40rpx;">
            {{ scanCodeData.title }}
          </view>
          <view class="mt-10 number">x {{ scanCodeData.qty }}</view>
        </view>
        <view class="amount mt-40 f fac">
          <view class="yuan">¥</view>{{ toYuan(scanCodeData.amount) }}
        </view>
      </view>
    </view>
    <!-- 套餐详情 -->
    <view class="box pl-25 pr-25">
      <view class="title-box">套餐详情</view>
      <!-- 遍历套餐 -->
      <template v-for="(item, index) in packages">
        <view :key="index">
          <view class="f fac mb-30">
            <view class="dian"></view>
            <view class="title pl-20">{{ item.title }}</view>
          </view>
          <!-- 遍历商品 -->
          <template v-for="(items, indexs) in item.contents">
            <view class=" mb-30" :key="indexs + item.title">
              <view class="f fac fjsb">
                <view class="pl-28 good-name">{{ items.title }}</view>
                <view class="f fac ">
                  <view class="good-amount">（{{ items.usage }}份）</view>
                  <view class="good-name">¥{{ toYuan(items.price) }}</view>
                </view>
              </view>
              <!-- 遍历描述 -->
              <template v-for="(itemss, indexss) in items.describes">
                <view class="pl-28 hint" :key="indexss + itemss.info">{{ itemss.info }}</view>
              </template>
            </view>
          </template>
          <view v-if="index + 1 !== packages.length" class="border_buttom pl-28"></view>
        </view>
      </template>
    </view>
    <!-- 购买须知 -->
    <view class="box pl-25 pr-25" style="margin-bottom: 190rpx;">
      <view class="title-box">购买须知</view>
      <view class="f fac mb-30">
        <view class="dian"></view>
        <view class="title pl-20">可用日期</view>
      </view>
      <!-- expire 1自购买日起2固定日期 -->
      <view class="pl-28 good-name" v-if="purchase.expire.type === 1">有效期：购买后{{ purchase.expire.days }}天内有效</view>
      <view class="pl-28 good-name" v-else>有效期：{{ purchase.expire.at }}</view>
      <view class="border_buttom pl-28 mt-30"></view>
      <view class="f fac mb-30 mt-30">
        <view class="dian"></view>
        <view class="title pl-20">不可用日期</view>
      </view>
      <!-- unavailable_date weekly每周不可用 holidays节假日 specified指定日期 -->
      <!-- <view class="pl-28 good-name">周六，周日不可用</view> -->
      <view class="pl-28 good-name f fac" style="flex-wrap: wrap;" >
        <!-- 每周不可用 -->
        <view class="f fac" style="flex-wrap: wrap;"   v-if="purchase.unavailable_date.weekly.selected === 1">
          <template v-for="(item,index) in purchase.unavailable_date.weekly.weekdays">
            <view :key="item">
              <view v-if="index + 1 === purchase.unavailable_date.weekly.weekdays.length">
                {{ item | weekdays }}
              </view>
              <view v-else>{{ item | weekdays }}，</view>
            </view>
          </template>不可用，
        </view>
        <!-- 节假日 -->
        <view v-if="purchase.unavailable_date.holidays.selected === 1">节假日不可用，</view>
        <!-- 指定日期 -->
        <view v-if="purchase.unavailable_date.specified.selected === 1">
          <template v-for="(item, index) in purchase.unavailable_date.specified.dates">
            <view :key="index">{{ item.start_date }} - {{ item.end_date }}，</view>
          </template>
        </view>
      </view>
      <view class="border_buttom pl-28 mt-30"></view>
      <view class="f fac mb-30 mt-30">
        <view class="dian"></view>
        <view class="title pl-20">可用时间</view>
      </view>
      <!-- purchase.consume.type 1：全天可用 2：指定时间可用 -->
      <view class="pl-28 good-name" v-if="purchase.consume.type === 1">全天可用</view>
      <view class="pl-28 good-name f fac" style="flex-wrap: wrap;" v-else>
        <template v-for="(item, index) in purchase.consume.times">
          <view :key="index">{{ item.start_time }} - {{ item.end_time }}，</view>
        </template>
      </view>
      <view class="border_buttom pl-28 mt-30"></view>
      <view class="f fac mb-30 mt-30">
        <view class="dian"></view>
        <view class="title pl-20">购买限制</view>
      </view>
      <!-- 1限制 limited -->
      <view class="pl-28 good-name" v-if="purchase.buy_limit_rule.limited === 1">每人最多买{{ purchase.buy_limit_rule.num
        }}份
      </view>
      <view class="pl-28 good-name" v-else>不限制</view>
      <view class="border_buttom pl-28 mt-30"></view>
      <view class="f fac mb-30 mt-30">
        <view class="dian"></view>
        <view class="title pl-20">预约规则</view>
      </view>
      <!-- 需要预约 1需要2不需要 -->
      <view class="pl-28 good-name" v-if="purchase.booking_rule.need_booking === 2">到店消费：无需预约，高峰期可能需要排队</view>
      <view class="pl-28 good-name" v-else>到店消费：提前{{ purchase.booking_rule.advance_num }}{{
        purchase.booking_rule.advance_unit | dateName }}</view>
      <view class="border_buttom pl-28 mt-30"></view>
      <view class="f fac mb-30 mt-30">
        <view class="dian"></view>
        <view class="title pl-20">使用规则</view>
      </view>
      <!-- 使用规则 1与店内优惠同享 2不与店内优惠同享 -->
      <view class="pl-28 good-name" v-if="purchase.with_discount === 1">与店内优惠同享</view>
      <view class="pl-28 good-name" v-else>不与店内优惠同享</view>
      <view class="border_buttom pl-28 mt-30"></view>
      <view class="f fac mb-30 mt-30">
        <view class="dian"></view>
        <view class="title pl-20">使用张数限制</view>
      </view>
      <!-- 1不限制2限制 -->
      <view class="pl-28 good-name" v-if="purchase.usage_sheet_rule.unlimited === 1">不限制张数</view>
      <view class="pl-28 good-name" v-else>限制{{ purchase.usage_sheet_rule.num }}张</view>
      <view class="border_buttom pl-28 mt-30"></view>
      <view class="f fac mb-30 mt-30">
        <view class="dian"></view>
        <view class="title pl-20">使用人数限制</view>
      </view>
      <!-- 1不限制2限制 -->
      <view class="pl-28 good-name" v-if="purchase.usage_people_rule.unlimited === 1">不限制人数</view>
      <view class="pl-28 good-name" v-else>限制{{ purchase.usage_people_rule.num }}人</view>
    </view>
    <view class="but-red">
      <u-button text="确认核销" @click="verificationsOrderCode" type="error" shape="circle"></u-button>
    </view>
    <u-modal ref="code" :show="show" title="提示" :content="'是否确认核销券码：' + code" :showCancelButton="true" @cancel="cancel"
      @confirm="confirm"></u-modal>
    <u-modal ref="text" :show="showCon" :content="content" @confirm="confirmCon"></u-modal>
  </view>
</template>

<script>
export default {
  name: 'verification',

  data() {
    return {
      showCon: false,
      content: '',
      show: false,
      scanCodeData: {},
      purchase: {
        expire: {},
        unavailable_date: {
          weekly: {},
          holidays: {},
          specified: {}
        },
        consume: {},
        buy_limit_rule: {},
        booking_rule: {},
        usage_sheet_rule: {},
        usage_people_rule: {}
      }, // 购买须知
      packages: [], // 套餐
      code: '',
      goodlist: [
        { name: '炭烤黑牛拼盘' },
        { name: '炭烤黑牛拼盘' },
        { name: '炭烤黑牛拼盘' },
      ]
    }
  },
  filters: {
    dateName(level) {
      let str = ''
      switch (level) {
        case 1:
          str = '天'
          break
        case 2:
          str = '小时'
          break
        case 3:
          str = '分钟'
          break
      }
      return str
    },
    weekdays(date) {
      let str = ''
      // 不可用日期 周日Sunday 周一Monday 周二Tuesday 周三Wednesday 周四Thursday 周五Friday 周六Saturday
      switch (date) {
        case 'Sunday':
          str = '周日'
          break
        case 'Monday':
          str = '周一'
          break
        case 'Tuesday':
          str = '周二'
          break
        case 'Wednesday':
          str = '周三'
          break
        case 'Thursday':
          str = '周四'
          break
        case 'Friday':
          str = '周五'
          break
        case 'Saturday':
          str = '周六'
          break
      }
      return str
    }
  },
  onLoad(e) {
    if (e) {
      this.code = e.code
      this.store_id = e.store_id
    }
  },
  mounted() {
    this.getScanCodeVerificationDetails()
  },

  methods: {
    async getScanCodeVerificationDetails() {
      let params = {
        code: this.code,
        store_id: this.store_id
      }
      let res = await this.get('/api/localLife/front/getScanCodeVerificationDetails', params);
      if (res.code === 0) {
        console.log(res.data.data);
        this.scanCodeData = res.data.data
        this.packages = res.data.data.local_life_order_product_package.packages
        this.purchase = res.data.data.local_life_order_purchase
      }
    },
    // 核销
    async verificationsOrderCode() {
      this.show = true
    },
    // 取消
    cancel() {
      this.show = false
    },
    // 确认
    async confirm() {
      let params = {
        code: this.code,
        store_id: parseInt(this.store_id)
      }
      let res = await this.get("/api/localLife/front/VerificationsOrderCode", params, true, true, false);
      this.content = res.msg;
      this.showCon = true;
      this.cancel();
    },
    // 消息提示确认
    confirmCon() {
      this.showCon = false;
    }
  },
}
</script>
<style scoped lang="scss">
.box {
  width: 702rpx;
  padding: 25rpx;
  margin: 0 auto 20rpx;
  border-radius: 16rpx;
  background-color: #FFFFFF;
  box-sizing: border-box;
}

.mb-30 {
  margin-bottom: 30rpx;
}

.pl-28 {
  padding-left: 28rpx;
}

.title {
  color: #00001c;
  font-size: 28rpx;
  font-weight: bold;
}

.title-box {
  font-size: 30rpx;
  font-weight: bold;
  color: #00001C;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.good-name {
  font-size: 28rpx;
  color: #00001C;
  font-weight: 400;
}

.good-amount {
  font-size: 28rpx;
  color: #999999;
  font-weight: 400;
}

.yuan {
  font-size: 28rpx;
  font-weight: bold;
}

.amount {
  font-size: 32rpx;
  font-weight: bold;
}

.number {
  color: #999999;
  font-size: 26rpx;
  font-weight: 400;
}

.dian {
  width: 8rpx;
  height: 8rpx;
  border-radius: 4rpx;
  background-color: #D2D2D2;
}

.border_buttom {
  width: calc(100% - 28rpx);
  height: 1rpx;
  margin-left: 28rpx;
  background-color: #F0F0F1;
  box-sizing: border-box;
  margin-bottom: 30rpx;
}

.hint {
  font-size: 24rpx;
  color: #999999;
  margin-top: 10rpx;
}

.but-red {
  display: flex;
  justify-content: center;
  align-items: center;
  padding:0 24rpx;
  box-sizing: border-box;
  width: 100%;
  height: 120rpx;
  background-color: #FFFFFF;
  margin-top: 20rpx;
  position: fixed;
  bottom: 0rpx;
  z-index: 2;
}
</style>