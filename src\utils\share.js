// #ifdef H5
// const weixinSDK = require('../../node_modules/jweixin-module/lib/index.js');
// #endif
module.exports = {
  onShow() {
    let pages = getCurrentPages()
    if (pages[pages.length - 1].route.includes('packageD/smallShop')) {
      uni.hideShareMenu()
      return
    }
    if (pages.length >= 1) {
      this.$x.pageRouter = pages[pages.length - 1].route
      if (this.$x.pageRouter) {
        this.$x.mpShare = {
          title: '', // 分享标题
          path: '', // 默认为当前页面路径
          imageUrl: '', // 默认为当前页面的截图
        }
      }
      return
    }
  },
  // 分享到好友
  async onShareAppMessage(el) {
    let pages = getCurrentPages();
    let str = pages[pages.length - 1].$page.fullPath
    // 用户信息
    if (uni.getStorageSync('user')) {
      let pid = parseInt(uni.getStorageSync('user').id)
      if (str.split('?').length > 1) {
        this.$x.mpShare.path = str + `&invite_code=${pid}`
      } else {
        this.$x.mpShare.path = str + `?invite_code=${pid}`
      }
    }
    return this.$x.mpShare
  },
  // 分享到朋友圈
  onShareTimeline() {
    let pages = getCurrentPages();
    let str = pages[pages.length - 1].$page.fullPath
    // 用户信息
    if (uni.getStorageSync('user')) {
      let pid = parseInt(uni.getStorageSync('user').id)
      if (str.split('?').length > 1) {
        this.$x.mpShare.path = str + `&invite_code=${pid}`
      } else {
        this.$x.mpShare.path = str + `?invite_code=${pid}`
      }
      this.$x.mpShare.query = `invite_code=${pid}`
    }
    return this.$x.mpShare
  },
  methods: {
    /**
     * 微信浏览器分享
     * @param {*} title 标题
     * @param {*} desc 描述
     * @param {*} imgUrl 图片路径
     * @param {*} linkObj 参数 传递 [{linkName: 参数名,linkNum:参数}] 如果路由中以有参数linName 则会替换参数值
     */
    setWeixinShare(title = '', desc = '', imgUrl = '', linkObj = []) {
      const _this = this
      if (_this.isWeixin()) {
        _this.get('/api/wechatofficial/isOpenWechat').then(wxRes => {
          if (wxRes.code === 0 && wxRes.data.is_open === 1) {
            const url = window.location.href.split('#')[0]
            // const url = window.location.href;
            _this
              .get('/api/wechatofficial/getJsConfig', {
                url,
              })
              .then(res => {
                if (res.code === 0) {
                  const data = res.data
                  title = title || uni.getStorageSync('tabTitle')
                  desc = desc || uni.getStorageSync('tabTitle')
                  imgUrl = imgUrl || uni.getStorageSync('h5_logo')
                  let linkUrl = window.location.href; // 分享地址
                  // 用户信息 h5链接分享判断是否带参数
                  if (uni.getStorageSync('user')) {
                    let pid = parseInt(uni.getStorageSync('user').id)
                    if (window.location.href.split('?').length > 2) {
                      linkStr = `&invite_code=${pid}`
                    } else {
                      linkStr = `?invite_code=${pid}`
                    }
                  }
                  // 只要linkNum值不为空，则生成参数字符串
                  if (linkObj.length > 0) {
                    let link = window.location.href.split('?')
                    let linkSplit = link[link.length - 1].split("&")
                    let obj = [] // 把路由上的参数遍历成数组
                    for (let index = 0; index < linkSplit.length; index++) {
                      let str = linkSplit[index].split('=')
                      obj.push({
                        linkName: str[0],
                        linkNum: str[1]
                      })
                    }
                    // 遍历传递的参数数组
                    for (let index = 0; index < linkObj.length; index++) {
                      let parameter = linkObj[index].linkName;
                      let linkNum = linkObj[index].linkNum;
                      // 查找新添加的参数是否存在 存在则替换，不存在则拼接
                      let objIndex = obj.findIndex(item => item.linkName === parameter)
                      if (objIndex > -1) {
                        let win
                        if (objIndex == 0) {
                          win = window.location.href.split( '?' + obj[objIndex].linkName + '=' + obj[objIndex].linkNum);
                          linkUrl = win[0] + '?' + obj[objIndex].linkName + '=' + linkNum + win[1] // 重构分享地址
                        } else {
                          win = window.location.href.split( '&' + obj[objIndex].linkName + '=' + obj[objIndex].linkNum);
                          linkUrl = win[0] + '&' + obj[objIndex].linkName + '=' + linkNum + win[1] // 重构分享地址
                        }
                      } else {
                        linkStr += linkStr ? `&${parameter}=${linkNum}` : `?${parameter}=${linkNum}`
                      }
                    }
                  }
                  linkUrl = linkUrl + linkStr;
                  jweixin.config({
                    debug: false,
                    appId: data.app_id,
                    timestamp: data.timestamp,
                    nonceStr: data.nonce_str,
                    signature: data.signature,
                    jsApiList: [
                      'updateAppMessageShareData',
                      'updateTimelineShareData',
                    ],
                  })

                  jweixin.ready(() => {
                    // 分享给朋友
                    jWeixin.updateAppMessageShareData({
                      title,
                      desc,
                      imgUrl,
                      link: linkUrl,
                      success: () => {
                        console.log('布置成功');
                      },
                    }),
                      // 分享至朋友圈
                      jweixin.updateTimelineShareData({
                        title,
                        desc,
                        imgUrl,
                        link: linkUrl,
                        success: () => {
                          console.log('布置成功')
                        },
                      })
                  })
                }
              })
          }
        })
      }
    },
    // 判断是否是微信浏览器，只在微信浏览器调用分享配置
    isWeixin() {
      const ua = navigator.userAgent.toLowerCase()
      if (ua.match(/MicroMessenger/i) == 'micromessenger') {
        return true
      } else {
        return false
      }
    },
  },
}
