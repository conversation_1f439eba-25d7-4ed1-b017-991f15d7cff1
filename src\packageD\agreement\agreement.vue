<template>
    <view>
        <rich-text :nodes="richTextHtml" />
    </view>
</template>
<script>
import { repairRichText } from '@/utils/fun'
export default {
    data() {
        return {
            type: '',
            title: '协议',
            richTextHtml: '',
        }
    },
    onLoad(options) {
        // 根据传入的options类型设置页面标题
        if (options.type) {
            this.type = options.type
            switch (options.type) {
                case 'signIn':
                    this.title = '注册协议'
                    break
                case 'user':
                    this.title = '用户服务协议'
                    break
                case 'privacy':
                    this.title = '隐私协议'
                    break
            }
        }
        uni.setNavigationBarTitle({
            title: this.title,
        })
        this.findSettingAccord()
    },
    methods: {
        async findSettingAccord() {
            const { data } = await this.get(
                '/api/user/findSettingAccord',
                {},
                true,
            )
            switch (this.type) {
                case 'signIn':
                    this.richTextHtml = repairRichText(data.agreement)
                    break
                case 'user':
                    this.richTextHtml = repairRichText(data.service_agreement)
                    break
                case 'privacy':
                    this.richTextHtml = repairRichText(data.privacy_policy)
                    break
            }
        },
    },
}
</script>
<style lang="scss" scoped></style>
