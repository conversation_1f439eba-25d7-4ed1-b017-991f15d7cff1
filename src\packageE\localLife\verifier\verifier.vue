<template>
    <view>
        <view class="container">
          <!-- 信息卡 -->
          <view class="back_drop">
            <!-- 信息 -->
            <view class="message f d-bf">
              <view class="f fac">
                <view class="ml_35 pt_30">
                  <u-image
                    width="110"
                    height="110"
                    shape="circle"
                    :src="store_data.user.avatar"
                  ></u-image>
                </view>
                <view class="pt_30 ml_15">
                  <view class="fontSize32 white">{{ store_data.store.name }}</view>
                  <view class="f fac mt_5">
                    <view class="iconfont icon-home-line6 white fontSize26"></view>
                    <view class="ml_10 white fontSize26">核销员</view>
                  </view>
                </view>
              </view>
              <view class="change-btn mt_30 mr_35 f d-cf d-c" @click="changeStore" v-if="verificationStoreCount > 1">
                <view class="white fontSize26">切换</view>
                <view class="iconfont icon-xia white"></view>
              </view>
            </view>
            <!-- 门店操作 -->
            <view class="store_operate">
              <u-grid :border="false" col="3">
                <u-grid-item @click="scanCode">
                  <view class="iconfont icon-icon_scan iconf-img"></view>
                  <text class="grid-text fs-1 mt_10">扫码验销</text>
                </u-grid-item>
                <u-grid-item @click="verifyNews">
                  <view class="iconfont icon-shumayanquan iconf-img"></view>
                  <text class="grid-text fs-1 mt_10">输码验销</text>
                </u-grid-item>
                <u-grid-item
                  @click="
                    navTo(
                      `/packageE/localLife/verifierDetail/verifierDetail?store_id=${store_id}`,
                    )
                  "
                >
                  <view class="iconfont icon-hexiaomingxi iconf-img"></view>
                  <text class="grid-text fs-1 mt_10">核销明细</text>
                </u-grid-item>
              </u-grid>
            </view>
          </view>
        </view>
        <store-popup
            ref="storePopup"
            @change-store-other="changeStoreOther"
        ></store-popup>
        <u-popup :round="30" :show="newsShow" @close="close">
          <view class="code-box">
            <view class="f fjsb fac verifyNews-title">
              <view class="title">输码验券</view>
              <view class="iconfont icon-close11" @click="close"></view>
            </view>
            <view class="con-verifyNews">
              <view>
                <u-input
                  placeholder="请输入券码"
                  border="surround"
                  fontSize="28rpx"
                  customStyle="height: 104rpx;"
                  v-model="value"
                ></u-input>
              </view>
              <view style="margin-top: 108rpx;">
                <u-button
                  customStyle="height: 80rpx;font-size:30rpx"
                  type="error"
                  size="small"
                  shape="circle"
                  text="确认"
                  @click="onVerifyNews"
                ></u-button>
              </view>
            </view>
          </view>
        </u-popup>
    </view>
</template>
<script>
import storePopup from './components/storePopup.vue'

export default {
  name: 'verifier',
  components: { storePopup },
  data() {
    return {
        store_id: 0,// 核销门店ID
        store_data: {
            user: {},
            store: {}
        }, // 门店信息
        newsShow: false, // 输码验消
        value: '', // 券码
        verificationStoreCount: null, // 核销员门店数量
    }
  },
  mounted() {
    this.getLocalLifeUse()
  },
  onLoad() {
    // 只能在wx浏览器上使用
    if (this.checkWenxin()) {
      const url = window.location.href.split('#')[0]
      this.get('/api/wechatofficial/getJsConfig', {
        url,
      }).then(res => {
        const data = res.data
        jweixin.config({
          debug: false,
          appId: data.app_id,
          timestamp: data.timestamp,
          nonceStr: data.nonce_str,
          signature: data.signature,
          jsApiList: ['scanQRCode'],
        })
      })
    }
  },
  methods: {
    // 切换门店按钮
    changeStore() {
      this.$refs.storePopup.storeShow = true
      this.$refs.storePopup.getLocalLifeUseStores(this.store_id)
    },
    // 获取门店数据
    async getLocalLifeUse() {
      const res = await this.get('/api/localLife/front/getLocalLifeUse')
      if (res.code === 0) {
        this.store_id = res.data.verification_store.store.id
        this.store_data = res.data.verification_store
        this.verificationStoreCount = res.data.verificationStoreCount
      }
    },
    // 切换其他门店
    async changeStoreOther(storeId, index) {
        const res = await this.get('/api/localLife/front/getVerificationsByUserId')
        if (res.code === 0) {
            this.store_data = res.data.find(item => item.store.id === storeId)
            this.store_id = storeId
        }
    },
    // 微信H5扫码
    initScanQRCode() {
      let that = this
      jweixin.ready(() => {
        jweixin.scanQRCode({
          needResult: 1,
          scanType: ['qrCode'],
          success: res => {
            console.log(res)
            let str = res.resultStr.split('=')
            if (str[0] === 'code') {
              that.navTo(
                `/packageE/localLife/verification/verification?code=${str[1]}&store_id=${that.store_id}`,
              )
            } else {
              that.toast('无效二维码')
            }
          },
          fail: res => {
            that.toast('扫码失败')
          },
        })
      })
    },
    // 扫码配置
    scanCode() {
      // #ifdef MP-WEIXIN
      let that = this
      uni.scanCode({
        scanType: ['qrCode'],
        success: function (res) {
          let str = res.result.split('=')
          if (str[0] === 'code') {
            that.navTo(
              `/packageE/localLife/verification/verification?code=${str[1]}&store_id=${that.store_id}`,
            )
          } else {
            that.toast('无效二维码')
          }
        },
        fail: res => {
          that.toast('扫码失败')
        },
      })
      // #endif

      // 微信h5可否使用
      if (this.checkWenxin()) {
        this.initScanQRCode()
      }
    },
    // 输码验销
    verifyNews() {
      this.newsShow = true
    },
    // 关闭核销验码
    close() {
      this.newsShow = false
      this.value = ''
    },
    // 确认核销验码
    async onVerifyNews() {
      let params = {
        store_id: parseInt(this.store_id),
        code: this.value,
      }
      let res = await this.get(
        '/api/localLife/front/VerificationsOrderCode',
        params,
      )
      if (res.code === 0) {
        this.$refs.uToast.show({
          message: '核销成功',
          duration: 4000,
        })
        this.newsShow = false
      }
    },
  }
}
</script>
<style lang="scss" scoped>
.container {
  width: 100vw;
  height: 100vh;
  background-color: #f5f5f5;
  .back_drop {
    width: 750rpx;
    height: 272rpx;
    background: linear-gradient(180deg, #f15353 0%, #f5f5f5 100%);
    .message {
      .change-btn {
        width: 130rpx;
        height: 56rpx;
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 33rpx;
      }
    }
    .store_operate {
      margin-top: 40rpx;
      margin-left: 24rpx;
      margin-right: 24rpx;
      width: 702rpx;
      // height: 319rpx;
      padding-bottom: 40rpx;
      background-color: #ffffff;
      border-radius: 16rpx;
    }
  }
  .data_count {
    width: 702rpx;
    height: 447rpx;
    background-color: #ffffff;
    margin-top: 245rpx;
    border-radius: 16rpx;
    margin-left: 24rpx;
    margin-right: 24rpx;
    .count-title {
      .date-time {
        margin-right: 24rpx;
        .today {
          width: 100rpx;
          height: 56rpx;
          border-radius: 12rpx 0 0 12rpx;
          text-align: center;
          line-height: 56rpx;
          color: #f15353;
          border: 1px solid #f15353;
        }
        .yesterday {
          width: 100rpx;
          height: 56rpx;
          text-align: center;
          line-height: 56rpx;
          color: #f15353;
          border-top: 1px solid #f15353;
          border-bottom: 1px solid #f15353;
        }
        .all {
          width: 100rpx;
          height: 56rpx;
          border-radius: 0rpx 12rpx 12rpx 0rpx;
          text-align: center;
          line-height: 56rpx;
          color: #f15353;
          border: 1px solid #f15353;
        }
      }
    }
    .data_number {
      margin-top: 44rpx;
    }
  }
}
.fontSize32 {
  font-size: 32rpx;
}
.fontSize26 {
  font-size: 26rpx;
}
.white {
  color: #ffffff;
}
.iconf-img {
  margin: 34rpx 0 0 0;
  width: 56rpx;
  height: 56rpx;
  font-size: 50rpx;
  color: #00001c;
  display: block;
}

.con-verifyNews {
  padding: 0rpx 36rpx 30rpx 36rpx;
  // height: 200rpx;
}
.verifyNews-title {
  padding: 0 36rpx;
  height: 117rpx;
  .title {
    font-size: 32rpx;
    color: #00001c;
    font-weight: bold;
  }
}

</style>