<!-- 横向商品展示组件 -->
<template>
	<view class="mb-40" style="overflow:hidden">
		<!-- 横向展示样式一 -->
		<view v-for="(item,index) in list" :key="index" class="d-cc pt-15 pl-10 pr-10" style="width: 720rpx;">
			<view style="width: 5%" v-if="collectionShows">
				<checkbox :value="String(index)" :checked="check[index].check"
					style="transform: scale(0.7);" color="#f14e4e" @click="collectionOfSelected(index)" />
			</view>
			<view class="bg-white p-20 ml_16 swhb radius10" style="width: 100%;"
				@click="goCommodity_details(item.product.id)">
				<view class="d-f">
					<view>
						<u--image :showLoading="true" :src="item.product.thumb" width="150rpx" height="150rpx"
							radius="10">
						</u--image>
					</view>
					<view class="ml-20">
						<view style="width: 470rpx" class="fs-2 ell">
							{{item.product.title}}
						</view>
						<view v-if="item.product.is_display!=0">
							<view class="d-bf mt_17 fs-1-5">
								<view class="c-7e7e7e fs-1"><text>库存:</text><text
										class="ml-5">{{item.product.stock}}</text>
								</view>
							</view>
							<view class="fw-b c-f14e4e mt-10 fs-1">
								<text>利润:</text>
								<text v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.product.id)">¥{{toYuan(item.product.level_profit)}}</text>
								<text v-else-if="userLevelPriceTitle">¥{{userLevelPriceTitle}}</text>
								<text v-else>¥{{toYuan(item.product.level_profit)}}</text>
							</view>
						</view>
						<view v-else style="background-color: #F0F0F0;width:100rpx;"
							class="c-888 fs-0 d-cc radius30 pt_5 pb_5 mt-20">已失效</view>
					</view>
				</view>
				<view class="d-bf mt-25 pr-20 fs-1" v-if="item.product.is_display!=0">
					<view class="d-cc-c">
						<view class="c-f14e4e" v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.product.id)">¥{{toYuan(item.product.level_price)}}</view>
						<view class="c-f14e4e" v-else>¥{{ userLevelPriceTitle ? userLevelPriceTitle : toYuan(item.product.level_price)}}</view>
						<view class="mt-15">超级批发价</view>
					</view>
					<view class="d-cc-c">
						<view class="c-f14e4e" v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.product.id)">¥{{item.product.price}}</view>
						<view class="c-f14e4e" v-else>¥{{ userLevelPriceTitle ? userLevelPriceTitle : item.product.price}}</view>
						<view class="mt-15">批发价</view>
					</view>
					<view class="d-cc-c">
						<view class="c-f14e4e" v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.product.id)">¥{{item.product.origin_price}}</view>
						<view class="c-f14e4e" v-else>¥{{ userLevelPriceTitle ? userLevelPriceTitle : item.product.origin_price}}</view>
						<view class="mt-15">建议零售价</view>
					</view>
					<view class="d-cc-c">
						<view class="c-f14e4e">{{item.product.sales}}</view>
						<view class="mt-15">已售</view>
					</view>
					<view class="iconfont icon-fontclass-gouwuche fs-2-5" style="color: #ff6c00;">
					</view>
				</view>
			</view>
		</view>
		<view class="fs-1 c-5e5e5e mb-25 mt-25">
			<view v-if="list.length != 0&&readMore" class="d-cc">暂无更多~</view>
			<view v-if="list.length == 0" class="d-cc">暂无数据~</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			list: { //商品信息
				type: Array
			},
		},
		data() {
			return {}
		},
		computed: {
			userLevelPriceTitle(){
				return this.$store.state.userLevelPriceTitle || ""
			},
			readMore() { //为了适配小程序端{{}}不能直接读取vuex值的问题
				return this.$store.state.homeNoMore
			},
			collectionShows() { //为了适配小程序端{{}}不能直接读取vuex值的问题
				return this.$store.state.collectionShows
			},
			
			check(){
				return this.$store.state.cancellationArray
			}

		},
		methods: {
			goCommodity_details(id) { //跳转到商品详情页面
				uni.navigateTo({
					url: '/packageA/commodity/commodity_details/commodity_details?id=' + id
				})
			},
			collectionOfSelected(e) { //复选框选中
				let arry = []
				if (this.list[e].check == true) {
					this.list[e].check = false
				} else {
					this.list[e].check = true
				}
				console.log(this.list)
				this.$store.commit("upCancellationArray", this.list)
			}
		}
	}
</script>

<style scoped>
/* 	::v-deep .uni-checkbox-input:hover {
		border: 1rpx slategray solid;
	} */
</style>
