<template>
    <view>
        <view class="head">
			<u-tabs 
				:list="tabList" 
				:scrollable="false"
				lineWidth="75rpx"
				lineHeight='7'
				lineColor="#f56c6c"
				:inactiveStyle="{
				 	color: '#2f2f2f',
				}"
                :current='tabsType'
				@click="tabsTap"
			>
			</u-tabs>
            <view class="f fac mb-15 pt-20 pl-25 pr-30">
                <view class="head_select">
                    <uni-data-select
                        v-model="value"
                        :localdata="liveOptions"
                        :clear="false"
                        @change="changeSelect"
                    ></uni-data-select>
                </view>
                    <u-search
                        height="60"
                        searchIconSize="40"
                        color="#666666"
                        bgColor="#eff0f1"
                        v-model="title"
                        placeholder="搜索会员ID/昵称/手机号"
                        :showAction="false"
                        @change="search"
                    ></u-search>
                <view
                    v-if="tabsType === 0"
                    class="ml-25 d-cc-c"
                    @click="getSetting"
                    show = true
                >
                    <view class="iconfont icon-shezhi2 font_size24"></view>
                    <view class="ml-5 fs-1 mt_10">积分设置</view>
                </view>
            </view>
            <!-- 明细tabs -->
            <u-tabs 
                v-if="tabsType === 1"
				:list="tabList2" 
				:scrollable="false"
				lineWidth="35rpx"
				lineHeight='7'
				lineColor="#f56c6c"
				:inactiveStyle="{
				 	color: '#2f2f2f',
				}"
				@click="tabsDetailTap"
			>
			</u-tabs>
            <view v-else class="pt_10"></view>
		</view>
        <!-- 会员列表 -->
        <view class="con-list" v-if="tabsType === 0">
            <template v-for="item in smallShopMemberList">
                <view class="b-r-16 mb_30 bg-white p-25" :key="item.id">
                    <view class="d-f">
                        <u-image
                            width="80rpx"
                            height="80rpx"
                            :showLoading="true"
                            :src="item.user_info.avatar"
                            radius="40"
                        >
                          <u-icon
                            slot="error"
                            size="40"
                            color="#d0d0d1"
                            name="photo"
                          ></u-icon>
                        </u-image>
                        <view class="f1 ml_15">
                            <view class="f fac">
                                <span class="member-name">{{ item.user_info.nick_name }}</span>
                                <view class="member-ID p-5-10 fs-1 ml-10 b-r-8">ID:{{ item.user_info.id }}</view>
                            </view>
                            <view class="member-phone mt_15" v-if='item.user_info.full_name || item.user_info.username'>
                                {{ item.user_info.full_name }}<span v-if="item.user_info.full_name && item.user_info.username">/</span>{{ item.user_info.username }}
                            </view>
                        </view>
                    </view>
                    <p class="f fac mt-30 fs-1-5 text-p" v-if="item.referrer_info.id">
                        <span>推荐人:</span>
                        <view class="member-ID p-5-10 fs-1 ml-15 b-r-8">ID:{{ item.referrer_info.id }}</view>
                        <span class="ml-15">{{ item.referrer_info.nick_name }}</span>
                    </p>
                    <p class="fs-1-5 text-p mt-20">积分: {{ toYuan(item.points) }}</p>
                    <p class="fs-1-5 text-p mt-20">注册时间: {{ formatDateTime(item.created_at)  }}</p>
                    <view class="w100 mt-30">
                        <u-row>
                            <u-col :span="4.6"></u-col>
                            <u-col :span="3.7">
                                <view style="width: 180rpx; height: 64rpx;">
                                    <u-button
                                        type="error"
                                        color="#f01125"
                                        size="small"
                                        shape="circle"
                                        text="充值积分"
                                        @click="addIntegral(item)"
                                    ></u-button>
                                </view>
                            </u-col>
                            <u-col :span="3.7">
                                <view style="width: 180rpx; height: 64rpx;">
                                    <u-button
                                        :plain='true'
                                        type="error"
                                        shape="circle"
                                        size="small"
                                        text="积分明细"
                                        @click="itemDetailList(item)"
                                    ></u-button>
                                </view>

                            </u-col>
                        </u-row>
                    </view>
                </view>
            </template>
            <view
                v-if="smallShopMemberList.length === total"
                class="d-cc fs-1 c-5e5e5e mb-25 mt-25"
            >
                暂无更多~
            </view>
        </view>
        <!-- 积分明细 -->
        <view class="con-list" v-else-if="tabsType === 1">
            <template v-for="item in detailList">
                <view class="b-r-16 mb_30 bg-white p-25" :key="item.id">
                    <view class="f fac fjsb">
                        <view>{{ item.business_type_name }}</view>
                        <view>{{ formatDateTime(item.created_at) }}</view>
                    </view>
                    <view class="f fac fjsb">
                        <view class="d-f fac mt_20">
                            <u-image
                                width="50rpx"
                                height="50rpx"
                                :showLoading="true"
                                :src="item.user_info.avatar"
                                radius="25"
                            >
                              <u-icon
                                slot="error"
                                size="40"
                                color="#d0d0d1"
                                name="photo"
                              ></u-icon>
                            </u-image>
                            <span class="member-name ml_15">{{ item.user_info.nick_name }}({{ item.user_info.id }})</span>
                        </view>
                        <view>{{ item.change_type === 1 ? '' : '-'   }}{{ toYuan(item.change_points) }}</view>
                    </view>
                    <view class="f fac mt_25">
                        变动后积分: {{  toYuan(item.balance) }}
                    </view>
                    <view class="f fac mt_25 fs-1-5 color90">
                        备注: {{ item.remark }}
                    </view>
                </view>
            </template>
            <view
                v-if="detailList.length === total"
                class="d-cc fs-1 c-5e5e5e mb-25 mt-25"
            >
                暂无更多~
            </view>
        </view>
        <!-- 积分设置 -->
        <u-popup :show="show" mode="bottom" :round="30" @close="onClosePopup">
            <view class="showPop">
                <view class="showSetting">
                    <text @click="onClosePopup">取消</text>
                    <text>积分设置</text>
                    <text @click="updateSetting">确定</text>
                </view>
                <view class="d-bf mt_70">
                    <view>购物赠送</view>
                    <view class="f fac">
                        <!-- <input
                            placeholder="请输入比例"
                            placeholder-class="f12"
                            v-model="formData.give_ratio"
                            class="input-css"
                        /> -->
                        <view class="w250">
                            <u-input v-model="formData.give_ratio" placeholder="请输入" fontSize="26" />
                        </view>
                        <span class="ml_30">%</span>
                    </view>
                </view>
                <p class="color90 fs-1 mt-10">设置为0或者为空，则不赠送！</p>
                <view class="d-bf mt_50">
                    <view>购物折扣</view>
                    <view class="f fac">
                        <!-- <input
                            placeholder="请输入比例"
                            placeholder-class="f12"
                            v-model="formData.deduction_ratio"
                            class="input-css"
                        /> -->
                        <view class="w250">
                            <u-input v-model="formData.deduction_ratio" placeholder="请输入" fontSize="26" />
                        </view>
                        <span class="ml_30">%</span>
                    </view>
                </view>
                <p class="color90 fs-1 mt-10">设置为0或者为空，则不支持折扣！</p>
                <view class="showSwitch mt_40">
                    <text>运费折扣</text>
                    <u-switch
                        v-model="formData.delivery_deduction_switch"
                        size="38"
                        activeColor="#f56c6c"
                        inactiveColor="#e6e6e6"
                    ></u-switch>
                </view>
                <p class="color90 fs-1 mt-10">开启后支持积分折扣运费！</p>
                <view class="d-bf mt_50">
                    <view>积分价值</view>
                    <view class="f fac">
                        <span>1元=</span>
                        <!-- <input
                            placeholder="请输入"
                            placeholder-class="f12"
                            v-model="formData.point_value"
                            class="input-css"
                        /> -->
                        <view class="w250 ml_30">
                            <u-input v-model="formData.point_value" placeholder="请输入" fontSize="26" />
                        </view>
                        <span class="ml_30">积分</span>
                    </view>
                </view>
                <p class="fs-1 mt_40 color-red">积分细则：</p>
                <p class="fs-1 mt_10 color-red">使用积分折扣的，如果订单金额小于商品供货价+技术服务费+运费的，差额部分在店主余额中扣除，如果余额不足，则无法下单！</p>
                <p class="fs-1 mt_10 color-red mb_40">店主利润按实付金额计算！</p>
            </view>
        </u-popup>
        <!-- 积分充值 -->
        <u-popup :show="show2" mode="bottom" :round="30" @close="onAddClosePopup">
            <view class="showPop">
                <view class="showSetting">
                    <text @click="onAddClosePopup">取消</text>
                    <text>充值积分</text>
                    <text @click="pointRecharge">确定</text>
                </view>
                <view class="d-f fac mt_60">
                    <u-image
                        width="60rpx"
                        height="60rpx"
                        :showLoading="true"
                        :src="itemSmallShopMember.user_info.avatar"
                        radius="30"
                    >
                      <u-icon
                        slot="error"
                        size="40"
                        color="#d0d0d1"
                        name="photo"
                      ></u-icon>
                    </u-image>
                    <view class="f fac ml-15">
                        <span class="member-name">{{ itemSmallShopMember.user_info.nick_name }}</span>
                        <view class="member-ID p-5-10 fs-1 ml-10 b-r-8">ID:{{ itemSmallShopMember.user_info.id }}</view>
                    </view>
                </view>
                <p class="mt_50">当前积分 {{ toYuan(itemSmallShopMember.points) }}</p>
                <view class="d-bf mt_50">
                    <view class="f fac"><span class="color-red fs-5 pt_15">*</span>充值数量</view>
                    <view class="w400">
                        <u-input  v-model="addIntegralFrom.change_points" placeholder="请输入充值数量" fontSize="24" />
                    </view>
                </view>
                <p class="mt_50 mb_20">备注</p>
                <u--textarea
                    height="150rpx"
                    v-model="addIntegralFrom.remark"
                    maxlength="175"
                ></u--textarea>
                <view class="h50"></view>
            </view>
        </u-popup>
        <u-modal :show="showModal" :showCancelButton='true' @confirm='confirm' @cancel='showModal = false' class="modal">
			<view class="slot-content">
				<rich-text nodes="确认要充值吗？"></rich-text>
			</view>
		</u-modal>
    </view>
</template>
<script>
export default {
    data() {
        return {
            // tabs 切换
            tabList: [
                {
					name: '会员列表',
					type: 0,
				},
				{
					name: '积分明细',
					type: 1,
				}
            ],
            // tabs 明细切换
            tabList2: [
                {
					name: '全部',
					type: 1,
				},
				{
					name: '收入',
					type: 2,
				},
                {
					name: '支出',
					type: 3,
				}
            ],
            tabsType: 0,
            tabsType2: 1,
            // 搜索条件
            liveOptions: [
                { value: 0, text: '会员ID' },
                { value: 1, text: '昵称' },
                { value: 2, text: '手机号' },
            ], // 选择框
            value: 0,
            title: '',
            smallShopMemberList: [], // 会员列表
            detailList: [], // 明细列表
            // 积分设置
            show: false,
            formData: {},
            // 积分充值
            show2: false,
            addIntegralFrom: {},
            userId: null,
            itemSmallShopMember: {
                user_info: {}
            },

            // 确认框
            showModal: false,

            // 分页
            page: 1,
            pageSize: 10,
            total: 0
        }
    },
    onShow() {
        this.getUserList(); // 获取会员列表；
    },
    // 下拉刷新
    onReachBottom() {
        if (this.tabsType === 0) {
            if (this.smallShopMemberList.length < this.total) {
                this.page = this.page + 1;
                this.getUserList();
            }
        } else {
            if (this.detailList.length < this.total) {
                this.page = this.page + 1;
                this.getUserPointList();
            }
        };
    },
    methods: {
        changeSelect(e) {
            console.log('选择选项：', e)
        },
        // 搜索
        async search() {
            this.page = 1;
            if (this.tabsType === 0) {
                this.smallShopMemberList = [];
                this.getUserList();
            } else {
                this.detailList = [];
                this.getUserPointList();
            };
        },
        // 获取会员列表
        async getUserList() {
            let data = {
                page: this.page,
                pageSize: this.pageSize,
            };
            if (this.title !== '') {
                switch (this.value) {
                    case 0:
                        data.uid = parseInt(this.title);
                        break;
                    case 1:
                        data.nick_name = this.title;
                        break;
                    case 2:
                        data.mobile = this.title;
                        break;
                    default:
                        break;
                }
            }
            let res = await this.get('/api/smallShop/user/list',data);
            if (res.code === 0) {
                this.total = res.data.total;
                if (this.page === 1) {
                    this.smallShopMemberList = [];
                }
                let list = res.data.list ? res.data.list : [];
                this.smallShopMemberList = [...this.smallShopMemberList, ...list];
            };
        },
        // 获取积分明细
        async getUserPointList() {
            let data = {
                page: this.page,
                pageSize: this.pageSize,
            };
            if (this.title !== '') {
                switch (this.value) {
                    case 0:
                        data.uid = parseInt(this.title);
                        break;
                    case 1:
                        data.nickname = this.title;
                        break;
                    case 2:
                        data.mobile = this.title;
                        break;
                    default:
                        break;
                }
            }
            if (this.tabsType2 === 2) {
                data.change_type = 1;  // 1 收入,
            } else if (this.tabsType2 === 3) {
                data.change_type = 2;  // 2 支出, 
            }
            let res = await this.get('/api/smallShop/user/point/list',data);
            if (res.code === 0) {
                if (this.page === 1) {
                    this.detailList = [];
                }
                let list = res.data.list ? res.data.list : [];
                this.detailList = [...this.detailList,...list];
                this.total = res.data.total;
            }
        },
        // tab  切换
        tabsTap(item) {
            this.tabsType = item.type;
            this.tabsType2 = 1;
            this.title = '';
            this.page = 1;
            if (this.tabsType === 0) {
                this.smallShopMemberList = [];
                this.getUserList();
            } else {
                this.detailList = [];
                this.getUserPointList();
            };
        },
        // 明细积分 tabs 切换
        tabsDetailTap(item) {
            this.tabsType2 = item.type;
            this.paeg = 1;
            this.detailList = [];
            this.getUserPointList();
        },
        // 获取积分设置
        async getSetting() {
            this.show = true;
            let res = await this.get('/api/smallShop/user/point/getSetting',{});
            if (res.code === 0) {
                this.formData = res.data;
                this.formData.point_value = this.formData.point_value / 100;
                this.formData.delivery_deduction_switch = this.formData.delivery_deduction_switch === 1? true : false; 
            }
        },
        // 保存 积分设置 updateSetting
        async updateSetting() {
            let params = {
                give_ratio: parseInt(this.formData.give_ratio),
                deduction_ratio: parseInt(this.formData.deduction_ratio),
                delivery_deduction_switch: this.formData.delivery_deduction_switch ? 1 : 0,
                point_value: parseInt(this.formData.point_value*100)
            };
            let res = await this.put('/api/smallShop/user/point/updateSetting',params);
            if (res.code === 0) {
                this.onClosePopup();
                this.toast('设置成功！')
            }
        },
        // 关闭积分设置
        onClosePopup() {
            this.formData = {
                give_ratio: 0, // 购物赠送
                deduction_ratio: 0, // 购物赠送
                delivery_deduction_switch: false, // 运费折扣
                point_value: 0, // 积分价值
            };
            this.show = false;
        },
        // 打开 充值积分 弹窗
        addIntegral(item) {
            this.show2 = true;
            this.userId = item.user_info.id;
            this.itemSmallShopMember = item;
            console.log(item);
            
        },
        // 充值积分
        pointRecharge() {
            this.showModal = true;
        },
        // 确认充值积分
        async confirm() {
            this.showModal = false;
            let data = {
                change_type: 1,
                change_points: parseInt(this.addIntegralFrom.change_points * 100),
                uid: this.userId,
                remark: this.addIntegralFrom.remark
            };
            let res = await this.post('/api/smallShop/user/point/recharge',data);
            if (res.code === 0) {
                
                this.toast('充值成功！');
                this.page = 1;
                this.smallShopMemberList = [];
                this.getUserList();
                this.onAddClosePopup()
            }
        },
        // 关闭积分充值
        onAddClosePopup() {
            this.show2 = false;
            this.addIntegralFrom = {}
            this.userId = null;
        },
        // 用户 积分明细
        itemDetailList(item) {
            // 切换tabs
            this.tabsType = 1;
            // 搜索条件 赋值
            this.title = '' + item.user_info.id;
            this.value = 0;
        },
    }
}
</script>
<style lang="scss" scoped>
.head {
    background-color: #fff;
}

.h50 {
    height: 50rpx;
}

.w100 {
    width: 100%;
}

.w250 {
    width: 250rpx;
}

.w400 {
    width: 400rpx;
}

.font_size24 {
    font-size: 36rpx;
}

.b-r-16  {
  border-radius: 16rpx;
}

.pl-40 {
    padding-left: 40rpx;
}

.p-5-10 {
    padding: 10rpx 16rpx;
}

.jce {
    justify-content: end;
}

.b-r-8 {
    border-radius: 8rpx;
}

.con-list {
  padding: 20rpx 25rpx;
}

.member-name{
    color: #0f0f0f;
    font-size: 26rpx;
}

.member-ID {
    background-color: #f01125;
    color: white;
}

.member-phone{
    color: #7b7b7b;
    font-size: 24rpx;
}

.text-p {
    color: #7b7b7b;
}

.color90 {
    color: #c1c1c1;
}

::v-deep uni-button {
    margin: 0 !important;
    padding: 0 !important;
    margin-left: 20rpx !important;
}

.showPop {
  padding-left: 30rpx;
  padding-top: 30rpx;
  padding-right: 30rpx;
  color: #101010;
  font-weight: 500;

  .showSetting {
    display: flex;
    justify-content: space-between;
    font-size: 28rpx;
  }
}

.showSwitch {
    display: flex;
    justify-content: space-between;
}

.input-css {
    // text-align: right;
    font-size: 28rpx;
}

.color-red{
    color: #f01125;
}

.head_select {
  margin-left: 6rpx;
  margin-right: 18rpx;
}

::v-deep .u-tabs__wrapper__nav__item__text {
    font-size: 26rpx;
}

::v-deep .u-tabs__wrapper__nav__line {
    top: 35px !important;
}

::v-deep .u-tabs__wrapper__nav__item{
    height: 39px !important;
}

::v-deep .uni-stat__select {
  padding: 0;
}

::v-deep .uni-select {
  border: none;
}
::v-deep .uni-select__input-box {
  padding: 0;
  width: 120rpx;
}

::v-deep .modal .u-transition {
    z-index: 10076 !important;
}

::v-deep .u-input {
  height: 40rpx;
}
</style>
