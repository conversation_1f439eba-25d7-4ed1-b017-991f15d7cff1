<template>
  <view class="p-20">
    <view class="bg-white p-20 top-view">
      <scroll-view class="scroll-view_H mb_20" scroll-x="true" scroll-left="0">
        <view class="f classify-view">
          <view
            class="classify-item"
            :class="classifyCheck === item.id ? 'active' : ''"
            v-for="item in classifyList"
            :key="item.id"
            @click="handleClassifyClick(item.id)"
          >
            {{ item.name }}
          </view>
        </view>
      </scroll-view>
      <sortBy
        v-if="classifyCheck !== 'supply'"
        :items="sortOptions"
        :sortBy="sortBy"
        :sortOrder="sortOrder"
        @sort-change="handleSortChange"
      />
    </view>
    <!-- <scroll-view class="scroll-view_Y" scroll-y="true" scroll-left="0" @scrolltolower="glideFn"> -->
    <view v-for="item in list" :key="item" class="sup-view bg-white p-20">
      <view class="f" @click="jumpA(item)">
        <u--image
          :showLoading="true"
          width="120rpx"
          height="120rpx"
          :src="item.shop_logo ? item.shop_logo : logo_img"
        ></u--image>
        <view class="ml_20 f1">
          <view class="f fjsb">
            <view class="f1 ell" style="height: 20px;font-weight: 700">
              {{ item.shop_name }}
            </view>
            <view v-if="classifyCheck === 'supply'">供应链</view>
            <view v-else>{{ item.category_info.name }}</view>
          </view>
          <view class="f fac statistics-view mt_15">
            <view>
              商品数量:
              <text class="c-f1 ml_20">{{ item.goods_count }}</text>
            </view>
            <view class="ml_40">
              热销 (件):
              <text class="c-f1 ml_20">{{ item.hot_sale }}</text>
            </view>
          </view>
        </view>
      </view>

      <scroll-view
        class="scroll-view_H goods-box"
        scroll-x="true"
        scroll-left="0"
      >
        <view class="f">
          <view
            v-for="product in cutOut(item.product)"
            :key="product.id"
            class="goods-item"
            @click="navTo('/packageA/commodity/commodity_details/commodity_details?id=' + product.id)"
          >
            <u--image
              :showLoading="true"
              width="200rpx"
              height="200rpx"
              :src="product.thumb"
            ></u--image>
            <view class="pl mt_20 mb_10">{{product.title}}</view>
            <view class="c-orange" v-if="checkNull(user)">¥{{level_price(product)}}</view>
            <view class="c-orange" v-else>价格登录可见</view>
          </view>
        </view>
      </scroll-view>
    </view>
    <view class="d-cc fs-1 c-5e5e5e mb-25 mt-25">
      <view
        v-if="
          (noMore && list.length != 0) || (noMore && classifyCheck === 'supply')
        "
      >
        暂无更多~
      </view>
      <view v-if="list.length == 0">暂无数据~</view>
    </view>
    <!-- </scroll-view> -->
  </view>
</template>
<script>
import sortBy from '@/common/sortBy/index'
export default {
  components: { sortBy },
  data() {
    return {
      user: uni.getStorageSync('user'),
      logo_img: uni.getStorageSync('h5_logo'),
      list: [],
      sortOptions: [
        [
          { label: '热销 (件)', value: 'is_hot', sort: 'desc' },
          { label: '商品数量', value: 'goods_num', sort: 'desc' },
        ],
      ],
      /**
       * 排序选中
       */
      sortBy: 'is_hot',
      sortOrder: 'desc',
      // 分类选中
      classifyCheck: 'all',
      classifyList: [],
      page: 1,
      pageSize: 5,
      total: 0,
      noMore: false, // 是否为最后一页
    }
  },
  onLoad() {
    this.getClassify()
    this.fetch()
  },
  methods: {
    level_price(product) {
      if (this.$store.state.discount_type === 1) {
        return this.toYuan(this.superTradePrice(product.exec_price))
      } else {
        const n1 = this.accMul(
          product.price,
          uni.getStorageSync('user').user_level.discount,
        )
        const n2 = this.accDiv(n1, 10000)
        return this.toYuan(n2)
        /* return (
            this.toYuan(
              this.accMul(
                product.price,
                uni.getStorageSync('user').user_level.discount,
              ),
            ) * 100
        ) // 把算好的超级批发价转成元,最后再保留两位小数(不四舍五入) */
      }
    },
    jumpA(item){
      let _type = this.classifyCheck === 'supply' ? 1 : 0
      this.navTo('/packageB/store/storeDetails?id=' + item.id + '&type=' + _type)
    },
    cutOut(arr) {
      let newArr = arr
      if (arr && arr.length > 4) {
        newArr = arr.splice(4)
      }
      return newArr
    },
    // 获取全部供应商分类
    getClassify() {
      this.get('/api/supplier/getSupplierCategorys').then(res => {
        if (res.code === 0) {
          this.classifyList = res.data
        }
        this.classifyList.unshift({
          name: '供应链',
          id: 'supply',
        })
        this.classifyList.unshift({
          name: '全部供应商',
          id: 'all',
        })
      })
    },
    // 上滑分页/供应链除外
    onReachBottom() {
      if (!this.noMore) {
        this.page = this.page + 1
        this.fetch()
      }
    },
    // 排序
    handleSortChange(option) {
      this.sortBy = option.sortBy
      this.sortOrder = option.sortOrder
      this.page = 1
      this.noMore = false
      this.fetch(false)
    },
    // 切换分类
    handleClassifyClick(id) {
      this.classifyCheck = id
      this.page = 1
      this.noMore = false
      this.fetch(false)
    },
    // 获取列表
    fetch(addList = true) {
      let page = this.page
      let pageSize = this.pageSize
      // 获取供应链
      if (this.classifyCheck === 'supply') {
        this.post('/api/supplier/getGatherSupplierProductList').then(res => {
          if (res.code === 0) {
            this.noMore = true
            this.list = res.data.list
          }
        })
      } else {
        // 获取供应商
        if (this.noMore) {
          return false
        }
        let params = {}
        if (this.classifyCheck !== 'all') {
          params.category_id = parseInt(this.classifyCheck)
        }
        switch (this.sortBy) {
          case 'is_hot': // 热销
            params.type = 1
            break
          case 'goods_num': // 商品数量
            params.type = 2
            break
        }
        switch (this.sortOrder) {
          case 'asc': // 热销
            params.order = 1
            break
          case 'desc': // 商品数量
            params.order = 2
            break
        }
        this.post('/api/supplier/getSupplierProductList', {
          ...params,
          page,
          pageSize,
        }).then(res => {
          if (res.code === 0) {
            this.noMore =
              res.data.page < res.data.total / res.data.pageSize ? false : true
            if (addList) {
              this.list = [...this.list, ...res.data.list]
            } else {
              this.list = res.data.list
            }
            this.total - res.data.total
          }
        })
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.top-view {
  border-radius: 10rpx;
  .scroll-view_H {
    white-space: nowrap;
    width: 100%;
  }
  .classify-view {
    .classify-item {
      margin-right: 20rpx;
      &.active {
        color: red;
      }
      &:last-child {
        margin-right: 0;
      }
    }
  }
}
.sup-view {
  border-radius: 10rpx;
  margin-top: 20rpx;
  .statistics-view {
    font-size: 22rpx;
  }
}
.goods-box {
  margin-top: 20rpx;
  .goods-item {
    min-width: 200rpx;
    height: 320rpx;
    margin-right: 20rpx;
    &:last-child {
      margin-right: 0;
    }
  }
}
.scroll-view_Y {
  height: calc(100vh - 75px);
}
</style>
