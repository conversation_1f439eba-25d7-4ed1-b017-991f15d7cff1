<template>
  <view class="bgw">
    <view :class="strIcon"></view>
    <view class="w100 f fac fjc f36">{{ title }}</view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      title: '',
      strIcon: ''
    }
  },
  async onLoad(e) {
    let data = {
      code: e.code,
      state: e.state,
    }
    let res = await this.get('/ecCps/opentbRedirectUri', data,false,true,false);
    if (res.code === 0) {
        this.title = '授权登录成功！'
        this.strIcon = 'iconfont icon-all_xuanze icon-ok f fac fjc'
    } else {
      this.title = '授权登录失败！'
      this.toast(res.msg);
      this.strIcon = 'iconfont icon-guanbi icon-ok f fac fjc'
    }
  },
  methods: {
  },
}
</script>

<style lang="scss" scoped>
.icon-ok {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  background-color: red;
  margin: 0 auto 46rpx;
  color: white;
  font-size: 68rpx;
}

.w100 {
  width: 100%;
}

.f36 {
  font-size: 36rpx;
}

.bgw {
  width: 100vw;
  background: white;
  padding-top: 300rpx;
  height: 100vh;
  box-sizing: border-box;
  font-weight: 500;
}
</style>