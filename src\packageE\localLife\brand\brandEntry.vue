<template>
  <view>
    <view class="box_img" v-if="brand">
      <u--image
        :showLoading="true"
        src="https://yunzhong.gz.cn/uploads/file/adf8fbe6bc27d0c226dbfd2d899f3fc1_20241107160809.png"
        width="214rpx"
        height="214rpx"
      ></u--image>
      <view style="margin-top: 26rpx">提交成功，等待审核~</view>
    </view>
    <view class="box" v-else>
      <!-- 表单内容 -->
      <u--form :model="fromData" :rules="rules" ref="fromData">
        <view class="title">主体信息</view>
        <view class="box-card">
          <u-form-item
            label="公司/组织名称"
            labelWidth="195rpx"
            :required="true"
            prop="name"
            ref="name"
            borderBottom
          >
            <u--input
              v-model="fromData.name"
              placeholder="请输入公司/组织名称"
              border="none"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="证件类型"
            labelWidth="195rpx"
            :required="true"
            prop="certificate_type"
            ref="certificate_type"
            borderBottom
          >
            <view class="f fac fjsb w100" @click="certificateBox">
              <view class="input-view-name">{{ certificate.name }}</view>
              <!-- <u--input v-model="certificate.name" placeholder="证件类型" border="none" disabled></u--input> -->
              <view class="iconfont icon-member_right icon_aaa"></view>
            </view>
          </u-form-item>
          <u-form-item
            label="营业执照编码"
            labelWidth="195rpx"
            :required="true"
            prop="certificate_code"
            ref="certificate_code"
            borderBottom
          >
            <u--input
              v-model="fromData.certificate_code"
              placeholder="请输入营业执照编码"
              border="none"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="企业营业执照"
            prop="business_license_img"
            ref="business_license_img"
            :required="true"
            labelWidth="185rpx"
          >
            <view
              class="iconfont icon-a-beizhu2 beizhu_style"
              @click="beizhu('business_license_img')"
            ></view>
          </u-form-item>
          <u-upload
            width="204rpx"
            height="204rpx"
            accept="image"
            maxCount="1"
            maxSize="10485760"
            name="_business_license_img"
            multiple
            :fileList="fileList_business_license_img"
            :previewFullImage="true"
            @afterRead="cardZAfterRead"
            @delete="deletePic"
          >
            <view class="qr-code-view">
              <u-icon name="plus"></u-icon>
            </view>
          </u-upload>
          <view class="toast" v-if="!toastName.business_license_img">
            请上传企业营业执照
          </view>
        </view>
        <view class="title">品牌信息</view>
        <view class="box-card">
          <u-form-item
            label="品牌名称"
            labelWidth="195rpx"
            :required="true"
            prop="brand_info.name"
            ref="brand_info_name"
            borderBottom
          >
            <u--input
              v-model="fromData.brand_info.name"
              placeholder="请输入品牌名称"
              border="none"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="认证类型"
            labelWidth="195rpx"
            :required="true"
            prop="brand_info.auth_type"
            ref="auth_type"
            borderBottom
          >
            <view class="f fac fjsb w100" @click="attestationBox">
              <view class="input-view-name" v-if="attestation.name">
                {{ attestation.name }}
              </view>
              <view class="input-view-name1" v-else>上传商标注册证</view>
              <!-- <u--input v-model="attestation.name" placeholder="上传商标注册证" border="none"
                                disabled></u--input> -->
              <view class="iconfont icon-member_right icon_aaa"></view>
            </view>
          </u-form-item>
          <u-form-item
            label="商标注册证"
            prop="brand_info.trademark_img"
            ref="brand_info_trademark_img"
            :required="true"
            labelWidth="155rpx"
          >
            <view
              class="iconfont icon-a-beizhu2 beizhu_style"
              @click="beizhu('brand_info_trademark_img')"
            ></view>
          </u-form-item>
          <u-upload
            width="204rpx"
            height="204rpx"
            accept="image"
            maxCount="1"
            maxSize="10485760"
            name="_brand_info_trademark_img"
            multiple
            :fileList="fileList_brand_info_trademark_img"
            :previewFullImage="true"
            @afterRead="cardZAfterRead"
            @delete="deletePic"
          >
            <view class="qr-code-view">
              <u-icon name="plus"></u-icon>
            </view>
          </u-upload>
          <view class="toast" v-if="!toastName.brand_info_trademark_img">
            请上传企业营业执照
          </view>
          <u-line margin="25rpx 0 0 0"></u-line>
          <u-form-item
            label="品牌中文名"
            labelWidth="195rpx"
            :required="true"
            prop="brand_info.chinese_name"
            ref="brand_info_chinese_name"
            borderBottom
          >
            <u--input
              v-model="fromData.brand_info.chinese_name"
              placeholder="请输入品牌中文名"
              border="none"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="品牌英文名"
            labelWidth="195rpx"
            :required="true"
            prop="brand_info.english_name"
            ref="brand_info_english_name"
            borderBottom
          >
            <u--input
              v-model="fromData.brand_info.english_name"
              placeholder="请输入品牌英文名"
              border="none"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="商标注册号"
            labelWidth="195rpx"
            :required="true"
            prop="brand_info.trademark_num"
            ref="brand_info_trademark_num"
            borderBottom
          >
            <u--input
              v-model="fromData.brand_info.trademark_num"
              placeholder="请与商标注册证上一致"
              border="none"
            ></u--input>
          </u-form-item>
          <template v-if="fromData.brand_info.valid_type !== 1">
            <u-form-item
              label="商标注册有效期"
              labelWidth="195rpx"
              :required="true"
              prop="brand_info.valid_at"
              ref="brand_info_valid_at"
              borderBottom
            >
              <view
                v-if="fromData.brand_info.valid_type === 2"
                class="f fac fjsb w100"
                @click="validATFun"
              >
                <view class="input-view-name" v-if="validAT.dataName">
                  {{ validAT.dataName }}
                </view>
                <view class="input-view-name1" v-else>请选择</view>
                <!-- <u--input v-model="validAT.dataName" placeholder="请选择" border="none" disabled></u--input> -->
                <view class="iconfont icon-member_right icon_aaa"></view>
              </view>
              <view v-else class="f fac fjsb w100">
                <view class="input-view-name" v-if="validAT.dataName">
                  {{ validAT.dataName }}
                </view>
                <view class="input-view-name1" v-else>请选择</view>
                <!-- <u--input v-model="validAT.dataName" placeholder="请选择" border="none" disabled></u--input> -->
                <view class="iconfont icon-member_right icon_aaa"></view>
              </view>
            </u-form-item>
          </template>

          <u-form-item
            label="有效期类型"
            labelWidth="195rpx"
            :required="true"
            prop="brand_info.valid_type"
            ref="brand_info_valid_type"
            borderBottom
          >
            <view class="f fac fjsb w100" @click="validTypeFun">
              <view class="input-view-name">{{ validType.name }}</view>
              <!-- <u--input v-model="validType.name" border="none" disabled></u--input> -->
              <view class="iconfont icon-member_right icon_aaa"></view>
            </view>
          </u-form-item>
          <u-form-item
            label="品牌经营类型"
            labelWidth="195rpx"
            :required="true"
            prop="brand_info.brand_type"
            ref="brand_info_brand_type"
            borderBottom
          >
            <view class="f fac fjsb w100" @click="brandTypeFun">
              <view class="input-view-name">{{ brandType.name }}</view>
              <!-- <u--input v-model="brandType.name" border="none" disabled></u--input> -->
              <view class="iconfont icon-member_right icon_aaa"></view>
            </view>
          </u-form-item>
          <u-form-item
            label="商标转让证明"
            prop="brand_info.transfer_img"
            ref="brand_info_transfer_img"
            :required="true"
            labelWidth="185rpx"
          >
            <view
              class="iconfont icon-a-beizhu2 beizhu_style"
              @click="beizhu('brand_info_transfer_img')"
            ></view>
          </u-form-item>
          <u-upload
            width="204rpx"
            height="204rpx"
            accept="image"
            maxCount="1"
            maxSize="10485760"
            name="_brand_info_transfer_img"
            multiple
            :fileList="fileList_brand_info_transfer_img"
            :previewFullImage="true"
            @afterRead="cardZAfterRead"
            @delete="deletePic"
          >
            <view class="qr-code-view">
              <u-icon name="plus"></u-icon>
            </view>
          </u-upload>
          <view class="toast" v-if="!toastName.brand_info_transfer_img">
            请上传企业营业执照
          </view>
          <u-line margin="25rpx 0 0 0"></u-line>
          <u-form-item
            label="商标续展证明"
            prop="brand_info.renewal_img"
            ref="brand_info_renewal_img"
            :required="true"
            labelWidth="185rpx"
          >
            <view
              class="iconfont icon-a-beizhu2 beizhu_style"
              @click="beizhu('brand_info_renewal_img')"
            ></view>
          </u-form-item>
          <u-upload
            width="204rpx"
            height="204rpx"
            accept="image"
            maxCount="1"
            maxSize="10485760"
            name="_brand_info_renewal_img"
            multiple
            :fileList="fileList_brand_info_renewal_img"
            :previewFullImage="true"
            @afterRead="cardZAfterRead"
            @delete="deletePic"
          >
            <view class="qr-code-view">
              <u-icon name="plus"></u-icon>
            </view>
          </u-upload>
          <view class="toast" v-if="!toastName.brand_info_renewal_img">
            请上传企业营业执照
          </view>
          <u-line margin="25rpx 0 0 0"></u-line>
          <u-form-item
            label="品牌logo"
            prop="brand_info.logo"
            ref="brand_info_logo"
            :required="true"
            labelWidth="185rpx"
          ></u-form-item>
          <u-upload
            width="204rpx"
            height="204rpx"
            accept="image"
            maxCount="1"
            maxSize="10485760"
            name="_brand_info_logo"
            multiple
            :fileList="fileList_brand_info_logo"
            :previewFullImage="true"
            @afterRead="cardZAfterRead"
            @delete="deletePic"
          >
            <view class="qr-code-view">
              <u-icon name="plus"></u-icon>
            </view>
          </u-upload>
          <view class="toast" v-if="!toastName.brand_info_logo">
            请上传品牌logo
          </view>
        </view>
        <view class="title">经营范围</view>
        <view class="box-card">
          <u-form-item
            label="经营类目"
            labelWidth="195rpx"
            :required="true"
            prop="categories"
            ref="categories"
          >
            <view class="f fac fjsb w100" @click="categoriesFun">
              <view class="input-view-name1">请选择</view>
              <!-- <u--input placeholder="请选择" border="none" disabled></u--input> -->
              <view class="iconfont icon-member_right icon_aaa"></view>
            </view>
          </u-form-item>
          <view
            class="f"
            v-if="categories.list.length > 0"
            style="margin-top: 30rpx; flex-wrap: wrap"
          >
            <template v-for="(item, index) in categories.list">
              <view class="f fac categories" :key="index">
                <view class="categories_name">
                  {{ item[0].name + '/' + item[1].name + '/' + item[2].name }}
                </view>
                <view
                  class="iconfont icon-close11 icon_x"
                  @click="delCategoriesFun(index)"
                ></view>
              </view>
            </template>
          </view>
        </view>
        <view class="title">法人实名信息</view>
        <view class="box-card">
          <u-form-item
            label="法人姓名"
            labelWidth="195rpx"
            :required="true"
            prop="lr_info.real_name"
            ref="lr_info_real_name"
            borderBottom
          >
            <u--input
              v-model="fromData.lr_info.real_name"
              placeholder="请输入法人姓名"
              border="none"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="法人证件号"
            labelWidth="195rpx"
            :required="true"
            prop="lr_info.id_card"
            ref="lr_info_id_card"
            borderBottom
          >
            <u--input
              v-model="fromData.lr_info.id_card"
              placeholder="请输入法人证件号"
              border="none"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="证件类型"
            labelWidth="195rpx"
            :required="true"
            prop="lr_info.certificate_type"
            ref="lr_info_certificate_type"
            borderBottom
          >
            <view class="f fac fjsb w100" @click="lrCertificateFun">
              <view class="input-view-name">{{ irCertificate.name }}</view>
              <!-- <u--input v-model="irCertificate.name" border="none" disabled></u--input> -->
              <view class="iconfont icon-member_right icon_aaa"></view>
            </view>
          </u-form-item>
          <u-form-item
            label="上传人像面"
            prop="lr_info.id_front_img"
            ref="lr_info_id_front_img"
            :required="true"
            labelWidth="155rpx"
          >
            <view
              class="iconfont icon-a-beizhu2 beizhu_style"
              @click="beizhu('lr_info_id_front_img')"
            ></view>
          </u-form-item>
          <u-upload
            width="204rpx"
            height="204rpx"
            accept="image"
            maxCount="1"
            maxSize="10485760"
            name="_lr_info_id_front_img"
            multiple
            :fileList="fileList_lr_info_id_front_img"
            :previewFullImage="true"
            @afterRead="cardZAfterRead"
            @delete="deletePic"
          >
            <view class="qr-code-view">
              <u-icon name="plus"></u-icon>
            </view>
          </u-upload>
          <view class="toast" v-if="!toastName.lr_info_id_front_img">
            请上传企业营业执照
          </view>
          <u-line margin="25rpx 0 0 0"></u-line>
          <u-form-item
            label="上传国徽面"
            prop="lr_info.id_back_img"
            ref="id_back_img"
            :required="true"
            labelWidth="155rpx"
          >
            <view
              class="iconfont icon-a-beizhu2 beizhu_style"
              @click="beizhu('lr_info_id_front_img')"
            ></view>
          </u-form-item>
          <u-upload
            width="204rpx"
            height="204rpx"
            accept="image"
            maxCount="1"
            maxSize="10485760"
            name="_lr_info_id_back_img"
            multiple
            :fileList="fileList_lr_info_id_back_img"
            :previewFullImage="true"
            @afterRead="cardZAfterRead"
            @delete="deletePic"
          >
            <view class="qr-code-view">
              <u-icon name="plus"></u-icon>
            </view>
          </u-upload>
          <view class="toast" v-if="!toastName.lr_info_id_back_img">
            请上传企业营业执照
          </view>
        </view>
        <view class="title">账号信息</view>
        <view class="box-card">
          <view class="annotation">
            注：登录账号不可变更，如手机号非品牌长期持有，不建议使用手机号作为登录账号！
          </view>
          <u-form-item
            label="联系人姓名"
            labelWidth="195rpx"
            :required="true"
            prop="real_name"
            ref="real_name"
            borderBottom
          >
            <u--input
              v-model="fromData.real_name"
              placeholder="请输入联系人姓名"
              border="none"
            ></u--input>
          </u-form-item>

          <u-form-item
            label="联系人电话"
            labelWidth="195rpx"
            :required="true"
            prop="mobile"
            ref="mobile"
            borderBottom
          >
            <u--input
              v-model="fromData.mobile"
              placeholder="请输入联系人电话"
              border="none"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="账号"
            labelWidth="195rpx"
            :required="true"
            prop="username"
            ref="username"
            borderBottom
          >
            <u--input
              v-model="fromData.username"
              placeholder="请输入账号"
              border="none"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="密码"
            labelWidth="195rpx"
            :required="true"
            prop="password"
            ref="password"
          >
            <u--input
              v-model="fromData.password"
              placeholder="请输入密码"
              border="none"
            ></u--input>
          </u-form-item>
        </view>
      </u--form>
      <view class="but-red">
        <u-button
          text="提交"
          @click="submit"
          type="error"
          shape="circle"
        ></u-button>
      </view>
      <!-- 证件类型 // 企业 -->
      <u-popup
        :show="certificate.show"
        :round="10"
        mode="bottom"
        @close="close"
      >
        <view class="classify">
          <view class="classify_title d-bf">
            <view class="c-b5 font_size13" @click="close">取消</view>
            <view class="c-20 font_size17">选择证件类型</view>
            <view class="font_size13 c-orange" @click="open">确认</view>
          </view>
          <view class="classify_content">
            <template v-for="item in certificate.list">
              <view
                class="certificate f fjsb fac"
                :key="item.id"
                @click="certificateChange(item)"
              >
                <view>{{ item.name }}</view>
                <view
                  v-if="item.id === certificate.id"
                  class="iconfont icon-all_select_choose icon_red"
                ></view>
              </view>
            </template>
          </view>
        </view>
      </u-popup>

      <!-- 认证类型 -->
      <u-popup
        :show="attestation.show"
        :round="10"
        mode="bottom"
        @close="attestationClose"
      >
        <view class="classify">
          <view class="classify_title d-bf">
            <view class="c-b5 font_size13" @click="attestationClose">取消</view>
            <view class="c-20 font_size17">选择证件类型</view>
            <view class="font_size13 c-orange" @click="attestationOpen">
              确认
            </view>
          </view>
          <view class="classify_content_attestation">
            <template v-for="item in attestation.list">
              <view
                class="certificate f fjsb fac"
                :key="item.id"
                @click="attestationChange(item)"
              >
                <view>{{ item.name }}</view>
                <view
                  v-if="item.id === attestation.id"
                  class="iconfont icon-all_select_choose icon_red"
                ></view>
              </view>
            </template>
          </view>
        </view>
      </u-popup>

      <!-- 时间选择器 -->
      <u-datetime-picker
        ref="datetimePicker"
        class="custom-datetime-picker"
        :show="validAT.show"
        v-model="validAT.value"
        title="请选择年月日"
        mode="date"
        :formatter="formatter"
        itemHeight="100"
        @cancel="closeData"
        @confirm="confirmData"
      ></u-datetime-picker>

      <!-- 有效期类型 -->
      <u-popup
        :show="validType.show"
        :round="10"
        mode="bottom"
        @close="validTypeClose"
      >
        <view class="classify">
          <view class="classify_title d-bf">
            <view class="c-b5 font_size13" @click="validTypeClose">取消</view>
            <view class="c-20 font_size17">有效期类型</view>
            <view class="font_size13 c-orange" @click="validTypeOpen">
              确认
            </view>
          </view>
          <view class="classify_content_attestation">
            <template v-for="item in validType.list">
              <view
                class="certificate f fjsb fac"
                :key="item.id"
                @click="validTypeChange(item)"
              >
                <view>{{ item.name }}</view>
                <view
                  v-if="item.id === validType.id"
                  class="iconfont icon-all_select_choose icon_red"
                ></view>
              </view>
            </template>
          </view>
        </view>
      </u-popup>

      <!-- 品牌经营类型 -->
      <u-popup
        :show="brandType.show"
        :round="10"
        mode="bottom"
        @close="brandTypeClose"
      >
        <view class="classify">
          <view class="classify_title d-bf">
            <view class="c-b5 font_size13" @click="brandTypeClose">取消</view>
            <view class="c-20 font_size17">品牌经营类型</view>
            <view class="font_size13 c-orange" @click="brandTypeOpen">
              确认
            </view>
          </view>
          <view class="classify_content_attestation">
            <template v-for="item in brandType.list">
              <view
                class="certificate f fjsb fac"
                :key="item.id"
                @click="brandTypeChange(item)"
              >
                <view>{{ item.name }}</view>
                <view
                  v-if="item.id === brandType.id"
                  class="iconfont icon-all_select_choose icon_red"
                ></view>
              </view>
            </template>
          </view>
        </view>
      </u-popup>

      <!-- 经营类目 -->
      <u-picker
        title="经营类目"
        :loading="categories.loading"
        :show="categories.show"
        ref="uPicker"
        keyName="name"
        confirmColor="#f15353"
        :columns="categories.columns"
        @confirm="categoriesConfirm"
        @change="categoriesChange"
        itemHeight="100"
        @cancel="categories.show = false"
      ></u-picker>

      <!-- 证件类型 // 身份证 -->
      <u-popup
        :show="irCertificate.show"
        :round="10"
        mode="bottom"
        @close="irCertificateClose"
      >
        <view class="classify">
          <view class="classify_title d-bf">
            <view class="c-b5 font_size13" @click="irCertificateClose">
              取消
            </view>
            <view class="c-20 font_size17">品牌经营类型</view>
            <view class="font_size13 c-orange" @click="irCertificateOpen">
              确认
            </view>
          </view>
          <view class="classify_content_ircard">
            <template v-for="item in irCertificate.list">
              <view
                class="certificate f fjsb fac"
                :key="item.id"
                @click="irCertificateChange(item)"
              >
                <view>{{ item.name }}</view>
                <view
                  v-if="item.id === irCertificate.id"
                  class="iconfont icon-all_select_choose icon_red"
                ></view>
              </view>
            </template>
          </view>
        </view>
      </u-popup>

      <!-- 企业营业执照备注弹窗 -->
      <u-popup
        :show="beizhuShow"
        mode="center"
        :round="30"
        @close="beizhuClose"
      >
        <view class="beizhu">
          <view class="beizhuStyle">
            <view class="beizhuStyle_tltle">提示</view>
            <view
              class="beizhuStyle_text"
              v-if="beizhuName === 'business_license_img'"
            >
              1. 需提供营业执照原件扫描件或加盖公司公章的复印件，不可截屏摄屏。
              <br />
              2. 营业执照未注吊销，且有效期截止时间大于15天。
              <br />
              3.
              由于新注册执照存在公示期，信息可能更新不及时，建议您自注册之日起【14个工作日】后再入驻。
              <br />
              4.
              图片尺寸为800*800px以上，支持PNG、JPG和JPEG格式，大小不超过5MB。
              <br />
            </view>
            <view
              class="beizhuStyle_text"
              v-else-if="beizhuName === 'brand_info_trademark_img'"
            >
              1. 请上传商标注册证扫描件。
              <br />
              2. 确保未在企业经营异常名录中，且所售商品在营业执照经营范围内。
              <br />
              3. 露出证件四角，请勿遮挡或模糊，保持信息清晰可见。
              <br />
              4. 有效期截止时间距今应大于15天。
              <br />
              5. 图片支持PNG、JPG和JPEG格式，大小不超过5MB。
              <br />
            </view>
            <view
              class="beizhuStyle_text"
              v-else-if="beizhuName === 'brand_info_transfer_img'"
            >
              1. 如涉及商标转让，请补充转让证明。
              <br />
              2. 露出证件四角，请勿遮挡或模糊，保持信息清晰可见。
              <br />
              3. 图片支持PNG、JPG和JPEG格式，大小不超过5MB。
              <br />
            </view>
            <view
              class="beizhuStyle_text"
              v-else-if="beizhuName === 'brand_info_renewal_img'"
            >
              1. 如涉及商标过期续展等，请补充商标续展证明。
              <br />
              2. 露出证件四角，请勿遮挡或模糊，保持信息清晰可见。
              <br />
              3. 图片支持PNG、JPG和JPEG格式，大小不超过5MB。
              <br />
            </view>
            <view
              class="beizhuStyle_text"
              v-else-if="beizhuName === 'lr_info_id_front_img'"
            >
              1. 请上传法人的身份证。
              <br />
              2.
              所提交资质若为复印件请加盖公司鲜章或本人手写签名，并且不能为截屏摄屏，不能带有无效水印。
              <br />
              3.图片尺寸为800*800px以上，支持PNG、JPG和JPEG格式，大小不超过5MB。
              <br />
            </view>
          </view>
          <view class="close-box">
            <view
              class="iconfont icon-close11 close-style"
              @click="beizhuClose"
            ></view>
          </view>
        </view>
      </u-popup>
    </view>
  </view>
</template>

<script>
export default {
  name: 'brandEntry',

  data() {
    return {
      brand: false, // 确认完毕

      beizhuShow: false, // 企业营业执照备注弹窗开关
      beizhuName: '',
      toastName: {
        business_license_img: true,
        brand_info_trademark_img: true,
        brand_info_transfer_img: true,
        brand_info_renewal_img: true,
        brand_info_logo: true,
        lr_info_id_front_img: true,
        lr_info_id_back_img: true,
      }, // 图片的文本提示

      fileList_business_license_img: [], // 企业营业执照
      fileList_brand_info_trademark_img: [], // 商品注册号
      fileList_brand_info_transfer_img: [], // 商标转让证明
      fileList_brand_info_renewal_img: [], // 商标续展证明
      fileList_brand_info_logo: [], // 品牌logo
      fileList_lr_info_id_front_img: [], // 人像面
      fileList_lr_info_id_back_img: [], // 国徽面
      fromData: {
        name: '', // 公司/组织名称
        certificate_type: null, // 证件类型
        certificate_code: '', // 请输入营业执照编码
        business_license_img: '', // 企业营业执照

        // 品牌信息
        brand_info: {
          name: '', // 品牌名称
          auth_type: null, // 认证类型
          trademark_img: '', // 商标注册证
          chinese_name: '', // 品牌中文名
          english_name: '', // 品牌英文名
          trademark_num: '', // 商标注册号
          valid_at: '', // 商标注册有效期
          valid_type: 2, // 有效期类型
          brand_type: null, // 品牌经营类型
          logo: '', // 品牌logo
        },

        // 经营范围
        categories: [],

        // 法人实名信息
        lr_info: {
          real_name: '', // 法人姓名
          id_card: '', // 法人证件号
          certificate_type: null, // 法人证件类型
          id_front_img: '', // 人面像
          id_back_img: '', // 国徽面
        },

        real_name: '', // 联系人姓名
        mobile: '', // 联系人手机号
        username: '', // 账号
        password: '', // 密码
      },
      rules: {
        name: [
          {
            type: 'string',
            required: true,
            message: '请输入公司/组织名称',
            trigger: ['blur'],
          },
        ],
        certificate_code: [
          {
            type: 'string',
            required: true,
            message: '请输入营业执照编号',
            trigger: ['blur'],
          },
        ],
        'brand_info.name': [
          {
            type: 'string',
            required: true,
            message: '请输入品牌名称',
            trigger: ['blur'],
          },
        ],
        'brand_info.auth_type': [
          {
            type: 'number',
            required: true,
            message: '请输入认证类型',
            trigger: ['change'],
          },
        ],
        'brand_info.chinese_name': [
          {
            type: 'string',
            required: true,
            message: '请输入品牌中文名',
            trigger: ['blur'],
          },
        ],
        'brand_info.english_name': [
          {
            type: 'string',
            required: true,
            message: '请输入品牌英文名',
            trigger: ['blur'],
          },
        ],
        'brand_info.trademark_num': [
          {
            type: 'string',
            required: true,
            message: '请输入商标注册证',
            trigger: ['blur'],
          },
        ],
        'brand_info.valid_at': [
          {
            type: 'number',
            required: true,
            message: '请输入商标注册证有效期',
            trigger: ['change'],
          },
        ],
        categories: [
          {
            // 自定义验证函数，见上说明
            validator: (rule, value, callback) => {
              if (this.categories.list.length > 0) {
                return true
              } else {
                return false
              }
            },
            message: '请输入的经营类目',
            // 触发器可以同时用blur和change
            trigger: ['blur'],
          },
        ],
        'lr_info.real_name': [
          {
            type: 'string',
            required: true,
            message: '请输入法人姓名',
            trigger: ['blur'],
          },
        ],
        'lr_info.id_card': [
          {
            type: 'string',
            required: true,
            message: '请输入法人证件号',
            trigger: ['blur'],
          },
          {
            // 自定义验证函数，见上说明
            validator: (rule, value, callback) => {
              // 上面有说，返回true表示校验通过，返回false表示不通过
              return uni.$u.test.idCard(value)
            },
            message: '您输入的身份证号码不是有效格式',
            // 触发器可以同时用blur和change
            trigger: ['blur'],
          },
        ],
        real_name: [
          {
            type: 'string',
            required: true,
            message: '请输入联系人姓名',
            trigger: ['blur'],
          },
        ],
        mobile: [
          {
            type: 'string',
            required: true,
            message: '请输入联系人电话',
            trigger: ['blur'],
          },
          {
            // 自定义验证函数，见上说明
            validator: (rule, value, callback) => {
              // 上面有说，返回true表示校验通过，返回false表示不通过
              let RegEx = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
              if (RegEx.test(value)) {
                return value
              } else {
                return false
              }
            },
            message: '您输入的手机号格式错误',
            // 触发器可以同时用blur和change
            trigger: ['blur'],
          },
        ],
        username: [
          {
            type: 'string',
            required: true,
            message: '请输入账号',
            trigger: ['blur'],
          },
        ],
        password: [
          {
            type: 'string',
            required: true,
            message: '请输入密码',
            trigger: ['blur'],
          },
        ],
      },

      // 证件类型
      certificate: {
        show: false,
        list: [],
        id: null,
        name: '',
      },

      // 认证类型
      attestation: {
        show: false,
        list: [
          { id: 1, name: '上传商标注册证' },
          { id: 2, name: '上传非商标品牌资质' },
        ],
        id: null,
        name: '',
      },

      // 商标注册有效期
      validAT: {
        show: false,
        value: Number(new Date()),
        dataName: '',
      },

      // 有效期类型
      validType: {
        show: false,
        list: [
          { id: 2, name: '非长期有效' },
          { id: 1, name: '长期有效' },
        ],
        id: 2,
        name: '非长期有效',
      },

      // 品牌经营类型
      brandType: {
        show: false,
        list: [],
        id: null,
        name: '',
      },

      // 经营分类
      categories: {
        show: false,
        loading: false,
        columns: [],
        list: [],
      },

      irCertificate: {
        show: false,
        list: [],
        id: null,
        name: '',
      },
    }
  },
  onShow() {
    this.certificateTypes()
    this.getbrandType()
    this.getCategories()
    this.lrCertificateTypes()
  },
  methods: {
    // 提交
    async submit() {
      if (
        this.fromData.brand_info.chinese_name &&
        this.fromData.brand_info.chinese_name !== ''
      ) {
        this.rules['brand_info.english_name'] = []
      }
      if (
        this.fromData.brand_info.english_name &&
        this.fromData.brand_info.english_name !== ''
      ) {
        this.rules['brand_info.chinese_name'] = []
      }
      this.$refs.fromData
        .validate()
        .then(async res => {
          this.toastName.business_license_img = !this.fromData
            .business_license_img
            ? false
            : true
          this.toastName.brand_info_trademark_img = !this.fromData.brand_info
            .trademark_img
            ? false
            : true
          this.toastName.brand_info_transfer_img = !this.fromData.brand_info
            .transfer_img
            ? false
            : true
          this.toastName.brand_info_renewal_img = !this.fromData.brand_info
            .renewal_img
            ? false
            : true
          this.toastName.brand_info_logo = !this.fromData.brand_info.logo
            ? false
            : true
          this.toastName.lr_info_id_front_img = !this.fromData.lr_info
            .id_front_img
            ? false
            : true
          this.toastName.lr_info_id_back_img = !this.fromData.lr_info
            .id_back_img
            ? false
            : true
          let s = false
          for (const key in this.toastName) {
            if (!this.toastName[key]) {
              s = true
              return
            }
          }
          if (s) {
            this.rules['brand_info.chinese_name'] = [
              {
                type: 'string',
                required: true,
                message: '请输入品牌中文名',
                trigger: ['blur'],
              },
            ]
            this.rules['brand_info.english_name'] = [
              {
                type: 'string',
                required: true,
                message: '请输入品牌英文名',
                trigger: ['blur'],
              },
            ]
            return
          }
          let categorieslist = []
          this.fromData.brand_info.valid_at = Math.ceil(
            this.fromData.brand_info.valid_at,
          )
          this.categories.list.forEach(element => {
            categorieslist.push({
              category1_id: parseInt(element[0].id),
              category2_id: parseInt(element[1].id),
              category3_id: parseInt(element[2].id),
            })
          })
          this.fromData.categories = categorieslist
          let { msg, code } = await this.post(
            '/api/localLife/front/brand/apply',
            { ...this.fromData },
          )
          if (code === 0) {
            this.brand = true
          }
        })
        .catch(errors => {
          this.toast('请按红字提示填写')
          this.rules['brand_info.chinese_name'] = [
            {
              type: 'string',
              required: true,
              message: '请输入品牌中文名',
              trigger: ['blur'],
            },
          ]
          this.rules['brand_info.english_name'] = [
            {
              type: 'string',
              required: true,
              message: '请输入品牌英文名',
              trigger: ['blur'],
            },
          ]
          this.toastName.business_license_img = !this.fromData
            .business_license_img
            ? false
            : true
          this.toastName.brand_info_trademark_img = !this.fromData.brand_info
            .trademark_img
            ? false
            : true
          this.toastName.brand_info_transfer_img = !this.fromData.brand_info
            .transfer_img
            ? false
            : true
          this.toastName.brand_info_renewal_img = !this.fromData.brand_info
            .renewal_img
            ? false
            : true
          this.toastName.brand_info_logo = !this.fromData.brand_info.logo
            ? false
            : true
          this.toastName.lr_info_id_front_img = !this.fromData.lr_info
            .id_front_img
            ? false
            : true
          this.toastName.lr_info_id_back_img = !this.fromData.lr_info
            .id_back_img
            ? false
            : true
        })
    },
    // 上传企业营业执照图片
    async cardZAfterRead(event) {
      // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file)
      let file = `fileList${event.name}`

      let fileListLen = this[file].length
      lists.map(item => {
        this[file].push({
          ...item,
          status: 'uploading',
          message: '上传中',
        })
      })
      for (let i = 0; i < lists.length; i++) {
        const result = await this.uploadFilePromise(lists[i].url, file)
        let item = this[file][fileListLen]
        this[file].splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: 'success',
            message: '',
            url: result,
          }),
        )
        fileListLen++
      }
    },
    // 删除图片
    deletePic(event) {
      let file = `fileList${event.name}`
      this[file].splice(event.index, 1)
      switch (file) {
        case 'fileList_business_license_img':
          this.fromData.business_license_img = ''
          break
        case 'fileList_brand_info_trademark_img':
          this.fromData.brand_info.trademark_img = ''
          break
        case 'fileList_brand_info_transfer_img':
          this.fromData.brand_info.transfer_img = ''
          break
        case 'fileList_brand_info_renewal_img':
          this.fromData.brand_info.renewal_img = ''
          break
        case 'fileList_brand_info_logo':
          this.fromData.brand_info.logo = ''
          break
        case 'fileList_lr_info_id_front_img':
          this.fromData.lr_info.id_front_img = ''
          break
        case 'fileList_lr_info_id_back_img':
          this.fromData.lr_info.id_back_img = ''
          break
        default:
          break
      }
    },
    uploadFilePromise(url, file) {
      return new Promise((resolve, reject) => {
        const a = uni.uploadFile({
          url: this.api.host + '/api/common/upload',
          filePath: url,
          name: 'file',
          success: res => {
            setTimeout(() => {
              resolve(res.data.data)
              const data = JSON.parse(res.data)
              switch (file) {
                case 'fileList_business_license_img':
                  this.fromData.business_license_img = data.data.file.url
                  break
                case 'fileList_brand_info_trademark_img':
                  this.fromData.brand_info.trademark_img = data.data.file.url
                  break
                case 'fileList_brand_info_transfer_img':
                  this.fromData.brand_info.transfer_img = data.data.file.url
                  break
                case 'fileList_brand_info_renewal_img':
                  this.fromData.brand_info.renewal_img = data.data.file.url
                  break
                case 'fileList_brand_info_logo':
                  this.fromData.brand_info.logo = data.data.file.url
                  break
                case 'fileList_lr_info_id_front_img':
                  this.fromData.lr_info.id_front_img = data.data.file.url
                  break
                case 'fileList_lr_info_id_back_img':
                  this.fromData.lr_info.id_back_img = data.data.file.url
                  break
                default:
                  break
              }
              this.toastName[file.split('fileList_')[1]] = true
            }, 1000)
          },
        })
      })
    },

    /******************** 公司证件类型 ********************/
    // 获取证件类型
    certificateTypes() {
      this.get('/api/localLife/front/brand/certificateTypes', {}, true).then(
        res => {
          this.certificate.list = res.data.types
          this.certificate.name = this.certificate.list[0].name
          this.fromData.certificate_type = this.certificate.list[0].id
        },
      )
    },
    // 关闭证件类型
    close() {
      this.certificate.show = false
    },
    // 确认证件类型
    open() {
      let res = this.certificate.list.find(
        item => item.id === this.certificate.id,
      )
      this.certificate.name = res.name
      this.fromData.certificate_type = res.id
      this.certificate.show = false
    },
    // 打开证件类型下弹窗
    certificateBox() {
      this.certificate.show = true
      let res = this.certificate.list.find(
        item => item.name === this.certificate.name,
      )
      this.certificate.id = res.id
    },
    // 点击选择证件类型
    certificateChange(item) {
      this.certificate.id = item.id
    },

    /******************** 认证类型 ********************/
    // 关闭认证类型弹窗
    attestationClose() {
      this.attestation.show = false
    },
    // 确认认证类型
    attestationOpen() {
      if (this.attestation.id === null) {
        this.attestation.show = false
        return
      }
      let res = this.attestation.list.find(
        item => item.id === this.attestation.id,
      )
      this.attestation.name = res.name
      this.fromData.brand_info.auth_type = res.id
      this.$refs.fromData.validateField('brand_info.auth_type')
      this.attestation.show = false
    },
    // 打开认证类型弹窗
    attestationBox() {
      this.attestation.show = true
    },
    // 点击选择认证类型
    attestationChange(item) {
      this.attestation.id = item.id
    },

    /******************** 商标注册有效期 ********************/
    // 打开时间选择器
    validATFun() {
      this.validAT.show = true
    },
    // 关闭时间选择器
    closeData() {
      this.validAT.show = false
    },
    // 确认时间选择器
    confirmData(data) {
      this.validAT.dataName = this.formatDateTime(data.value / 1000, 3)
      this.fromData.brand_info.valid_at = data.value / 1000
      this.$refs.fromData.validateField('brand_info.valid_at')
      this.validAT.show = false
    },
    formatter(type, value) {
      if (type === 'year') {
        return `${value}年`
      }
      if (type === 'month') {
        return `${value}月`
      }
      if (type === 'day') {
        return `${value}日`
      }
      return value
    },

    /******************** 有效期类型 ********************/
    // 打开有效期弹窗
    validTypeFun() {
      this.validType.show = true
    },
    // 点击选择有效期类型
    validTypeChange(item) {
      this.validType.id = item.id
    },
    // 关闭有效期类型弹窗
    validTypeClose() {
      this.validType.show = false
    },
    // 确认有效期类型
    validTypeOpen() {
      let res = this.validType.list.find(item => item.id === this.validType.id)
      this.validType.name = res.name
      this.fromData.brand_info.valid_type = res.id
      this.validType.show = false
      // 长期有效则禁用时间栏
      if (res.id === 1) {
        this.validAT.dataName = ''
        this.fromData.brand_info.valid_at = ''
        this.rules['brand_info.valid_at'] = [{}]
        this.$refs.fromData.validateField('brand_info.valid_at')
      } else {
        this.rules['brand_info.valid_at'] = [
          {
            type: 'number',
            required: true,
            message: '请输入商标注册证有效期',
            trigger: ['change'],
          },
        ]
      }
    },

    /******************** 品牌经营类型 ********************/
    // 获取品牌经营类型
    getbrandType() {
      this.get('/api/localLife/front/brand/types', {}, true).then(res => {
        if (res.code === 0) {
          this.brandType.list = res.data.types
          this.brandType.name = this.brandType.list[0].name
          this.brandType.id = this.brandType.list[0].id
          this.fromData.brand_info.brand_type = this.brandType.list[0].id
        }
      })
    },
    // 打开品牌经营类型弹窗
    brandTypeFun() {
      this.brandType.show = true
      let res = this.brandType.list.find(
        item => item.name === this.brandType.name,
      )
      this.brandType.id = res.id
    },
    // 打开品牌经营类型弹窗
    brandTypeChange(item) {
      this.brandType.id = item.id
    },
    // 关闭品牌经营类型弹窗
    brandTypeClose() {
      this.brandType.show = false
    },
    // 确认品牌经营类型弹窗
    brandTypeOpen() {
      let res = this.brandType.list.find(item => item.id === this.brandType.id)
      this.brandType.name = res.name
      this.fromData.brand_info.brand_type = res.id
      this.brandType.show = false
    },

    /******************** 品牌经营类型 ********************/
    // 获取经营类目
    async getCategories() {
      // 获取一级分类
      let res = await this.get('/api/localLife/front/category/all', {}, true)

      // 获取二级分类
      let para = { level: 2, parent_id: parseInt(res.data[0].id) }
      let res1 = await this.get('/api/localLife/front/category/all', para, true)
      res1.data.unshift({ name: '无', id: 0 })

      // 获取三级分类
      let para1 = { level: 3, parent_id: parseInt(res1.data[0].id) }
      let res2 = await this.get(
        '/api/localLife/front/category/all',
        para1,
        true,
      )
      res2.data.unshift({ name: '无', id: 0 })

      this.categories.columns = [res.data, res1.data, res2.data]
    },
    // 打开经营类目
    categoriesFun() {
      this.categories.show = true
    },
    // 确认经营类目
    categoriesConfirm(e) {
      this.categories.list.push(e.value)
      this.$refs.fromData.validateField('categories')
      this.categories.show = false
    },
    // 多级联动
    categoriesChange(e) {
      const {
        columnIndex,
        value,
        values,
        index,
        picker = this.$refs.uPicker,
      } = e
      this.categories.loading = true
      if (columnIndex === 0) {
        this.get(
          '/api/localLife/front/category/all',
          { level: 2, parent_id: parseInt(value[0].id) },
          true,
        ).then(res => {
          if (res.code === 0) {
            let category2 = [{ name: '无', id: 0 }, ...res.data]
            picker.setColumnValues(1, category2)
            if (res.data.length === 0) {
              picker.setColumnValues(2, [])
              return
            }
            this.get(
              '/api/localLife/front/category/all',
              { level: 3, parent_id: parseInt(res.data[0].id) },
              true,
            ).then(res1 => {
              if (res1.code === 0) {
                let category3 = [{ name: '无', id: 0 }, ...res1.data]
                picker.setColumnValues(2, category3)
              }
            })
          } else {
            return
          }
        })
        this.categories.loading = false
      }
      if (columnIndex === 1) {
        this.get(
          '/api/localLife/front/category/all',
          { level: 3, parent_id: parseInt(value[1].id) },
          true,
        ).then(res => {
          if (res.code === 0) {
            let category = [{ name: '无', id: 0 }, ...res.data]
            picker.setColumnValues(2, category)
          } else {
            return
          }
        })
        this.categories.loading = false
      }
      if (columnIndex === 2) {
        this.categories.loading = false
      }
    },
    // 删除类目
    delCategoriesFun(i) {
      this.categories.list = this.categories.list.filter(
        (item, index) => index !== i,
      )
    },

    /******************** 法人证件类型 ********************/
    // 获取证件类型
    lrCertificateTypes() {
      this.get('/api/localLife/front/brand/lrCertificateTypes', {}, true).then(
        res => {
          this.irCertificate.list = res.data.types
          this.irCertificate.name = this.irCertificate.list[0].name
          this.fromData.lr_info.certificate_type = this.irCertificate.list[0].id
        },
      )
    },
    // 打开证件弹窗
    lrCertificateFun() {
      this.irCertificate.show = true
      let res = this.irCertificate.list.find(
        item => item.name === this.irCertificate.name,
      )
      this.irCertificate.id = res.id
    },
    // 关闭证件弹窗
    irCertificateClose() {
      this.irCertificate.show = false
    },
    // 确认证件
    irCertificateOpen() {
      let res = this.irCertificate.list.find(
        item => item.id === this.irCertificate.id,
      )
      this.irCertificate.name = res.name
      this.fromData.lr_info.certificate_type = res.id
      this.irCertificate.show = false
    },
    // 选择证件
    irCertificateChange(item) {
      this.irCertificate.id = item.id
    },

    /******************** 企业营业执照备注弹窗 ********************/
    // 企业营业执照 备注
    beizhu(title) {
      this.beizhuShow = true
      this.beizhuName = title
    },
    // 关闭
    beizhuClose() {
      this.beizhuShow = false
    },
  },
}
</script>

<style lang="scss" scoped>
.w100 {
  width: 100%;
}

.input-view-name {
  font-size: 30rpx;
}

.input-view-name1 {
  font-size: 30rpx;
  color: rgb(192, 196, 204);
}

.icon_red {
  color: #f15353;
}

.icon_aaa {
  color: #aaaab3;
}

.box {
  background-color: #f5f5f5;
}

.box_img {
  // background-color: #FFFFFF;
  width: 100%;
  display: flex;
  align-items: center;
  padding-top: 100rpx;
  flex-direction: column;
}

.title {
  height: 80rpx;
  width: 702rpx;
  margin: 0 auto;
  font-size: 28rpx;
  color: #00001c;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.box-card {
  background: #ffffff;
  border-radius: 16rpx;
  width: 702rpx;
  margin: 0 auto;
  padding: 14rpx 32rpx;
  box-sizing: border-box;
}

.classify {
  padding: 40rpx 30rpx 30rpx 30rpx;

  .classify_content {
    margin-top: 50rpx;
    height: 600rpx;
    overflow-y: scroll;

    .certificate {
      height: 103rpx;
      border-bottom: 1px solid #f0f0f1;
      font-size: 28rpx;
      font-weight: 500;
    }
  }

  .classify_content_ircard {
    margin-top: 50rpx;
    height: 420rpx;
    overflow-y: scroll;

    .certificate {
      height: 103rpx;
      border-bottom: 1px solid #f0f0f1;
      font-size: 28rpx;
      font-weight: 500;
    }
  }

  .classify_content_attestation {
    margin-top: 50rpx;
    height: 250rpx;
    overflow-y: scroll;

    .certificate {
      height: 103rpx;
      border-bottom: 1px solid #f0f0f1;
      font-size: 28rpx;
      font-weight: 500;
    }
  }
}

.categories {
  font-size: 24rpx;
  height: 52rpx;
  padding: 0 16rpx;
  background: #f8f8f8;
  border-radius: 26rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;

  .icon_x {
    font-size: 16rpx;
    color: #aaaab3;
    margin-left: 17rpx;
  }
}

.annotation {
  height: 100rpx;
  background: #F8F8F8;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 36rpx;
  font-size: 24rpx;
  width: 615rpx;
  padding: 0 24rpx;
  box-sizing: border-box;
  margin: 6rpx auto;
  color: #6e6e79;
}

.but-red {
  // margin: 0 auto 20rpx;
  // width: 702rpx;
  padding: 20rpx 24rpx;
  width: 100%;
  height: 120rpx;
  box-sizing: border-box;
  background-color: #ffffff;
  // background: #F15353;
  // border-radius: 41rpx 41rpx 41rpx 41rpx;
  margin-top: 135rpx;
}

.qr-code-view {
  min-width: 204rpx;
  min-height: 204rpx;
  max-width: 204rpx;
  max-height: 204rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10rpx;
  background-color: #F8F8F8;

  ::v-deep .u-icon .uicon-plus {
    color: #b4b4b4 !important;
    font-size: 40rpx !important;
  }
}

.beizhu_style {
  width: 26rpx;
  height: 26rpx;
  color: #aaaab3;
}

.beizhu {
  width: 600rpx;
  position: relative;

  .beizhuStyle {
    width: 600rpx;
    background: #ffffff;
    border-radius: 30rpx;

    .beizhuStyle_tltle {
      font-size: 32rpx;
      color: #00001c;
      font-weight: bold;
      text-align: center;
      line-height: 40rpx;
      margin: 28rpx 0 20rpx 0;
    }

    .beizhuStyle_text {
      width: 540rpx;
      margin: 0 auto 30rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #3b3b4a;
      line-height: 44rpx;
    }
  }

  .close-box {
    position: absolute;
    bottom: -160rpx;
    width: 100%;

    .close-style {
      width: 64rpx;
      height: 64rpx;
      border-radius: 50%;
      margin: 0 auto;
      border: 6rpx solid #d6d6dc;
      font-size: 45rpx;
      color: #d6d6dc;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.toast {
  font-size: 24rpx;
  line-height: 24rpx;
  color: #f56c6c;
  margin: 15rpx 0;
}

::v-deep .custom-datetime-picker .u-popup__content {
  border-top-left-radius: 10rpx; /* 左上角圆角 */
  border-top-right-radius: 10rpx; /* 右上角圆角 */
  overflow: hidden; /* 确保圆角有效 */
}
</style>
