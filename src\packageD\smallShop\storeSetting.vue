<template>
  <view class="store-setting-view">
    <u--form labelPosition="left" :model="formData">
      <view class="m-card-view mt_20">
        <view class="m-card-title d-bf mb_20">
          <text>基本信息</text>
        </view>
        <u-form-item label="店铺名称" labelWidth="160rpx" prop="name">
          <u--input
            v-model="formData.shop_name"
            placeholder="请输入名称"
          ></u--input>
        </u-form-item>
        <view class="pl_160">
          <text class="c-8a font_size12">默认为: 昵称的小商店</text>
        </view>
        <u-form-item prop="name">
          <view class="d-f">
            <text class="mr_30">店主二维码</text>
            <u-upload
              width="140rpx"
              height="140rpx"
              accept="image"
              maxCount="1"
              maxSize="10485760"
              name="1"
              multiple
              :fileList="qrCodeImages"
              :previewFullImage="true"
              @afterRead="afterRead"
              @delete="deletePic"
            >
              <view class="qr-code-view">
                <u-icon name="plus"></u-icon>
              </view>
            </u-upload>
          </view>
        </u-form-item>
      </view>
      <view class="m-card-view mt_20">
        <view class="m-card-title d-bf mb_20">
          <text>首页设置</text>
        </view>
        <u-form-item prop="name">
          <view class="d-f">
            <text class="mr_30">列表数据</text>
            <view>
              <u-checkbox-group
                v-model="checkGroup.special_album"
                placement="column"
                size="40"
                activeColor="#f14e4e"
              >
                <view class="d-cf mb_10">
                  <u-checkbox name="1"></u-checkbox>
                  <text class="font_size14">特卖专辑</text>
                </view>
                <text class="c-8a font_size12 mb_20 ml_50">
                  显示导入的共享商品专辑
                </text>
              </u-checkbox-group>
              <u-checkbox-group
                v-model="checkGroup.jhcps"
                placement="column"
                size="40"
                activeColor="#f14e4e"
              >
                <view class="d-cf mb_20">
                  <u-checkbox name="1"></u-checkbox>
                  <text class="font_size14">聚推联盟</text>
                </view>
              </u-checkbox-group>
              <!-- 卡券中心暂时不显示 -->
              <!-- <u-checkbox-group
                v-model="checkGroup.card_center"
                placement="column"
                size="40"
                activeColor="#f14e4e"
              >
                <view class="d-cf mb_20">
                  <u-checkbox name="1"></u-checkbox>
                  <text class="font_size14">卡券中心</text>
                </view>
              </u-checkbox-group> -->
              <u-checkbox-group
                v-model="checkGroup.meituan_group"
                placement="column"
                size="40"
                activeColor="#f14e4e"
              >
                <view class="d-cf mb_20">
                  <u-checkbox name="1"></u-checkbox>
                  <text class="font_size14">美团团购</text>
                </view>
              </u-checkbox-group>
              <u-checkbox-group
                v-model="checkGroup.douyin_group"
                placement="column"
                size="40"
                activeColor="#f14e4e"
              >
                <view class="d-cf mb_20">
                  <u-checkbox name="1"></u-checkbox>
                  <text class="font_size14">抖音团购</text>
                </view>
              </u-checkbox-group>
              <u-checkbox-group
                v-model="checkGroup.all_product"
                placement="column"
                size="40"
                activeColor="#f14e4e"
              >
                <view class="d-cf mb_20">
                  <u-checkbox name="1"></u-checkbox>
                  <text class="font_size14">全部商品</text>
                </view>
              </u-checkbox-group>
              <u-checkbox-group
                v-model="checkGroup.hot_product"
                placement="column"
                size="40"
                activeColor="#f14e4e"
              >
                <view class="d-cf mb_10">
                  <u-checkbox name="1"></u-checkbox>
                  <text class="font_size14">热销商品</text>
                </view>
                <text class="c-8a font_size12 mb_20 ml_50">按销量排序</text>
              </u-checkbox-group>
              <u-checkbox-group
                v-model="checkGroup.new_product"
                placement="column"
                size="40"
                activeColor="#f14e4e"
              >
                <view class="d-cf mb_10">
                  <u-checkbox name="1"></u-checkbox>
                  <text class="font_size14">新品上线</text>
                </view>
                <text class="c-8a font_size12 mb_20 ml_50">
                  按加入小商店最新显示
                </text>
              </u-checkbox-group>
              <u-checkbox-group
                v-model="checkGroup.first_category"
                placement="column"
                size="40"
                activeColor="#f14e4e"
              >
                <view class="d-cf mb_20">
                  <u-checkbox name="1"></u-checkbox>
                  <text class="font_size14">一级分类</text>
                </view>
              </u-checkbox-group>
            </view>
          </view>
        </u-form-item>
      </view>
    </u--form>
    <view class="save-btn mt_20" @click="onSave">保存</view>
  </view>
</template>
<script>
export default {
  data() {
    return {
      id: null,
      qrCodeImages: [],
      remarkImages: [],
      checkGroup: {
        special_album: [1], // 特卖专辑
        all_product: [], // 全部商品
        hot_product: [], // 热销商品
        new_product: [], // 新品上线
        first_category: [], // 新品上线
        jhcps: [], // 聚推联盟
        card_center: [], // 卡券中心
        douyin_group: [], // 抖音团购
        meituan_group: [], // 美团团购
      },
      formData: {
        shop_name: '', // 店铺名称 更新时同时更新小商店名称
        qr_code: '', // 店主二维码
        // special_album: 0, // 特卖专辑
        // all_product: 0, // 全部商品
        // hot_product: 0, // 热销商品
        // new_product: 0, // 新品上线
        // first_category: 0, // 新品上线
      },
    }
  },
  onLoad() {
    this.onLoadSetting()
  },
  onShow() {
    /* this.user = uni.getStorageSync("user");
    // 未登录
    if (!this.checkNull(this.user)) {
      this.navTo("/pages/login/login");
    } */
  },
  methods: {
    // 新增图片
    async afterRead(event) {
      // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
      const lists = [].concat(event.file)
      let fileListLen = this.qrCodeImages.length
      lists.map(item => {
        this.qrCodeImages.push({
          ...item,
          status: 'uploading',
          message: '上传中',
        })
      })
      for (let i = 0; i < lists.length; i++) {
        const result = await this.uploadFilePromise(lists[i].url)
        const item = this.qrCodeImages[fileListLen]
        this.qrCodeImages.splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: 'success',
            message: '',
            url: result,
          }),
        )
        fileListLen++
      }
    },
    // 删除图片
    deletePic(event) {
      this.qrCodeImages.splice(event.index, 1)
      this.remarkImages.splice(event.index, 1)
      console.log('deletePic this.qrCodeImages', this.qrCodeImages)
      console.log('deletePic this.remarkImages', this.remarkImages)
    },
    uploadFilePromise(url) {
      return new Promise((resolve, reject) => {
        const a = uni.uploadFile({
          url: this.api.host + '/api/common/upload',
          filePath: url,
          name: 'file',
          success: res => {
            setTimeout(() => {
              resolve(res.data.data)
              const data = JSON.parse(res.data)
              this.remarkImages.push(data.data.file.url)
              this.formData.qr_code = this.remarkImages[0]
            }, 1000)
          },
        })
      })
    },
    onSave() {
      Object.keys(this.checkGroup).forEach(key => {
        if (this.checkGroup[key].length > 0) {
          this.formData[key] = 1
        }
        if (this.checkGroup[key].length === 0) {
          this.formData[key] = 0
        }
      })
      const params = {
        value: this.formData,
      }
      if (this.id) {
        params.id = this.id
      }
      if (this.qrCodeImages.length === 0) {
        params.value.qr_code = ''
      }
      this.put('/api/smallShop/setting/updateShopSetting', params, true)
        .then(res => {
          if (res.code === 0) {
            setTimeout(() => {
              this.toast(res.msg)
            }, 500)
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    // 获取店铺设置
    async onLoadSetting() {
      const res = await this.get(
        '/api/smallShop/setting/getShopSetting',
        {},
        true,
      )
      const setting = res.data.setting
      this.id = setting.id
      this.formData = { ...setting.value }
      this.qrCodeImages = []
      if (setting.value.qr_code) {
        this.qrCodeImages.push({
          url: setting.value.qr_code,
        })
      }
      // console.log('this.qrCodeImages', this.qrCodeImages)
      // console.log('this.formData', this.formData)
      Object.keys(this.formData).forEach(key => {
        if (this.formData[key] === 1) {
          this.checkGroup[key] = ['1']
        }
      })
      // console.log('this.checkGroup', this.checkGroup)
    },
  },
}
</script>
<style lang="scss" scoped>
.store-setting-view {
  padding: 0 30rpx;
  .save-btn {
    width: 100%;
    height: 82rpx;
    line-height: 82rpx;
    border-radius: 10rpx;
    background-color: rgba(255, 102, 102, 100);
    font-size: 28rpx;
    color: #fff;
    text-align: center;
    margin-bottom: 32rpx;
  }
}
.qr-code-view {
  min-width: 140rpx;
  min-height: 140rpx;
  max-width: 140rpx;
  max-height: 140rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #b4b4b4;
  border-radius: 10rpx;
  ::v-deep .u-icon .uicon-plus {
    color: #b4b4b4 !important;
    font-size: 80rpx !important;
  }
}
</style>
