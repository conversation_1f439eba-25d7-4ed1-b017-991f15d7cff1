<template>
  <u-checkbox-group placement="column" @change="onCheckSome">
    <template v-for="item in albumsData">
      <view class="item-album" :key="item.id">
        <view class="relative">
          <view class="checkbox">
            <u-checkbox size="30" :key="item.id" :name="item.id"></u-checkbox>
          </view>
          <view class="f">
            <view>
              <image
                class="thumbnail"
                v-if="item.covers[0].src"
                :src="item.covers[0].src"
                @click="goAlbumDetail(item.id, item)"
              ></image>
            </view>
            <view class="f1">
              <view class="mb_12 con-tit" @click="goAlbumDetail(item.id, item)">
                <template v-if="item.name.length > 20">
                  <text class="thumbtitle">
                    {{ item.name.slice(0, 20) + '...' }}
                  </text>
                </template>
                <template v-else>
                  <text class="thumbtitle">{{ item.name }}</text>
                </template>
              </view>
              <view class="con-tag-2 f fac fw">
                <view class="mr_10 mb_10" v-for="(member,index) in item.relations" :key="index">
                  <u-tag
                    v-if="member.tag.name"
                    :key="member.tag_id"
                    :text="member.tag.name"
                    type="warning"
                    size="small"
                  ></u-tag>
                </view>
              </view>
            </view>
          </view>
          <view class="con-count">
            <view class="item-count">
              <text class="count-num">{{ item.product_count }}</text>
              <text class="count-txt">商品数量</text>
            </view>
            <view class="item-count">
              <text class="count-num">{{ item.import_count }}</text>
              <text class="count-txt">累计导入</text>
            </view>
            <view class="item-count">
              <text class="count-num">{{ item.browse_count }}</text>
              <text class="count-txt">累计访问</text>
            </view>
            <view class="item-count">
              <text class="count-num">{{ item.sales_total }}</text>
              <text class="count-txt">累计销量</text>
            </view>
          </view>
          <view>
            <u-row
              justify="space-between"
              gutter="10"
              customStyle="margin-top:30rpx; margin-left:-6rpx"
            >
              <u-col span="10">
                <template v-if="item.describe.length > 18">
                  <text class="c-959595">
                    {{ item.describe.slice(0, 18) + '...' }}
                  </text>
                </template>
                <template v-else>
                  <text class="c-959595">{{ item.describe }}</text>
                </template>
              </u-col>
              <u-col span="2">
                <template v-if="currentAlbumIndex === 0">
                  <view
                    class="font_size12 c-8a d-ef fontw500 c-66A3E7"
                    @click="onDelete(item.id)"
                  >
                    删除
                  </view>
                </template>
                <template v-if="currentAlbumIndex === 1">
                  <view
                    class="font_size12 c-8a d-ef fontw500 c-66A3E7"
                    @click="onClickImportSingle(item.id)"
                  >
                    导入
                  </view>
                </template>
              </u-col>
            </u-row>
          </view>
        </view>
      </view>
    </template>
  </u-checkbox-group>
</template>
<script>
export default {
  name: 'AlbumList',
  props: {
    currentAlbumIndex: {
      type: Number,
      default: 0,
    },
    albumsData: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    onCheckSome(element) {
      this.$emit('onCheckSome', element)
    },
    onClickImportSingle(id) {
      // 单独导入
      this.$emit('onClickImportSingle', id)
    },
    onDelete(id) {
      this.$emit('onDelete', id)
    },
    goAlbumDetail(id, item) {
      this.navTo(
        '/packageD/smallShop/albumDetail?id=' +
          id
      )
    },
  },
}
</script>
<style lang="scss" scoped>
// 专辑列表 start
.item-album {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: white;
}
.relative {
  position: relative;
}
.checkbox {
  position: absolute;
  top: 5rpx;
  right: 0;
}
.f {
  display: flex;
}
.thumbnail {
  margin-right: 30rpx;
  width: 213rpx;
  height: 120rpx;
}
.thumbtitle {
  line-height: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
}
.con-count {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
  .item-count {
    text-align: center;
    &:not(:last-child) {
      margin-right: 30rpx;
    }
    .count-num {
      font-size: 40rpx;
      color: #f11111;
    }
    .count-txt {
      margin-top: 10rpx;
      color: #444;
    }
  }
}
// 专辑列表 end
</style>
