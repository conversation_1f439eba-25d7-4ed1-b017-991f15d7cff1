<!-- 商品评论详情页面 原型图没有对应页面 -->
<template>
	<view style="overflow:hidden">
		<view class="bg-white">
			<view class="p-20">
				<view class="d-bf">
					<view class="d-f">
						<u-avatar :src="list.avatar" size="70"></u-avatar>
						<view class="ml-15">
							<view class="fs-1">{{list.nickname}}</view>
							<view style="color: #a3a3a3;" class="fs-1-5 mt_10">{{list.user.user_level.name}}</view>
						</view>
					</view>
					<view class="c-7b7b7b">{{list.created_at}}</view>
				</view>
				<view class="mt-20 fs-1-5">{{list.content}}</view>
				<view class="mt-15 d-f">
					<view v-for="(item,index) in list.imageUrls" :key="index" :class="index == 0?'':'ml_15'">
						<u--image :src="item" width="130rpx" height="130rpx" radius="8" @click="imgListPreview(item)">
						</u--image>
					</view>
				</view>
				<view class="hr"></view>
				<view class="mt-10 d-f">
					<text class="fs-1 d-cc">评分:</text>
					<uni-rate :readonly="true" v-model="list.level" :size="21" active-color="red" />
				</view>
			</view>
			<view style="background-color: #fafafa;" class="p-20 d-bf pr_35 mb-20 mt-5"
				@click="navTo('/packageA/commodity/commodity_details/commodity_details?id='+list.productId)">
				<view class="d-f">
					<view>
						<u--image :src="list.order_item.image_url" width="150" height="150"></u--image>
					</view>
					<view class="ml-15">
						<view class="fs-1-5">{{list.order_item.title}}</view>
						<view class="fs-0-5 mt-20" style="color:#a3a3a3">规格：{{list.order_item.sku_title}}</view>
					</view>
				</view>
				<view style="margin-top: -8%;">
					<view class="fs-1-5">¥{{list.order_item.amount}}</view>
					<view class="mt-20 fs-0 d-ef">{{list.order_item.qty}}</view>
				</view>
			</view>
		</view>
		<!-- 全部评论 -->
		<view style="background-color: white;margin-bottom:250rpx;" class="mt-20 p-15">
			<view>
				<view class="mt-20">
					<text class="iconfont icon-weixinliaotian"></text><text class="ml-5">全部评论</text>
				</view>
				<view class="hr"></view>
				<view class="p-20 pl_35">
					<view class="d-bf">
						<view class="d-f">
							<u-avatar :src="list.avatar" size="70"></u-avatar>
							<view class="ml-15">
								<view class="fs-1">{{list.nickname}}</view>
								<view style="color: #a3a3a3;" class="fs-1 mt_5">{{list.user.user_level.name}}</view>
							</view>
						</view>
						<view class=" c-7b7b7b">{{list.created_at}}</view>
					</view>
					<view class="mt-20 fs-1-5">{{list.content}}</view>
					<view class="hr"></view>
				</view>
				<view class="d-bf">
					<view class="c-7b7b7b">{{list.created_at}}</view>
					<view style="border:solid #bfcbd9 1rpx;width: 100rpx;" class="radius30 d-cc"
						@click="replyingToObjectChanges(list)">回复</view>
				</view>
			</view>
			<!-- 子评论列表 -->
			<view style="background-color: #efedf5;" class="mt-20 pt-20 pl_80 pr_60 pb_20"
				v-if="checkNull(childCommentsArry)">
				<view v-for="(item,index) in childCommentsArry" :key="index" class="mt-20">
					<view class="d-bf">
						<view class="d-f">
							<u-avatar :src="item.avatar" size="70"></u-avatar>
							<view class="ml-15 d-cc">
								<view class="fs-1">{{item.nickname}}</view>
							</view>
						</view>
						<view class=" c-7b7b7b">{{formatDateTime(item.created_at,6)}}</view>
					</view>
					<view class="mt-20 fs-1-5">{{item.nickname}}回复{{item.parent_comment.nickname}}:{{item.content}}
					</view>
					<view class="d-bf mt-45">
						<view class="c-7b7b7b">{{formatDateTime(item.created_at,6)}}</view>
						<view style="border:solid #bfcbd9 1rpx;width: 100rpx;" class="radius30 d-cc"
							@click="replyingToObjectChanges(item)">回复</view>
					</view>
					<view class="mt-40">
						<view class="hr"></view>
					</view>
				</view>
			</view>
		</view>
		<!-- 底部按钮 -->
		<view class="btn d-f" style="background-color: #FFFFFF;">
			<view class="d-f">
				<u--input :placeholder="'回复@'+name+':'" border="surround" 
					v-model="value">
				</u--input>
				<view class="ml-15 d-cc" @click="reply()">
					<u-button text="提交" type="error" style="height: 70rpx;"></u-button>
				</view>
			</view>
			<view>
				<button style="color: #7a7a7a;font-size: 28rpx;" class="ml-20" open-type="share"
					@click="$store.commit('upShadeShow',true)">分享</button>
			</view>
		</view>
		<shade></shade>
	</view>
</template>

<script>
	import shade from "../../../../common/shade/shade.vue";
	export default {
		components: {
			shade,
		},
		data() {
			return {
				user: {}, //用户信息
				list: {}, //单个详情信息
				value: '', //回复框信息
				name: '', // 被回复的用户名称
				replyId: '', //被回复对象的id  
				childCommentsArry: '', //子评论列表
				childComments: { //子评论参数
					page: 1,
					pageSize: 999
				},
			}
		},
		onLoad(opation) {
			this.list = JSON.parse(decodeURIComponent(opation.item))
			this.list.order_item.amount = this.toYuan(this.list.order_item.amount)
			this.list.id = parseInt(this.list.id)
			this.replyId = parseInt(this.list.id)
			this.name = this.list.nickname
			this.getSubComments()
			this.getIndex()
		},
		onShareAppMessage() { //分享
			let str = ''
            if (uni.getStorageSync('user')) {
              let pid = parseInt(uni.getStorageSync('user').id);
              str = "&invite_code=" + pid
            }
			return {
				title: "评论详情",
				path: '/packageB/goods/commentDetails/evaluationDetails/evaluationDetails?item=' + this.list + str
			}
		},
		onShareTimeline() { // 分享朋友圈
			let query = ''
            if (uni.getStorageSync('user')) {
              let pid = parseInt(uni.getStorageSync('user').id);
              query = "&invite_code=" + pid
            }
			return {
				title: "评论详情",
				path: '/packageB/goods/commentDetails/evaluationDetails/evaluationDetails?item=' + this.list,
				query
			};
		},
		onUnload() {
			this.$store.commit('upShadeShow', false)
		},
		methods: {
			reply() { //提交回复
				if (this.value == '') {
					this.showText("请输入回复内容")
					return;
				}
				let json = {
					avatar: this.user.avatar,
					nickname: this.user.nickname,
					commentId: parseInt(this.replyId),
					content: this.value,
					first_comment_id: parseInt(this.list.id),
					type: 2
				}
				if (!this.checkNull(this.user.nickname)) { //如果用户没有设置名称就传账号
					json["nickname"] = this.user.username
				}
				this.post('/api/comment/createComment', json, true).then(data => {
					this.getSubComments()
					this.value = ''
					this.showText("回复成功")
				})
			},
			getSubComments() { //获取子评论
				this.get('/api/comment/getCommentByCommentId?page=' + this.childComments.page + '&pageSize=' + this
					.childComments.pageSize + '&commentId=' + this.list.id, {}, true).then(data => {
					this.childCommentsArry = data.data.list
				})
			},
			getIndex() { //获取用户最新头像和昵称
				this.post('/api/center/index', {}, true).then(data => {
					this.user = data.data.user
				})
			},

			replyingToObjectChanges(item) { //回复对象更改
				this.name = item.nickname
				this.replyId = item.id
			}
		}
	}
</script>

<style scoped>
	.btn {
		/* 返回顶部绝对定位*/
		position: fixed;
		padding: 20rpx;
		width: 100%;
		bottom: 0rpx;
	}

	.hr {
		background-color: #e8e8e8;
		height: 1rpx;
		width: 100%;
		margin-top: 25rpx;
	}

	::v-deep .u-input{
		width: 450rpx;
	}

	button::after {
		border: none;

	}
	button {
		background-color: #fff;
		border-radius: 0;
	}
</style>
