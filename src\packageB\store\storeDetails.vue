<!-- 店铺详情页面 -->
<template>
	<view>
		<!-- 搜索框 -->
		<view class="d-bf pl_23 pt_14 pb_14">
			<view class="d-cc">
				<view class="iconfont icon-member-left fs-0" style="color: #747474" @tap="navigateBack">
				</view>
				<view class="ml-15">
					<view>
						<view style="width:665rpx;" class="d-bf">
							<u-search height="60" bgColor="#eff0f1" color="#666666" v-model="title" searchIconSize="40"
								:showAction="false" placeholder="请搜索商品名称" @click="goSearch" :disabled="true">
							</u-search>
							<view class="iconfont icon-all_more  pl_20"
								@click="avigationBarPopups == true?avigationBarPopups = false:avigationBarPopups = true">
							</view>
						</view>
					</view>
				</view>
				<view class="d-cc-c" style="z-index: 99;position: absolute;top:50rpx;right: 5rpx;"
					v-show="avigationBarPopups">
					<view class="mydiv ml-15"></view>
					<view class="p-15 popupCss">
						<view class="d-cc mb_5" v-for="(item,index) in navigationJump" :key="index"
							@click="navigationBarJump(item)">
							<text :class="item.icon" style="font-size: 18rpx;margin-right: 5rpx;"></text>{{item.name}}
						</view>
					</view>
				</view>
			</view>
		</view>
		<!--  -->
		<!-- 背景 -->
		<view class="bg">
		</view>
		<view class="d-cc" style="margin-top: -14%;z-index: 99;">
			<u--image mode="aspectFit" :src="isShow?list3.logo_src:list2.shop_logo" width="300" height="150">
			</u--image>
		</view>
		<!--  -->
		<!-- 店铺内容 -->
		<view class="d-cc-c pl-30 pr-30 bg-white" style="margin-top: -5.7%;">
			<view style="font-size: 38rpx;line-height: 48rpx;" class="fw-b mt_70" :class="isShow?'mb-30':''">
				{{isShow?list3.welcome:list2.name}}
			</view>
			<view class="d-cc mt_31 mb_40" v-if="!isShow">
				<view class="fs-0-5 d-cc-c">
					<view class="c-f14e4e">{{list2.goods_count}}</view>
					<view class="c-666666 mt_12">商品数量</view>
				</view>
				<view class="d-cc fs-0 collectCss ml-45" @click="enshrine" v-if="collectClick">
					<view class="iconfont icon-fontclass-pingfen" style="font-size: 10rpx;">
					</view>
					<view class="ml_7">
						{{collectClick}}
					</view>
				</view>
			</view>
			<view v-if="!isShow&&content!=''&&checkNull(content)">
				<view class="fs-1-5 c-333333 d-cc mb-20">
					店铺公告
				</view>
				<view style="overflow: hidden;width: 670rpx;"
					:style="notice.value == false?'height:38px':'height: 100%'" class="mb-20">
					<rich-text :nodes="content" ref="dv1" ></rich-text>
				</view>
				<view @click="announcement" class="d-cc fs-1 mb-20" style="color: #9b9b9b;" v-if="bulletinSeeMore"><text
						class="mr-5">{{notice.aSwitch}}</text>
					<u-icon :name="notice.icon" color="#9b9b9b" size="25"></u-icon>
				</view>
			</view>
		</view>
		<!--  -->
		<!-- 分类 -->
		<view class="bg-white mt_19 pl_15">
			<u-tabs :list="list1" lineWidth="0" lineHeight="0" :activeStyle="{color: '#f14e4e',fontWeight: 'bold'}"
				:inactiveStyle="{color: '#2f2f2f'}" @click="click"></u-tabs>
		</view>
		<view>
			<!-- 商品 -->
			<view v-show="item == 'screeningOfGoods'?true:false">
				<screeningOfGoods :list="list4" :user="user" @pMerchandiseNews="merchandiseNews"></screeningOfGoods>
			</view>
			<!--  -->
			<!-- 分类 -->
			<view v-show="item == 'classifyModule'?true:false">
				
				<!-- <classifyModule :supplier_id="id" :url="`/api/supplier/getSupplierProductCategoriesAll?supplier_id=${id}`" type="store">
				</classifyModule> -->
			
				<classifyModule 
					:gather_supply_id="id" 
					:url="`/api/supplier/getStoreCategoriesAll?id=${id}&type=${parseInt(type)}&pid=${pid}&level=1`" 
					type="store"
				>
				</classifyModule>
			</view>
			<!--  -->
			<!-- 店铺 -->
			<view v-show="item == 'storesDescription'?true:false">
				<view class="bg-white m-20 p-20 radius10 fs-2 c-5e5e5e">
					<view v-for="(item,index) in list" :key="index" class="d-f" :class="index == 0?'':'mt_37'">
						<view>{{item.name}}</view>
						<progress :percent="item.value*20" active :stroke-width="6"
							style="width: 300rpx;margin-left: 30rpx;" border-radius="30" activeColor="#f14e4e" />
						<view class="d-cc c-f14e4e ml_17">{{item.value}}</view>
					</view>
				</view>
				<view class="bg-white m-20 p-20 radius10">
					<view class="c-666666 fs-1-5">退货说明</view>
					<view class="c-333333 fs-2 mt_24">
						<rich-text :nodes="ReturnInstructions"></rich-text>
					</view>
				</view>
				<view class="bg-white m-20 p-20 radius10">
					<view class="c-666666 fs-1-5">店铺资质</view>
					<view v-for="(item,index) in list6" :key="index" class="mt-20">
						<!-- <view class="mt_23 fs-1 c-333333">{{item.name}}</view> -->
						<view class="d-f">
							<view class="mt_19 flow ml-20" v-for="(item1,index1) in item.imgs" :key="index1">
								<image :src="item1" @click="largeIcon(item.imgs,index1)" mode="widthFix">
								</image>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- -->
		</view>
		<!--  -->
	</view>
</template>

<script>
	import screeningOfGoods from "../../common/screeningOfGoods/screeningOfGoods.vue";
	import classifyModule from "../../common/classifyModule/classifyModule.vue";
	export default {
		components: {
			screeningOfGoods,
			classifyModule
		},
		data() {
			return {
				homeNoMore: false,
        user:{},
				id: '', //店铺id
				pid:0,
				type: '', //店铺类型：0供应商，1供应链，2自营（type不是1不能收藏店铺）
				level:1,
				isShow: false, //是否是平台
				content: "", //公告内容
				bulletinSeeMore:false,//公告查看更多是否显示
				ReturnInstructions: '', //退货说明
				avigationBarPopups: false, //导航栏弹窗
				title: '', //搜索输入框的值
				notice: {
					aSwitch: '展开',
					icon: 'arrow-up',
					value: false
				},
				showHeight: 200,

				collectClick: '', //是否已经收藏
				tagStyle: {
					p: 'color: #606266; line-height: 24px;'
				},
				navigationJump: [ //导航栏跳转
					{
						name: "首页",
						icon: "iconfont icon-fontclass-shouye",
						type: 'tabBar',
						url: "/pages/index/index",
					},
					{
						name: "我的",
						icon: "iconfont icon-my",
						type: 'tabBar',
						url: "/pages/membercenter/membercenter"
					},
					{
						name: "订单",
						icon: "iconfont icon-shangchengdingdan",
						type: 'default',
						url: "/packageA/myOrder/myOrder"
					},
				],
				list: [], //店铺评分
				list1: [{
					name: '商品',
				}, {
					name: '分类',
				}],
				list2: {}, //店铺信息
				list3: {}, //平台信息
				list4: [], //商品信息
				list5: {}, //总共有多少页 和当前页
				list6: [], //资质信息
				item: 'screeningOfGoods', //screeningOfGoods(商品)storesDescription(店铺)
			}
		},
		async onLoad(opation) {
			this.id = opation.id
			this.type = opation.type
			await this.storeInformation(opation.id)
			
			this.merchandiseNews()
			await this.frameworkValue();
			
			this.getSupplierQualifications(opation.id)
			if (opation.id == 0) { //如果id为0代表是平台
				this.isShow = true
			} else {
				this.$set(this.list1, 2, {
					name: '店铺'
				})
				if(this.user) this.collectState(opation.id)
				setTimeout(this.getHeight,120) //获取公告高度
			}
			this.$store.commit("upHomePage", { //还原筛选状态
				page: 1,
				pageSize: 5,
				category1_id: '',
				sort_by: '',
			})
			//修改浏览器标题
			let navTitle = this.isShow?this.list3.welcome:this.list2.shop_name;
			uni.setNavigationBarTitle({
				title:navTitle
			})
			
			
		},
		onShow() {
      this.user = uni.getStorageSync('user')
		},
		mounted() {
		},
		onReachBottom() {
			if (!this.homeNoMore) {
				this.merchandiseNews("refresh")
			}
		},
		methods: {
			navigateBack() {
				uni.navigateBack();
			},
			largeIcon(item,index) {
				uni.previewImage({
					current: index,
					indicator: "number",
					loop: true,
					urls: item
				})
			},
			getHeight(){
				this.$nextTick(() => {
					if(this.$refs.dv1.$el.offsetHeight>40){
						this.bulletinSeeMore = true
					}
				})
			},
			click(index, item) {
				if (index.name == "商品") {
					this.item = 'screeningOfGoods'
				} else if (index.name == "分类") {
					this.item = 'classifyModule'
				} else if (index.name == "店铺") {
					this.item = 'storesDescription'
				}
			},
			async storeInformation(id) { //店铺详情
				// const result =  this.get('/api/supplier/index?id=' + id, {}, true).then(data => {
				const result =  this.get(`/api/supplier/getStoreDetatilInformation?id=${this.id}&type=${this.type}`, {}, true).then(data => {
					this.content = data.data.supplier.supplier_setting.affiche.replace(/<img/g,
						'<img style="max-width:100%"')
					this.$set(this.list, 0, {
						name: "描述相符",
						value: data.data.supplier.describe_score == '' ? 0 : data.data.supplier
							.describe_score
					})
					this.$set(this.list, 1, {
						name: "卖家服务",
						value: data.data.supplier.service_score == '' ? 0 : data.data.supplier
							.service_score
					})
					this.$set(this.list, 2, {
						name: "物流服务",
						value: data.data.supplier.shopping_score == '' ? 0 : data.data.supplier
							.shopping_score
					})
					this.list2 = data.data.supplier;
					this.ReturnInstructions = this.list2.supplier_setting.explain.replace(/<img/g,
						'<img style="max-width:100%"')
				})
				return result;
			},
			async frameworkValue() { //获取平台信息,修改成同步在onLoad获取数据修改标题
				const result = await this.get('/api/home/<USER>', {}, true).then(data => {
					 this.list3 = data.data.header;
				})	
			},
			merchandiseNews(stair) { //商品信息展示
				this.$store.commit("upHomePage", {
					pageSize: 10
				})
				if (stair != "refresh") { //除了以外所有的筛选都是从第一页开始
					this.$store.commit("upHomePage", {
						page: 1
					})
				}
				let json = this.$store.state.homePage //要传的参数
				let apiUrl = "/api/product/list"
				if(this.user) apiUrl = "/api/product/listLogin"
				let url = apiUrl + "?page=" + json.page + '&pageSize=' + json.pageSize
				if (json.sort_by != '') { //判断用户是否选择了二级筛选
					url = "/api/product/list?page=" + json.page + '&pageSize=' + json.pageSize + '&sort_by=' + json
						.sort_by
				}
				if (this.title != '') { //判断用户是否输入框有值
					url = "/api/product/list?page=" + json.page + '&pageSize=' + json.pageSize + '&sort_by=' + json
						.sort_by + '&title=' + this.title
				}
				switch (this.type) {
					// 0=供应商
					case "0":
						url += '&supplier_id=' + this.id
						break;
					// 1=供应链
					case "1":
						url += '&gather_supply_id=' + this.id
						break;
					// 否则id就都是0
					default:
						url += '&supplier_id=0&gather_supply_id=0'
						break;
				}
				this.get(url, {}, true).then(data => {
					// for (var i = 0; i < data.data.list.length; i++) { //分转元
					// 	data.data.list[i].price = this.toYuan(data.data.list[i].price)
					// 	data.data.list[i].origin_price = this.toYuan(data.data.list[i].origin_price)
					// }
					this.list5 = data.data
					if (this.list5.page <= this.list5.total / this.list5.pageSize) { //判断是不是最后一页
						this.homeNoMore =  false
						this.$store.commit("upHomePage", {
							page: this.$store.state.homePage.page + 1
						})
					} else {
						this.homeNoMore =  true
					}
					if (stair == "refresh") { //只有在下拉刷新的时候数组才会一直叠加
						this.list4 = [...this.list4, ...data.data.list]
					} else {
						this.list4 = data.data.list
					}
				})
			},
			collectState(id) { //收藏状态
				id = parseInt(id)
				let json = {
					item_id: id,
					type: 2
				}
				this.post('/api/favorite/findFavorite', json, true).then(data => {
					if (data.data.enable == 1) {
						this.collectClick = '已收藏'
					} else {
						this.collectClick = '收藏'
					}
				})
			},
			getSupplierQualifications(id) { //获取店铺资质
				id = parseInt(id)
				this.get('/api/supplier/getSupplierQualifications?supplier_id=' + id, {}, true).then(data => {
					this.list6 = data.data
				})
			},
			enshrine() { //收藏
				let json = {
					item_id: parseInt(this.id),
					type: 2
				}
				this.post('/api/favorite/createFavorite', json, true).then(data => {
					if (data.data.enable == 1) {
						this.collectClick = '已收藏'
						this.showText('已收藏')
					} else {
						this.collectClick = '收藏'
						this.showText('已取消')
					}
				})
			},
			announcement() { //公告切换
				if (this.notice.value == false) {
					this.notice.value = true
					this.notice.aSwitch = "收起"
					this.notice.icon = "arrow-up"
				} else {
					this.notice.value = false
					this.notice.aSwitch = "展开"
					this.notice.icon = "arrow-down"
				}
			},
			goSearch() { //跳转到搜索详情页面
				this.navTo(`/packageA/search/search?supplier_id=${this.id}`);
			},
			navigationBarJump(item) { //导航栏跳转
				if (item.type == "tabBar") {
					this.tabTo(item.url)
				} else {
					this.navTo(item.url)
				}
			},
		}
	}
</script>

<style scoped>
	.bg {
		width: 750rpx;
		height: 170rpx;
		background-image: linear-gradient(0deg, #f14e4e 0%, #ec5e25 100%);
	}

	.collectCss {
		height: 30rpx;
		border-radius: 8rpx;
		border: solid 1rpx #f14e4e;
		color: #F14E4E;
		padding: 12rpx;
	}

	.mydiv {
		height: 0px;
		width: 0px;
		border-left: 12rpx solid transparent;
		border-right: 12rpx solid transparent;
		border-bottom: 12rpx #FFFFFF solid;
	}

	.popupCss {
		background-color: #FFFFFF;
		border-radius: 10rpx;
		color: #717471;
		font-size: 25rpx;
		margin-top: -1rpx;
	}

	/deep/.u-read-more__content[data-v-35272c51] {
		font-size: 30rpx;
	}
</style>
