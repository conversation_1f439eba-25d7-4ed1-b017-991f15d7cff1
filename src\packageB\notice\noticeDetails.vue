<!-- 公告详情 -->
<template>
	<view class="page">
		<view class="title">{{title}}</view>
		<view class="author">{{author}}</view>
		<u-parse :content="content"></u-parse>
		
		<view class="navigation">
			<view class="navIndex d-cc mb_20" @click="tabTo('/pages/index/index')">
				<view class="iconfont icon-fontclass-shouye" style="color: #FFFFFF;"></view>
			</view>
			<view class="navIndex d-cc mb_20" @click="backRefresh">
				<view class="iconfont icon-fontclass-jilu2" style="color: #FFFFFF;"></view>
			</view>
			<view  @click="toTop" class="btn">
				<view class="d-cc topcs">
					<view class="iconfont icon-fontclass-zhiding" style="color: #FFFFFF;"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				content:'',
				author:'',
				title:'',
				scrollTop:0,
				flage:false
			}
		},
		onLoad(options) {
			this.getArticleContent(options.id)
			this.getTheTitle();
		},
		onPageScroll(e) { //根据距离顶部距离是否显示回到顶部按钮
			this.flage = true;
		},
		methods: {
			getArticleContent(id){
				this.get('/api/article/detail?id='+id,{}, true).then(data => {
					let res = data.data;
					this.content = data.data?.rehotSearch.content;
					this.author = res.rehotSearch?.author;
					this.title = res.rehotSearch?.title;
				})
			},
			getTheTitle(){ //获取页面标题
				this.get('/api/home/<USER>', {}, true).then(data => {
					uni.setNavigationBarTitle({
						title: data.data.tab.title
					})
				})
			},
			toTop() {
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 200,
				});
			},
		}
	}
</script>
<style lang="scss">
	.page {
		height: 100%;
		background-color: #fff;
		padding: 0 62rpx;
		position: relative;
		.title {
			font-size: 36rpx;
			color: #333333;
			font-weight: bold;
			padding: 68rpx 0 50rpx 0;
		}
		.author {
			font-size: 24rpx;
			color: #919191;
			padding: 0 0 32rpx 0;
		}
		.navigation {
			position: fixed;
			right: 33rpx;
			bottom: 226rpx;
			.topcs {
				background-color: #999999;
				width: 70rpx;
				height: 70rpx;
				border-radius: 50%;
			}
			.navIndex {
				width: 70rpx;
				height: 70rpx;
				background-color: #999;
				font-size: 34rpx;
				border-radius: 50%;
			}
		}
		
	}
</style>
