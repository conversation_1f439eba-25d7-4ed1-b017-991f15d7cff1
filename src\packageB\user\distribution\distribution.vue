<!-- 招商 分销页面  因推广页面UI重构 此页面废弃 -->
<template>
	<view>
		<view class="ggbg"></view>
		<view style="color: #ffffff;margin-top: -400rpx;">
			<view class="p-20">
				<!-- 头像 名称 等级-->
				<view class="d-bf">
					<view class="d-f">
						<view>
							<u-avatar :src="user.avatar" size="90">
							</u-avatar>
						</view>
						<view class="ml-20">
							<view class="fw-b fs-3">{{user.nickname}}</view>
							<view class="mt-15 fs-1"><text v-if="pageType == 'distribution'">分销商</text>
								<text v-else>招商员</text>等级:{{centerInfo.level_name}}
							</view>
						</view>
					</view>
				</view>
				<!-- 消息 -->
				<view style="background-color:rgb(255,255,255,0.3);color:#4a4a4a" class="mt-30 p-20 fs-1 radius10">
					<view>
						{{centerInfo.shop_settle}}
					</view>
					<view class="mt-10">
						{{centerInfo.supplier_settle}}
					</view>
					<view class="mt-10">
						{{centerInfo.supply_settle}}
					</view>
				</view>
			</view>
		</view>
		<!--收益信息 -->
		<view class="bg-white m-20 radius15 flow d-f  pl_64 pb-30" style="margin-top: -3rpx;" >
			<view v-for="(item,index) in earningsFigures" :key="index" class="mt-40"
				style="width: 300rpx;height: 120rpx;margin-bottom: -30rpx;" >
				<view v-if="earningsFigures.length > 0">
					<view class="c-f14e4e d-cc fw-b fs-3" >
						{{ item.value | processing }}
					</view>
					<view class="mt-10 fs-1 d-cc">
						{{item.name}}
					</view>
				</view>
			</view>
		</view>
		<!-- 订单筛选 -->
		<view class="bg-white  radius15 m-20 p-20 selection">
			<view class="d-f mb_34">
				<view>
					<u--input 
						placeholder="订单号" 
						border="surround" 
						type="number" 
						v-model="filterData.orderNumber"
						fontSize="24rpx"
						placeholderStyle="font-size:24rpx;color: #858585;">
					</u--input>
				</view>
				<view class="ml_30">
					<u--input 
						:placeholder="pageType  == 'distribution'?'下单会员手机号/ID':'供应商名称'" 
						:type="pageType  == 'distribution'?'number':'text'" v-model="filterData.orderSupplier"
						border="surround"
						fontSize="24rpx"
						placeholderStyle="font-size:24rpx;color: #858585;">
					</u--input>
				</view>
			</view>
			<view class="d-f">
				<view>
					<uni-data-select 
						v-model="filterData.stateValue" 
						:localdata="range" 
						:clear="false"
						placeholder="分成状态"
						:class="pageType  == 'investment'?'mr_30': ''">
					</uni-data-select>
				</view>
				<view v-if="pageType  == 'distribution'" class="ml_30 mr_30 divided-type">
					<uni-data-select v-model="filterData.typeValue" :localdata="range1" :clear="false"
						placeholder="分成类型">
					</uni-data-select>
				</view>
				<view class="d-cc">
					<view style="background: #f14e4e;width: 110rpx;height: 56rpx;" class="radius10 c-white d-cc" @click="getResultData()">搜索</view>
				</view>
			</view>
			<view class="mt_34" style="color: #606060;">
				分成金额合计:<text class="c-f14e4e">{{centerInfo.settle_amount_total | processing}}元</text>
			</view>
		</view>
		<!-- 订单列表 -->
		<view class="bg-white  radius15 m-20 p-20" v-if="orderList.length !=0">
			<view v-for="(item,index) in orderList" :key="index" :class="index == 0?'':'mt-30'"
				@click="dividedIntoDetails(item)">
				<view class="d-bf">
					<view class="d-bf">
						<view>{{item.order_sn}}</view>
						<view class="c-aaaaaa font_size12 ml_12">{{item.settle_type_name}}</view>
					</view>
					
					<view class="c-f14e4e" >+{{item.amount | processing}}</view>
				</view>
				<view class="d-bf mt_28" style="color: #808080;">
					<view>{{item.created_at}}</view>
					<view>{{status[item.status]}}</view>
				</view>
				<view class="mt_30">
					<u-line color="#f0f0f0"></u-line>
				</view>
			</view>
		</view>
		<view class="d-cc fs-1 c-5e5e5e mb-25 mt-25">
			<view v-if="$store.state.homeNoMore&&orderList.length != 0">暂无更多~</view>
			<view v-if="orderList.length == 0">暂无数据~</view>
		</view>
		<!-- 订单详情弹窗 -->
		<view class="fs-1">
			<u-action-sheet :show="dividedShow" @close="dividedShow = false" round="30" title="分成详情">
				<view  class="d-bf p-20">
					<view style="color: #585858;">时间</view>
					<view style="font-family: SourceHanSansCN-Regular;" >{{dividedDetails.created_at}}</view>
				</view>
				<view  class="d-bf p-20">
					<view style="color: #585858;">订单号</view>
					<view style="font-family: SourceHanSansCN-Regular;" >{{dividedDetails.order_sn}}</view>
				</view>
				<view  class="d-bf p-20" v-if="dividedDetails.child_user_info">
					<view style="color: #585858;">下单会员</view>
					<view style="font-family: SourceHanSansCN-Regular;" >
						{{dividedDetails.child_user_info && dividedDetails.child_user_info.nickname || dividedDetails.child_user_info.username}}
					</view>
				</view>
				<view  class="d-bf p-20" v-if="dividedDetails.supplier_info">
					<view style="color: #585858;">供应商名称</view>
					<view style="font-family: SourceHanSansCN-Regular;" >
						{{dividedDetails.supplier_info && dividedDetails.supplier_info.name}}
					</view>
				</view>
				<view  class="d-bf p-20">
					<view style="color: #585858;">订单金额</view>
					<view style="font-family: SourceHanSansCN-Regular;" >
						{{dividedDetails.order_amount | processing}}
					</view>
				</view>
				<view  class="d-bf p-20">
					<view style="color: #585858;">分成基数</view>
					<view style="font-family: SourceHanSansCN-Regular;" >
						{{dividedDetails.settle_amount | processing}}
					</view>
				</view>
				<view  class="d-bf p-20">
					<view style="color: #585858;">分成比例</view>
					<view style="font-family: SourceHanSansCN-Regular;" >
						{{dividedDetails.ratio}}%
					</view>
				</view>
				<view  class="d-bf p-20">
					<view style="color: #585858;">分成金额</view>
					<view style="font-family: SourceHanSansCN-Regular;" class="c-f14e4e">
						+{{dividedDetails.amount | processing}}
					</view>
				</view>
				<view  class="d-bf p-20">
					<view style="color: #585858;">分成类型</view>
					<view style="font-family: SourceHanSansCN-Regular;" >
						{{dividedDetails.settle_type_name}}
					</view>
				</view>
				<view  class="d-bf p-20">
					<view style="color: #585858;">分成状态</view>
					<view style="font-family: SourceHanSansCN-Regular;" >
						{{status[dividedDetails.status]}}
					</view>
				</view>
				
			</u-action-sheet>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				pageType: '', //页面类型 招商/分销
				status: ["未结算", "已结算", "已失效"],
				dividedShow: false, //分成详情模态框是否弹出
				portUrl: '', //页面获取数据接口调用判断
				user: '', //最新用户信息
				ResultData: '', //招商和分销接口数据
				centerInfo: '', //订单描述 订单交易金额
				orderList: [], //订单列表
				earningsFigures: [ //收益数据
					{
						name: '累计支付订单金额（元）'
					},
					{
						name: '已完成订单金额（元）'
					},
					{
						name: '今日分成（元）'
					},
					{
						name: '累计分成（元）'
					},
					{
						name: '已结算分成（元）'
					},
					{
						name: '未结算分成（元)'
					}
				],
				range: [{ //状态下拉框筛选数据
						value: 0,
						text: "未结算"
					},
					{
						value: 1,
						text: "已结算"
					},
					{
						value: -1,
						text: "已失效"
					},
				],
				range1: [{ //分类下拉框筛选数据
						value: 1,
						text: "订单分成"
					},
					{
						value: 2,
						text: "采购技术服务分成"
					},
					{
						value: 3,
						text: "供应商扣点分成"
					},
				],
				dividedDetails:{},
				filterData: { //接口参数
					stateValue: '', //状态下拉框数据
					typeValue: '', //类型下拉框数据
					orderNumber: '', //订单号
					orderSupplier: '', //订单手机号 供应商名称
					page: 1,
					pageSize: 10,
				},
			}
		},
		onLoad(opations) {
			this.pageType = opations.type
			if (opations.type == 'distribution') { //动态修改导航栏标题
				uni.setNavigationBarTitle({
					title: '分销'
				})
			} else {
				uni.setNavigationBarTitle({
					title: '招商'
				})
			}

			this.getResultData()
			this.getUser()
		},
		filters:{
			processing(price) {
				return price? price : '0.00';
			}
		},
		onReachBottom() {
			if (!this.$store.state.homeNoMore) { //不是最后一页才获取商品列表
				this.getResultData("refresh")
			}
		},
		methods: {
			getResultData(stair) { //获取分销和招商页面数据
				if (stair != "refresh") { //除了下拉刷新以外所有的筛选都是从第一页开始
					this.filterData.page = 1
				}
				if (this.pageType == 'distribution') { //动态修改导航栏标题
					this.portUrl = `/api/distributor/getCenterInfo?page=${this.filterData.page}&pageSize=${this.filterData.pageSize}`;
				} else {
					this.portUrl = `/api/merchant/getCenterInfo?page=${this.filterData.page}&pageSize=${this.filterData.pageSize}`;
				}
				if(this.filterData.orderNumber) { //有值的时候才可以添加字段
					this.portUrl += `&order_sn=${this.filterData.orderNumber}`;
				}
				if(this.filterData.orderSupplier && this.pageType == 'investment') {
					this.portUrl += `&supplier_name=${this.filterData.orderSupplier}`;
				}
				if(this.filterData.orderSupplier && this.pageType == 'distribution') {
					this.portUrl += `&member=${this.filterData.orderSupplier}`;
				}
				if(this.filterData.typeValue && this.pageType == 'distribution') {
					this.portUrl += `&settle_type=${this.filterData.typeValue}`;
				}
				if(this.filterData.stateValue !== '') {
					this.portUrl += `&status=${this.filterData.stateValue}`;
				}
				this.get(this.portUrl, {}, true).then(data => {
					this.centerInfo = data.data.center_info
					this.centerInfo.settle_amount_total = this.toYuan(this.centerInfo.settle_amount_total);
					this.earningsFigures[0]["value"] = Number(this.toYuan(this.centerInfo.paid_amount_total));
					this.earningsFigures[1]["value"] = Number(this.toYuan(this.centerInfo.received_amount_total));
					this.earningsFigures[2]["value"] = Number(this.toYuan(this.centerInfo.today_amount_total));
					this.earningsFigures[3]["value"] = Number(this.centerInfo.settle_amount_total);
					this.earningsFigures[4]["value"] = Number(this.toYuan(this.centerInfo.finish_amount_total));
					this.earningsFigures[5]["value"] = Number(this.toYuan(this.centerInfo.wait_amount_total));
					this.ResultData = data.data;
					let orderLists = data.data.list;
					orderLists.forEach((item) => {
						item.created_at = this.formatDateTime(item.created_at,6);
						item.order_amount = this.toYuan(item.order_amount);
						item.settle_amount = this.toYuan(item.settle_amount);
						item.ratio = this.toYuan(item.ratio);
						item.amount = this.toYuan(item.amount);
					})
					if (data.data.page < data.data.total / data.data.pageSize) { //判断是不是最后一页
						this.$store.commit("upHomeNoMore", false)
						this.filterData.page++
					} else {
						this.$store.commit("upHomeNoMore", true)
					}
					if (stair == "refresh") { //只有在下拉刷新的时候数组才会一直叠加
						this.orderList = [...this.orderList, ...orderLists]
					} else {
						this.orderList = orderLists
						
					}

					
				})
			},
			getUser() { //获取最新用户信息
				this.post('/api/center/index', {}, true).then(data => {
					this.user = data.data.user
				})
			},
			dividedIntoDetails(item) { //分成详情
				this.dividedDetails = item;
				this.dividedShow = true
			}
		}
	}
</script>

<style scoped>
	.ggbg {
		/* 公告背景*/
		background-image: linear-gradient(#f15353, #f5f5f5);
		width: 100%;
		height: 400rpx;
	}
	.selection ::v-deep .uni-stat__select  {
		padding: 0;
		box-sizing: border-box;
		
	}
	.selection ::v-deep .uni-stat__select .uni-select {
		/* width: 240rpx; */
		box-sizing: border-box;
	}
	.selection ::v-deep .uni-stat__select .uni-select__input-box {
		
	}
	.selection ::v-deep .uni-select__selector-item {
		font-size: 24rpx;
	}
	::v-deep .uni-stat__actived {
		outline: none;
	}

	::v-deep .uni-select__input-text {
		width: 206rpx;
		font-size: 24rpx;
	}
	::v-deep .u-input {
		height: 40rpx;
		padding: 6rpx 18rpx!important;
	}

	::v-deep .uni-select {
		height: 56rpx;
	}
</style>
