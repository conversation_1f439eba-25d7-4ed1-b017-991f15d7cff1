<!-- 充值页 -->
<template>
  <view>
    <!--充值详情 -->
    <view class="pay-box">
      <view class="pay-money">
        <view class="top">充值金额:</view>
        <view class="mid">￥{{ amount || 0.0 }}</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      amount: 0,
      pay_type: '',
      pay_info_id: '',
      wx_openid: '', // 小程序ID
    }
  },
  onLoad(option) {
    this.amount = option.amount / 100
    this.pay_type = parseInt(option.pay_type)
    this.pay_info_id = parseInt(option.pay_info_id)
    if (!window.location.href.includes('code')) {
     uni.setStorageSync('ju_he_recharge', JSON.stringify({
        pay_type: this.pay_type,
        pay_info_id: this.pay_info_id
      }));
    }
    this.getOpenid()
  },
  methods: {
    // 获取openid
    async getOpenid() {      
      const windowUrl = window.location.href
      let pageHref = ''
      if (windowUrl.includes('code')) {
        pageHref = windowUrl
      } else {
        pageHref = windowUrl
      }
      let url = ''
      let codeValue = ''
      if (pageHref.includes('code')) {
        const indexHref = pageHref.lastIndexOf('code')
        const extension = pageHref
          .substring(indexHref, pageHref.length)
          .replace('&', ',')
        let arr = []
        arr = extension.split(',')
        codeValue = arr[0].match(/=(\S*)/)[1]
      }
      if (codeValue) {
        url =
          '/api/wechatofficial/getOpenid?url=' +
          pageHref.replace('#', '%23') +
          '&code=' +
          codeValue
      } else {
        url = '/api/wechatofficial/getOpenid?url=' + pageHref.replace('#', '%23')
      }
      this.get(url, {}, true).then(res => {
        const data = res.data
        if (data.code == 201) {
          // 还未获取code
          window.location.href = data.redirect_url
        } else {
          console.log('wqeqwewqeqweq', data);
          this.wx_openid = data.wechatData.wx_openid
          this.juHePay()
          
          // let windowUrlNew = window.location.href
          // if (windowUrlNew.includes('code')) {
          //   let urlParams = new URLSearchParams(window.location.search)
          //   urlParams.delete('code')
          //   urlParams.delete('state')
          //   window.location.search = urlParams.toString()
          // }
        }
      })
    },

    // 聚合充值
    async juHePay() {
      const juHePay = JSON.parse(uni.getStorageSync('ju_he_recharge'));
      const data = {
        amount: this.toFen(this.amount),
        pay_info_id: juHePay ? juHePay.pay_info_id : '',
        pay_type: juHePay ? juHePay.pay_type : '',
        wx_openid: this.wx_openid,
        // #ifdef H5
        type: 'M',
        // #endif
      }
      const res = await this.post('/api/finance/aggregatePaymentRechargeNotLogin', data)
      if (res.code === 0) {
        // #ifdef H5
        let json = {}
        if (this.wx_openid) {
          const config = {
            appId: res.data.fields.app_id,
            nonceStr: res.data.fields.nonce_str,
            signature: res.data.fields.pay_sign,
            timestamp: res.data.fields.time_stamp,
            jsApiList: ["chooseWXPay"] ,
            beta: false,
            debug: false,
            // url: herf,
          }
          console.log('wqewqeqwewqeqweqweqweq', config);
          jweixin.config(config)
          json = {
            appId: res.data.fields.app_id,
            nonceStr: res.data.fields.nonce_str,
            package:  res.data.fields.package,
            timestamp: res.data.fields.time_stamp,
            paySign: res.data.fields.pay_sign,
            signType: res.data.fields.sign_type,
          }
          jweixin.chooseWXPay({
            appId: json.appId,
            timestamp: json.timestamp, // 充值签名台生成签
            nonceStr: json.nonceStr, // 充值签名随机串，不长于 32 位
            package: json.package, // 统一充值接口返回的prepay_id参数值，提交格式如：prepay_id=***）
            signType: json.signType, // 签名方式，默认为'SHA1'，使用新版充值需传入'MD5'
            paySign: json.paySign, // 充值签名
            success: res => {
              // 充值成功后的回调函数
              if (res.errMsg == 'chooseWXPay:ok') {
                this.toast('充值成功')
                uni.navigateBack();
              }
            },
            cancel: res => {
              // 充值取消
              this.toast('充值取消')
              uni.navigateBack();
            },
            fail: res => {
              this.toast('充值失败')
              uni.navigateBack();
            },
          })
          return
        } else {
          window.location.href = res.data.fields.counter_url
        }
        // #endif
        // #ifdef MP-WEIXIN
        if(res.data.fields.miniapp_app_id && res.data.fields.jump_type === 2) {
          //微信小程序充值
          wx.navigateToMiniProgram({
          appId: res.data.fields.miniapp_app_id,
          path: res.data.fields.counter_url,
          envVersion: 'trial',
          // release: 正式版  trial: 体验版
          success(res) {
            // 打开成功
            console.log('打开成功')
            uni.redirectTo({
              url: '/packageB/wallet/wallet',
            })
          },
            fail(err) {
              console.log('打开失败', err)
            },
          })
        } else {
          uni.requestPayment({
            provider: 'wxpay',
            timeStamp: res.data.fields.time_stamp,
            nonceStr: res.data.fields.nonce_str,
            package: res.data.fields.package,
            signType: res.data.fields.sign_type,
            paySign: res.data.fields.pay_sign,
            success: function (res) {
              this.toast('充值成功')
              setTimeout(() => {
                uni.redirectTo({
                  url: '/packageB/wallet/wallet',
                })
              }, 1000)
            },
            fail: function (err) {
              this.toast('充值失败')
              setTimeout(() => {
                uni.redirectTo({
                  url: '/packageB/wallet/wallet',
                })
              }, 1000)
            },
          })
        }
        // #endif
      } else {
        this.toast(res.msg)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.pay-box {
  background: #fff;
  margin: 18rpx 32rpx;

  .pay-money {
    margin: 0 auto;
    padding: 40rpx 0;
    text-align: center;
    border-radius: 10rpx;

    .top {
      color: #999;
      font-size: 12px;
    }

    .mid {
      color: #f76d6d;
      font-size: 48rpx;
      margin: 32rpx 0;
    }

    .bottom {
      font-size: 12px;
      color: #999;
    }
  }
}

#payBtnList {
  margin: 0 32rpx 80rpx 32rpx;
  padding: 24rpx 0;
  border-radius: 10rpx;
  background-color: #fff;
  overflow: hidden;

  .title {
    color: #f87070;
    font-size: 28rpx;
    margin-bottom: 32rpx;

    .line {
      height: 28rpx;
      width: 6rpx;
      background-color: #f87070;
      display: inline-block;
      margin-right: 28rpx;
    }
  }

  .mod_btns {
    border-bottom: 1px solid #f4f4f4;
    text-align: left;
    margin: 0 10rpx;
    padding: 28rpx 0 28rpx 18rpx;

    .icon-pay_yue {
      font-size: 56rpx;
      color: #ff7433;
      margin-right: 32rpx;
    }

    .icon-pay_otherpay {
      font-size: 56rpx;
      color: #ffba00;
      margin-right: 32rpx;
    }

    .icon-pay_wechat {
      font-size: 56rpx;
      color: green;
      margin-right: 32rpx;
    }

    .icon-pay_remittance {
      font-size: 56rpx;
      color: #ff692f;
      margin-right: 32rpx;
    }

    .icon-pay_default {
      font-size: 56rpx;
      color: #2f9cff;
      margin-right: 32rpx;
    }

    .icon-pay_alipay {
      color: #29a1f7;
      font-size: 56rpx;
      margin-right: 32rpx;
    }
  }
}

.newSubTn {
  width: 80%;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  position: fixed;
  bottom: 24rpx;
  left: 10%;
  border-radius: 8px;
  background-color: #f15353;
  color: #fff;
}
</style>
