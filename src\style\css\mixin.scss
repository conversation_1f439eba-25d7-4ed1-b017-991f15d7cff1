@charset "utf-8";


@mixin font_size($num:28rpx) {
	font-size: $num;
}
//外边距的公共样式
@mixin mt_($num:1rpx) {
	margin-top: $num;
}
@mixin mr_($num:1rpx) {
	margin-right: $num;
}
@mixin mb_($num:1rpx) {
	margin-bottom: $num;
}
@mixin ml_($num:1rpx) {
	margin-left: $num;
}

//内边距的公共样式
@mixin pt_($num:1rpx) {
	padding-top: $num;
}
@mixin pr_($num:1rpx) {
	padding-right: $num;
}
@mixin pb_($num:1rpx) {
	padding-bottom: $num;
}
@mixin pl_($num:1rpx) {
	padding-left: $num;
}


@for $i from 1 through 20 {
	.font_size#{($i + 11)}{@include font-size(22rpx + ($i+$i));}
}

//外边距的公共样式
@for $j from 1 through 1000 {
	.mt_#{$j}{@include mt_($j+ rpx);}
}
@for $j from 1 through 1000 {
	.mr_#{$j}{@include mr_($j+ rpx);}
}
@for $j from 1 through 1000 {
	.mb_#{$j}{@include mb_($j+ rpx);}
}
@for $j from 1 through 1000 {
	.ml_#{$j}{@include ml_($j+ rpx);}
}

//内边距的公共样式
@for $j from 1 through 1000 {
	.pt_#{$j}{@include pt_($j+ rpx);}
}
@for $j from 1 through 1000 {
	.pr_#{$j}{@include pr_($j+ rpx);}
}
@for $j from 1 through 1000 {
	.pb_#{$j}{@include pb_($j+ rpx);}
}
@for $j from 1 through 1000 {
	.pl_#{$j}{@include pl_($j+ rpx);}
}
