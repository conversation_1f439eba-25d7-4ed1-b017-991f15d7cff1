<!-- 批量下单的编辑弹窗 -->
<template>
	<view>
		<u-popup :show="bulkShow" @close="bulkClose" @open="open" :round="20">
			<view class="popup-main">
					<view class="title d-cc">
						<view>编辑</view>
						<view class="title_close d-f" @click="bulkClose">
							<view class="iconfont icon-close11 "></view>
						</view>
					</view>
					<view class="content">
						<u--form
							labelPosition="left"
							:model="bulkForm"
							:rules="rules"
							ref="bulkForm"
								>
							<u-form-item
									label="下单号"
									prop="purchase_sn"
									ref="purchase_sn"
									labelWidth="240rpx"
									:required="true"
									style="font-size: 28rpx;"
							>
								<u--input
										v-model="bulkForm.purchase_sn"
										placeholder="请输入下单号"
										border="none"
								></u--input>
							</u-form-item>
							<u-form-item
									label="购买数量"
									prop="qty"
									ref="qty"
									labelWidth="240rpx"
									:required="true"
									style="font-size: 28rpx;"
							>
								<u--input
										v-model="bulkForm.qty"
										placeholder="请输入购买数量"
										border="none"
								></u--input>
							</u-form-item>
							<u-form-item
									label="收货人"
									prop="realname"
									ref="realname"
									labelWidth="240rpx"
									:required="true"
									style="font-size: 28rpx;"
							>
								<u--input
										v-model="bulkForm.realname"
										placeholder="请输入收货人姓名"
										border="none"
								></u--input>
							</u-form-item>
							<u-form-item
									label="手机号"
									prop="mobile"
									ref="mobile"
									labelWidth="240rpx"
									:required="true"
									style="font-size: 28rpx;"
							>
								<u--input
										v-model="bulkForm.mobile"
										placeholder="请输入手机号"
										border="none"
										style="font-size: 28rpx;"
										placeholder-style="text-align:right;font-size:28rpx"
								></u--input>
							</u-form-item>
							<u-form-item
									label="所在地区"
									prop="mobile"
									ref="mobile"
									labelWidth="200rpx"
									:required="true"
									style="font-size: 28rpx;"
							>
								<u-cell-group :border="false" >
									<u-cell @click="addressBtn" :border="false" :value="address" :isLink="true"></u-cell>
								</u-cell-group>
							</u-form-item>
							<u-form-item
									label="详细地址"
									prop="detail"
									ref="detail"
									labelWidth="240rpx"
									:required="true"
							>
							<u--input
									v-model="bulkForm.detail"
									placeholder="请输入详细地址"
									border="none"
							></u--input>
							</u-form-item>
						</u--form>
						
						<view class="bulk-group d-cc">
							<view class="bulk-btn" @click="bulkClose">取消</view>
							<view class="bulk-btn red-btn" @click="bulkConfirm">确定</view>
						</view>
					</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		name:"bulkPopup",
		props:{
			bulkForm:{
				type:Object,
				default:{}
			},
			address:{
				type:String,
				default:() => ""
			},
			bulkShow: {
				type:Boolean,
				default:false
			},
			addressShow: {
				type:Boolean,
				default:false
			}
		},
		data() {
			return {
				rules: {
					'realname':[
						{
							type: 'string',
							required: true,
							message: '请输入收件人姓名',
							trigger: ['blur']
						},
						{
							min: 2,
							max: 15,
							message: '长度在2-15个字符之间'
						},
					],
					'mobile': [
						{
							type: 'string',
							required: true,
							message: '请输入联系电话',
							trigger: ['blur']
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// uni.$u.test.mobile()就是返回true或者false的
								this.userShow = uni.$u.test.mobile(value);
								return uni.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['blur'],
						}
					],
					'province': {
						type: 'string',
						required: true,
						message: '请选择地区',
						trigger: ['blur']
					},
					'detail': {
						type: 'string',
						required: true,
						message: '请输入详细地址',
						trigger: ['blur']
					},
				},
			}
		},
		
		methods:{
			bulkClose() {
				this.$emit('bulksClose', this.bulkShow)
			},
			bulkConfirm() {
				this.$emit('bulksConfirm', this.bulkForm)
			},
			addressBtn() {
				console.log('11111111');
				console.log(this.addressShow);
				this.$emit('addressBtnOn', this.addressShow)
			}
		}
	}
</script>
<style scoped>
	.content ::v-deep  .u-cell .u-cell__body {
		padding: 0;
	}
	.content ::v-deep .u-form-item__body__right__message {
		text-align: right;
	}
	.content ::v-deep .u-form-item__body__right .u-form-item__body__right__content__slot {
		justify-content: flex-end;
	}
	.content ::v-deep .u-input__content__field-wrapper .u-input__content__field-wrapper__field {
		text-align: right!important;
		font-size: 28rpx!important;
	}
</style>
<style lang="scss" scoped>
	.popup-main {
		.title {
			padding: 40rpx 0 50rpx 0;
			position: relative;
			color: #202020;
			text-align: center;
			line-height: 36rpx;
			font-size: 34rpx;
		}
		.title_close {
			position: absolute;
			// width: 50rpx;
			height: 50rpx;
			right:30rpx;
			align-items: flex-end;
			// text-align: right;
		}
		.content {
			margin: 0 40rpx;
			padding-bottom: 28rpx;
			.bulk-group {
				margin-top: 72rpx;
				.bulk-btn {
					border-radius: 4rpx;
					border: solid 1rpx #a1a1a1;
					padding: 14rpx 100rpx;
					margin-right: 48rpx;
					font-size: 22rpx;
					position: relative;
					// &::before {
					// 	content: "";
					// 	position: absolute;
					// 	left:0;
					// 	top: 0;
					// 	width: 200%;
					// 	height: 200%;
					// 	border: 1px solid #a1a1a1;
					// 	/* 以（0,0）为放缩中心点 */
					// 	transform-origin: 0  0;
					// 	transform: scale(0.5);
					// }
				}
				.red-btn {
					background-color: #f14e4e;
					border:0rpx;
					color: #fff;
					&::before {
						content: "";
						position: absolute;
						left:0;
						top: 0;
						width: 200%;
						height: 200%;
						border: none;
						/* 以（0,0）为放缩中心点 */
						transform-origin: 0  0;
						transform: scale(0.5);
					}
				}
			}
		}
	}
</style>
