<template>
    <view class="f fac fjsb fw video-card">
        <template v-for="item in videoList">
            <view class="video-box" @click="goVideo(item)">
                <view style="position: relative;">
                    <image :src="item.cover_url" mode="widthFix" class="video-img" />
                    <view class="f fac fjc imgbut">
                        <span class="iconfont icon-you icon-but"></span>
                    </view>
                </view>
                <view class="title">
                    {{ item.title }}
                </view>
            </view>
        </template>
    </view>
</template>

<script>
export default {
    data() {
        return {
            id: null,
            videoList: [],
            page: 1,
            pageSize: 10,
            total: 0,
        }
    },
    onLoad(e) {
        if (e.id) {
            this.id = parseInt(e.id)
            this.getVideoList()
        }
    },
    async onReachBottom() {
        if (this.videoList.length < this.total) {
            this.page = this.page + 1;
            this.getVideoList()
        }
    },
    methods: {
        async getVideoList() {
            let params = {
                page: this.page,
                pageSize: this.pageSize,
                product_id: this.id
            }
            let res = await this.post('/api/video/center/list', params)
            if (res.code === 0) {
                this.videoList = [...this.videoList,...res.data.list];
                this.total = res.data.total
            }
        },
        // 获取 id 
        goVideo(item) {
            this.navTo('/packageE/marketingTool/miniVideo/commodityVideo?id=' + item.id + '&product_id=' + this.id + '&pause=true')
        },
    }
}
</script>

<style lang="scss" scoped>
.video-card {
    width: 702rpx;
    margin: 17rpx auto;

    .video-box {
        width: 345rpx;
        // height: 470rpx;
        box-sizing: border-box;
        background: #FFFFFF;
        border-radius: 16rpx;
        position: relative;
        margin-top: 10px;

        .video-img {
            min-width: 345rpx;
            max-width: 345rpx;
            min-height: 278rpx;
            max-height: 278rpx;
            border-radius: 16rpx 16rpx 0rpx 0rpx;
        }

        .imgbut {
            width: 72rpx;
            height: 72rpx;
            position: absolute;
            background: rgba($color: #000000, $alpha: 0.3);
            border-radius: 50%;
            top: 103rpx;
            left: 136rpx;

            .icon-but {
                font-size: 55rpx;
                color: #FFFFFF;
            }
        }

        .title {
            width: 305rpx;
            height: 75rpx;
            margin: 24rpx auto 16rpx;
            font-weight: 500;
            font-size: 28rpx;
            color: #00001C;
            line-height: 38rpx;
            word-break: break-all;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
    }
}
</style>