export const mixin = {
  data() {
    return {
      weixin_xianshi: false, // 在微信环境中才显示
    }
  },
  computed: {},
  onload(e) {},
  onShow() {
    this.allow = true
    if(this.isLogin()){
      this.updateUserInfo()
      this.queryUserLevelAuth()
    }else{
      if(this.$store.state.userLevelPriceTitle){
        this.$store.commit('updateUserLevelPriceTitle', "")
      }
    }
    this.getDiscountType()
  },
  methods: {
    // 是否登录
    isLogin(){
      return this.checkNull(uni.getStorageSync('user'))
    },
    // 获取最新用户信息对比等级
    async updateUserInfo(){
      const {data} = await this.post('/api/user/info')
      let storageUser = uni.getStorageSync('user')
      if(storageUser.user_level.id !== data.user_level.id){
        uni.setStorageSync('user',data)
      }
    },
    async queryUserLevelAuth(){
      const {code,data} = await this.get('/api/userPriceAuth/getAuth',{},false,false,false)
      if(code === 0 && data){
        const resData = JSON.parse(data)
        // 开启
        if(resData.is_open === "1"){
          const level_id = uni.getStorageSync('user').level_id
          // 不可以查看价格
          if(!resData.user_level || !resData.user_level.includes(level_id)){
            if(resData.product_ids && resData.product_ids.length){
              this.$store.commit('updateProductIds',resData.product_ids)
            }
              this.$store.commit('updateUserLevelPriceTitle', resData.message)
          }
        }
      }
    },
    // 匹配会员无权限是否可以看该商品价格
    isMatchingPriceAuthority(product_id){
        if(this.$store.state.userLevelPriceTitle && this.$store.state.product_ids.length){
            return this.$store.state.product_ids.includes(product_id)
        }else{
            return false;
        }
    },
    getSuperPrice(row) {
      const price =
        this.$store.state.discount_type === 1
          ? this.superTradePrice(row.exec_price)
          : row.level_price
      return this.toYuan(price)
    },
    // 获取会员等级定价方式
    async getDiscountType() {
      if (this.$store.state.discount_type === null) {
        const { code, data } = await this.post('/api/user/findSetting')
        if (code === 0) {
          this.$store.commit('upDiscountType', data.setting.value.discount_type)
        }
      }
    },
    // 转换超级批发价 (针对会员等级定价方式 = 1的情况)
    superTradePrice(price) {
      price = price || 0
      if (uni.getStorageSync('user') && uni.getStorageSync('user').user_level.discount > 0) {
        const n1 = this.accMul(
          price,
          uni.getStorageSync('user').user_level.discount,
        )
        let n2 = this.accDiv(n1, 10000)
        n2 = this.toDecimal2(n2)
        return n2
      } else {
        return price
      }
    },
    // 邮箱手机号正则验证
    regTest(type, value) {
      if (type == 'phone') {
        if (value.length == 0) {
          this.toast('请输入手机号')
          return false
        } else if (/^1[123456789]\d{9}$/.test(value)) {
          return true
        } else {
          this.toast('手机号格式不正确')
          return false
        }
      } else if (type == 'email') {
        if (value.length == 0) {
          this.toast('请输入邮箱')
        } else if (/^(\w-*\.*)+@(\w-?)+(\.\w{2,})+$/.test(value)) {
          return true
        } else {
          this.toast('邮箱格式不正确')
          return false
        }
      }
    },
    backRefresh() {
      // 返回上一页比并且刷新数据
      uni.$emit('refreshData')
      // #ifndef H5
      uni.navigateBack({
        delta: 1,
      })
      // #endif
      // #ifdef H5
      window.history.go(-1)
      // #endif
    },
    // 返回上一页
    navBack(d) {
      // #ifndef H5
      // console.log(d + '111');
      uni.navigateBack({
        delta: d || 1,
      })
      // #endif
      // #ifdef H5
      history.back()
      // console.log(window.history.back());
      // console.log(d);
      // #endif
    },
    toast(config, duration = 1500) {
      if (typeof config === 'string') {
        uni.showToast({
          title: config,
          icon: 'none',
          duration,
        })
      } else {
        uni.showToast(config)
      }
    },
    // 页面跳转
    navTo(url) {
      const that = this
      const login_s = '111'
      if (login_s != '') {
        uni.navigateTo({
          url,
          animationType: 'slide-in-right',
          animationDuration: 250,
        })
      } else {
        uni.redirectTo({
          url: '/pages/login/login',
        })
      }
    },
    // 跳转  关闭页面
    navsTo(url) {
      const that = this
      uni.redirectTo({
        url,
      })
    },
    // 防止连续点击  不带参数点击
    $clicks(fn) {
      const that = this
      if (this.allow) {
        this.allow = false

        fn()
        setTimeout(function () {
          that.allow = true
        }, 1500)
      } else {
        this.toast('操作过快！')
        console.log(this.allow)
      }
    },
    // 防止连续点击  带参数点击
    $check() {
      const that = this
      if (this.allow) {
        this.allow = false
        setTimeout(function () {
          that.allow = true
        }, 1500)
        return true
      } else {
        this.toast('操作过快！')
        return false
      }
    },
    // 导航菜单跳转
    tabTo(url) {
      uni.redirectTo({
        url,
      })
    },
    // 微信环境才显示
    weixin_huanjing_xianshi() {
      // #ifdef H5
      const ua = window.navigator.userAgent.toLowerCase()

      if (ua.match(/MicroMessenger/i) == 'micromessenger') {
        this.weixin_xianshi = true
      } else {
        this.weixin_xianshi = false
      }
      // #endif
    },
    // 获取上一页的参数
    routes() {
      const routes = getCurrentPages() // 获取当前打开过的页面路由数组
      let curRoute = routes[routes.length - 1].route // 获取当前页面路由
      const curParam = routes[routes.length - 1].options // 获取路由参数
      curRoute = '/' + curRoute
      sessionStorage.setItem('route', curRoute)
      return curRoute
    },
    pcRoutes() {
      const routes = getCurrentPages() // 获取当前打开过的页面路由数组
      let pcurl = ''
      let routeUrl = ''
      const routeArr = 'pages/index/index'
      if (routes.length > 1) pcurl = routes[routes.length - 1].__page__.fullPath
      if (pcurl === routeArr) {
        routeUrl = pcurl || ''
      } else {
        routeUrl = pcurl.slice(1) || ''
      }
      sessionStorage.setItem('route', routeUrl)
      return pcurl
    },

    // 获取设备信息
    obtainingDeviceInformation(arry) {
      // 参数格式为数组例如['model','pixelRatio'] 返回设备型号和像素比 返回值格式为{model: 'iPhone', pixelRatio: 2.0000000298023224}对象形式
      try {
        const json = {}
        const res = uni.getSystemInfoSync()
        for (let i = 0; i < arry.length; i++) {
          if (arry[i] == 'model') {
            // 设备型号
            json.model = res.model
          } else if (arry[i] == 'pixelRatio') {
            // 设备像素比
            json.pixelRatio = res.pixelRatio
          } else if (arry[i] == 'windowWidth') {
            // 可使用窗口宽度
            json.windowWidth == res.windowWidth
          } else if (arry[i] == 'windowHeight') {
            // 可使用窗口高度
            json.windowHeight = res.windowHeight
          } else if (arry[i] == 'language') {
            // 应用设置的语言
            json.language = res.language
          } else if (arry[i] == 'version') {
            // 引擎版本号
            json.version = res.version
          } else if (arry[i] == 'platform') {
            // 客户端平台
            json.platform = res.platform
          } else if (arry[i] == 'brand') {
            // 设备品牌
            json.brand = res.brand
          } else if (arry[i] == 'screenWidth') {
            // 屏幕宽度
            json.screenWidth = res.screenWidth
          } else if (arry[i] == 'screenHeight') {
            // 屏幕高度
            json.screenHeight = res.screenHeight
          } else if (arry[i] == 'windowTop') {
            // 可使用窗口的顶部位置
            json.windowTop = res.windowTop
          } else if (arry[i] == 'windowBottom') {
            // 可使用窗口的底部位置
            json.windowBottom = res.windowBottom
          } else if (arry[i] == 'statusBarHeight') {
            // 状态栏的高度
            json.statusBarHeight = res.statusBarHeight
          } else if (arry[i] == 'navigationBarHeight') {
            // 导航栏的高度
            json.navigationBarHeight = res.navigationBarHeight
          } else if (arry[i] == 'storage') {
            // 设备磁盘容量
            json.storage = res.storage
          } else if (arry[i] == 'currentBattery') {
            // 当前电量百分比
            json.currentBattery = res.currentBattery
          } else if (arry[i] == 'appName') {
            // 宿主APP名称
            json.appName = res.appName
          } else if (arry[i] == 'AppPlatform') {
            // app平台
            json.AppPlatform = res.AppPlatform
          } else if (arry[i] == 'host') {
            //	宿主平台
            json.host = res.host
          } else if (arry[i] == 'app') {
            // 当前运行的客户端
            json.app = res.app
          } else if (arry[i] == 'cacheLocation') {
            // 上一次缓存的位置信息
            json.cacheLocation = res.cacheLocation
          } else if (arry[i] == 'system') {
            // 操作系统版本
            json.system = res.system
          } else if (arry[i] == 'platform') {
            // 客户端平台，值域为：ios、android、mac（3.1.10+）、windows（3.1.10+）、linux（3.1.10+）
            json.platform = res.platform
          } else if (arry[i] == 'fontSizeSetting') {
            //	用户字体大小设置。以“我-设置-通用-字体大小”中的设置为准，单位：px
            json.fontSizeSetting = res.fontSizeSetting
          } else if (arry[i] == 'SDKVersion') {
            // 客户端基础库版本
            json.SDKVersion = res.SDKVersion
          } else if (arry[i] == 'swanNativeVersion') {
            // 宿主平台版本号
            json.swanNativeVersion = res.swanNativeVersion
          } else if (arry[i] == 'albumAuthorized') {
            // 允许微信使用相册的开关（仅 iOS 有效）
            json.albumAuthorized = res.albumAuthorized
          } else if (arry[i] == 'cameraAuthorized') {
            // 允许微信使用摄像头的开关
            json.cameraAuthorized = res.cameraAuthorized
          } else if (arry[i] == 'locationAuthorized') {
            // 允许微信使用定位的开关
            json.locationAuthorized = res.locationAuthorized
          } else if (arry[i] == 'microphoneAuthorized') {
            //	允许微信使用麦克风的开关
            json.microphoneAuthorized = res.microphoneAuthorized
          } else if (arry[i] == 'notificationAuthorized') {
            // 允许微信通知的开关
            json.notificationAuthorized = res.notificationAuthorized
          } else if (arry[i] == 'notificationAlertAuthorized') {
            // 允许微信通知带有提醒的开关（仅 iOS 有效）
            json.notificationAlertAuthorized = res.notificationAlertAuthorized
          } else if (arry[i] == 'notificationBadgeAuthorized') {
            // 允许微信通知带有标记的开关（仅 iOS 有效）
            json.notificationBadgeAuthorized = res.notificationBadgeAuthorized
          } else if (arry[i] == 'notificationSoundAuthorized') {
            // 允许微信通知带有声音的开关（仅 iOS 有效）
            json.notificationSoundAuthorized = res.notificationSoundAuthorized
          } else if (arry[i] == 'bluetoothEnabled') {
            // 蓝牙的系统开关
            json.bluetoothEnabled = res.bluetoothEnabled
          } else if (arry[i] == 'locationEnabled') {
            // 地理位置的系统开关
            json.locationEnabled = res.locationEnabled
          } else if (arry[i] == 'wifiEnabled') {
            // Wi-Fi 的系统开关
            json.wifiEnabled = res.wifiEnabled
          } else if (arry[i] == 'safeArea') {
            // 在竖屏正方向下的安全区域
            json.safeArea = res.safeArea
          } else if (arry[i] == 'safeAreaInsets') {
            // 在竖屏正方向下的安全区域插入位置（2.5.3+）
            json.safeAreaInsets = res.safeAreaInsets
          } else if (arry[i] == 'deviceId') {
            // 设备 id
            json.deviceId = res.deviceId
          } else if (arry[i] == 'uniPlatform') {
            // 当前所处环境
            json.uniPlatform = res.uniPlatform
          }
        }
        return json
      } catch (e) {
        // error
      }
    },
    externalLinkJudgment(path) {
      // 判断是不是外部链接
      console.log(path.indexOf('http'), path.indexOf('www'),path)
      if (
        path.indexOf('http') == -1 &&
        path.indexOf('www') == -1 &&
        path.indexOf('https') == -1
      ) {
        uni.navigateTo({
          url: path,
          success: () => {
            console.log('跳转成功')
          },
          fail: res => {
            if (
              res.errMsg.indexOf(
                'navigateTo:fail can not navigateTo a tabbar page' != -1,
              )
            ) {
              uni.switchTab({
                url: path,
              })
            }
            console.log('navigate failed', res)
          },
        })
      } else {
        // 跳转到webView页面
        uni.navigateTo({
          url: '/packageB/webView/webView?src=' + encodeURIComponent(path),
        })
      }
    },
    toFen(amount) {
      return Math.round(amount * 100)
    },
    toYuan(fen) {
      let num = fen
      num = fen * 0.01
      num += ''
      const reg =
        num.indexOf('.') > -1
          ? /(\d{1,3})(?=(?:\d{3})+\.)/g
          : /(\d{1,3})(?=(?:\d{3})+$)/g
      num = num.replace(reg, '$1')
      num = this.toDecimal2(num)
      return num
      // return (amount / 100);
    },
    // 百分比转为小数
    toPoint(percent) {
      let str = percent.replace('%', '')
      str = str / 100
      return str
    },
    // 小数转为百分数
    toPercent(point) {
      let str = Number(point * 100).toFixed(2)
      str += '%'
      return str
    },
    // 强制保留2位小数
    toDecimal2(x) {
      var f = parseFloat(x);
      if (isNaN(f)) {
          return false;
      }
      var s = typeof x === 'number' ? x.toString() : x
      var rs = s.indexOf('.');
      if (rs < 0) {
          rs = s.length;
          s += '.';
      }
      while (s.length <= rs + 2) {
          s += '0';
      }
      var res = s.match(/^\d+(?:\.\d{0,2})?/)
      return res && res.length ? res[0] : '0.00';
      /* var f = parseFloat(x)
      if (isNaN(f)) {
        return false
      }
      var f = Math.round(x * 100) / 100
      let s = f.toString()
      let rs = s.indexOf('.')
      if (rs < 0) {
        rs = s.length
        s += '.'
      }
      while (s.length <= rs + 2) {
        s += '0'
      }
      return s */
    },
    // 成功提示
    showSuccess(context) {
      uni.showToast({
        title: context,
        duration: 2000,
      })
    },

    // 纯文字提示
    showText(context) {
      uni.showToast({
        title: context,
        icon: 'none', // 如果要纯文本，不要icon，将值设为'none'
        duration: 2000, // 持续时间为 2秒
      })
    },
    // 格式化时间
    formatDateTime(inputTime, num) {
      inputTime = inputTime * 1000
      if (inputTime == null) {
        return ''
      }
      const date = new Date(inputTime)
      const y = date.getFullYear()
      let m = date.getMonth() + 1
      m = m < 10 ? '0' + m : m
      let d = date.getDate()
      d = d < 10 ? '0' + d : d
      let h = date.getHours()
      h = h < 10 ? '0' + h : h
      let minute = date.getMinutes()
      let second = date.getSeconds()
      minute = minute < 10 ? '0' + minute : minute
      second = second < 10 ? '0' + second : second
      let t = ''
      switch (num) {
        case 1:
          t = y
          break
        case 2:
          t = y + '-' + m
          break
        case 3:
          t = y + '-' + m + '-' + d
          break
        case 4:
          t = y + '-' + m + '-' + d + ' ' + h
          break
        case 5:
          t = y + '-' + m + '-' + d + ' ' + h + ':' + minute
          break
        case 6:
          t = y + '-' + m + '-' + d + ' ' + h + ':' + minute + ':' + second
          break
        case 7:
          t = h + ':' + minute
          break
        default:
          t = y + '-' + m + '-' + d + ' ' + h + ':' + minute + ':' + second
          break
      }
      return t
    },
    // 判断值是否为空
    checkNull(value) {
      if (
        value == null ||
        value == undefined ||
        value == '' ||
        value.length == 0
      ) {
        return false
      } else {
        return true
      }
    },
    // 校验是否为数字
    isNaN(value) {
      return (typeof value === 'number' && !isNaN(value))
    },
    /* js小数乘法精度丢失
     */
    accMul(arg1, arg2) {
      if (arg1 == null || arg2 == null) {
        return null
      }
      let n1, n2
      let r1, r2 // 小数位数
      try {
        r1 = arg1.toString().split('.')[1].length
      } catch (e) {
        r1 = 0
      }
      try {
        r2 = arg2.toString().split('.')[1].length
      } catch (e) {
        r2 = 0
      }
      n1 = Number(arg1.toString().replace('.', ''))
      n2 = Number(arg2.toString().replace('.', ''))
      return (n1 * n2) / Math.pow(10, r1 + r2)
    },
    // 除法
    accDiv(arg1, arg2) {
      let t1 = 0
      let t2 = 0
      let r1
      let r2
      try {
        t1 = arg1.toString().split('.')[1].length
      } catch (e) {}
      try {
        t2 = arg2.toString().split('.')[1].length
      } catch (e) {}

      r1 = Number(arg1.toString().replace('.', ''))
      r2 = Number(arg2.toString().replace('.', ''))
      return (r1 / r2) * Math.pow(10, t2 - t1)
    },
    // 加法
    accAdd: function (arg1, arg2) {
      var r1, r2, m
      try {
        r1 = arg1.toString().split('.')[1].length
      } catch (e) {
        r1 = 0
      }
      try {
        r2 = arg2.toString().split('.')[1].length
      } catch (e) {
        r2 = 0
      }
      m = Math.pow(10, Math.max(r1, r2))
      return (arg1 * m + arg2 * m) / m
    },
    // 减法
    accSub: function (arg1, arg2) {
      return this.accAdd(arg1, -arg2)
    },
    // 预览图片
    imgListPreview(item) {
      const urlList = []
      urlList.push(item) // 要显示的图片路径
      uni.previewImage({
        indicator: 'number',
        loop: true,
        urls: urlList,
      })
    },
    navigateBack() {
      // 返回上一页
      uni.navigateBack()
    },

    miniLogin() {
      // 小程序登录
      const that = this
      uni.getUserProfile({
        desc: '获取您的昵称、头像、地区及性别',
        success: function (info) {
          const rawData = JSON.parse(info.rawData)
          that.avatar = rawData.avatarUrl
          // 获取用户信息成功, info.authResult保存用户信息
          uni.login({
            provider: 'weixin',
            onlyAuthorize: true, // 微信登录仅请求授权认证
            success: function (event) {
              const { code } = event
              // 客户端成功获取授权临时票据（code）,向业务服务器发起登录请求。
              const url =
                '/api/wechatmini/wxMiniLogin?code=' +
                code +
                '&nickname=' +
                rawData.nickName +
                '&avatar_url=' +
                rawData.avatarUrl
              // 当微信小程序本地存储有pid时登录时携带上
              // #ifdef MP-WEIXIN
              let data = {}
              if(uni.getStorageSync('pid')) {
                data = {
                  pid: parseInt(uni.getStorageSync('pid'))
                }
              }
              // #endif
              that.get(url, data, true).then(res => {
                const data = res.data
                if (data.status === 0) {
                  that.toast(res.msg)
                  return
                }
                if (data.code == 202) {
                  that.toast('该账号还未注册,2秒后将跳转到注册页面!')
                  setTimeout(
                    () =>
                      uni.navigateTo({
                        url:
                          '/pages/register/register?avatar=' +
                          data.data?.avatar +
                          '&nickname=' +
                          data.data?.nickname +
                          '&wx_mini_openid=' +
                          data.data?.wx_mini_openid +
                          '&wx_unionid=' +
                          data.data?.wx_unionid +
                          '&show=' +
                          true +
                          '&type=1',
                      }),
                    2000,
                  )
                } else {
                  uni.setStorageSync('token', data.token)
                  uni.setStorageSync('user', data.user)
                  uni.setStorageSync('type', 'refresh')
                  that.toast(res.msg)
                  /* uni.navigateBack()
					        return; */
                  const pages = getCurrentPages()
                  let index = null;
                  for(let i=0;i<pages.length;i++){
                    if(pages[i].route === 'pages/login/login'){
                      index = i - 1
                      break;
                    }
                  }
                  const parentLink = pages[index]?.$page?.fullPath
                  /* const parentLink = pages[pages.length - 2]?.$page?.fullPath
                  if(parentLink === "/pages/login/login"){
                    parentLink = pages[pages.length - 3]?.$page?.fullPath
                  } */
                  /* let albumLink = pages.find(item=>item.$page.fullPath === "/packageC/album/albumList")
                  if(albumLink){
                    parentLink = albumLink.$page.fullPath
                  } */
                  /****************** /

                  /*************** */
                  if (parentLink) {
                    setTimeout(() => {
                      uni.redirectTo({
                        url: parentLink
                      });
                    }, 1500)
                  } else {
                    setTimeout(() => {
                      uni.redirectTo({
                        url: '/pages/index/index'
                      });
                    }, 1500)
                  }
                }
              })
            },
            fail: function (err) {
              // 登录授权失败
              // err.code是错误码
            },
          })
        },
      })
    },
  },
}
