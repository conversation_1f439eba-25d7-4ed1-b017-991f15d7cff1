<template>
	<view class="d-cc">
		<view class="d-f">
			<view>
				<iframe id="iframe_id" :src="httpUrl+'/h5/index.html#'" frameborder="0" class="ifCss"
					scrolling="auto"></iframe>
			</view>
			<!-- 右侧模块 -->
			<view  style="margin-top: 200px;margin-left: 25px;" v-show="platform == 'windows'|| platform == 'mac'">
				<view class="d-cf">
					<view class="d-cf">
						<image
							src="https://shop-yunzshop-com.oss-cn-hangzhou.aliyuncs.com/newimage/4fcbd209f8578cfb7f78ad2c96a0f789.jpeg"
							style="border-radius: 50%;height: 47.5px;width: 47.5px;"></image>
					</view>
					<view style="color: #515a63;font-size: 17px;margin-left: 10px;" class="">供应链中台</view>
				</view>
				<!-- 二维码 -->
				<view style="border:1px solid #e4e4e4;width: 160px;height: 185px;margin-top: 10px;padding:10px;" class="radius20 bg-white">
					<view class="d-cc-c">
						<view style="color: #666;font-size: 15px;" @click="test">手机微信 <text style="margin-left: 7.5px;">"扫一扫"</text></view>
						<view style="margin-top: 10px;">
							<canvas id="qrcode" canvas-id="qrcode"
								:style="{ width: `${size}px`, height: `${size}px` }"></canvas>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uQRCode from '@/uni_modules/Sansnn-uQRCode/js_sdk/u-qrcode';
	export default {
		data() {
			return {
				height: '',
				text: this.httpUrl+'/h5/index.html#',
				size: 150,
				platform: '',
				httpUrl:'' //网页链接的地址
			}
		},
		onShow() {
			this.platform = this.obtainingDeviceInformation(["platform"]).platform
		},
		onLoad() {
			this.getSiteRoot()
			//接收iframe页面传过来的值
			window.addEventListener('message', e => {
				if (e.origin === this.httpUrl) {
					if(typeof(e.data)!= "object"){
						this.text += e.data
					}
					this.drawCode()
					this.text = this.httpUrl+"/h5/index.html#"
				}
			}, false)
		},
		methods: {
			drawCode() { //绘制二维码
				const ctx = uni.createCanvasContext('qrcode');
				const uqrcode = new uQRCode({
						text: this.text,
						size: this.size
					},
					ctx
				);
				uqrcode.make();
				uqrcode.draw();
			},
			getSiteRoot() {
				this.httpUrl =  document.location.protocol + "//" + window.location.host
			  },
		}
	}
</script>

<style>
	.ifCss {
		width: 390px;
		height: 850px;
		margin: 0 auto;
		border: none;
	}
</style>
