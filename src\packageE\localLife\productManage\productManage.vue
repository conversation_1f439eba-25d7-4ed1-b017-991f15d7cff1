<template>
  <view>
    <!-- 搜索 -->
    <view class="f d-bf search_box pl-25">
      <view class="search">
        <u-search
          clearabled
          searchIconSize="40"
          placeholder="商品名称"
          :showAction="false"
          height="64"
          v-model="product_title"
        ></u-search>
      </view>
      <view class="search_btn pr-45">
        <u-button
          shape="circle"
          type="error"
          size="small"
          @click="searchProduct"
        >
          搜索
        </u-button>
      </view>
    </view>
    <!-- tabs切换 -->
    <view class="tabs_box">
      <view style="width: 750rpx">
        <u-tabs
          :scrollable="false"
          :list="tabsList"
          lineColor="#F15353"
          lineHeight="5"
          :itemStyle="{
            height: '70rpx',
            width: '210rpx',
          }"
          :activeStyle="{
            color: '#F15353',
            fontWeight: '500',
          }"
          :inactiveStyle="{
            color: '#00001C',
            fontWeight: '400',
          }"
          @click="changeTabs"
        ></u-tabs>
      </view>
    </view>
    <!-- 商品信息 -->
    <view
      class="product_box mt_20 ml_25 mr_40 pt_25 f"
      v-for="(item, index) in productList"
      :key="item.id"
    >
      <view class="product_img ml_25">
        <u-image
          :src="item.image_url"
          radius="20rpx"
          width="200rpx"
          height="200rpx"
        ></u-image>
      </view>
      <view class="product_msg ml_20 f d-f-c d-bf2">
        <view class="fs-2 black">{{ item.title }}</view>
        <view>
          <text class="silver">库存: {{ item.stock_name }}</text>
          <text class="ml_50 silver">销量：{{ item.sales }}</text>
        </view>
        <view class="f d-bc">
          <view class="black fs-3">￥{{ toYuan(item.price) }}</view>
          <view class="f fac">
            <view v-if="item.visible" class="silver">适用</view>
            <view v-else class="silver">禁用</view>
            <view class="ml_10">
              <u-switch
                size="30"
                :value="item.visible"
                @change="changeSwitch(item.id, item.visible, index)"
                activeColor="#F15353"
                inactiveColor="#D6D6DC"
              ></u-switch>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'productManage',

  data() {
    return {
      tabsList: [
        {
          name: '全部',
        },
        {
          name: '适用',
        },
        {
          name: '禁用',
        },
      ],
      product_title: '', // 商品名称
      tag_type: 0, // 0-全部 1-适用 2-禁用
      productList: [], // 商品列表
      store_id: null, // 门店ID
      page: 1,
      pageSize: 10,
      total: null,
    }
  },
  onLoad(options) {
    this.store_id = options.store_id
  },
  mounted() {
    this.getStoreProductList()
  },

  // 加载更多数据
  async onReachBottom() {
    if (this.productList.length < this.total) {
      this.page = ++this.page
      const params = {
        product_title: this.product_title,
        tag_type: parseInt(this.tag_type),
        store_id: parseInt(this.store_id),
        page: this.page,
        pageSize: this.pageSize,
      }
      const res = await this.get(
        '/api/localLife/front/store/product/list',
        params,
      )
      if (res.code === 0) {
        this.productList = this.productList.concat(res.data.list)
      }
    }
  },

  methods: {
    // 获取商品列表
    async getStoreProductList() {
      const params = {
        product_title: this.product_title,
        tag_type: parseInt(this.tag_type),
        store_id: parseInt(this.store_id),
        page: this.page,
        pageSize: this.pageSize,
      }
      const res = await this.get(
        '/api/localLife/front/store/product/list',
        params,
      )
      if (res.code === 0) {
        this.productList = res.data.list
        this.total = res.data.total
      }
    },
    // 搜索商品
    searchProduct() {
      this.page = 1
      this.getStoreProductList()
    },
    // 切换tabs
    changeTabs(value) {
      switch (value.name) {
        case '全部':
          this.tag_type = 0
          break
        case '适用':
          this.tag_type = 1
          break
        case '禁用':
          this.tag_type = 2
          break

        default:
          break
      }
      this.page = 1
      this.getStoreProductList()
    },
    // 改变按钮状态
    async changeSwitch(id, value, index) {
      const data = {
        product_id: parseInt(id),
        store_id: parseInt(this.store_id),
      }
      let res = ''
      if (value) {
        res = await this.post(
          '/api/localLife/front/store/product/invisible',
          data,
        )
      } else {
        res = await this.post(
          '/api/localLife/front/store/product/visible',
          data,
        )
      }
      if (res.code === 0) {
        this.page = 1
        if (this.tag_type === 0) {
          this.getStoreProductList()
        } else {
          this.productList.splice(index, 1)
        }
        this.toast(res.msg)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.search_box {
  width: 750rpx;
  height: 96rpx;
  background-color: #ffffff;
  .search {
    width: 580rpx;
  }
  .search_btn {
    width: 110rpx;
    height: 64rpx;
  }
}
.tabs_box {
  width: 750rpx;
  height: 78rpx;
  background-color: #ffffff;
}
.product_box {
  width: 702rpx;
  padding-bottom: 26rpx;
  // height: 248rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  .product_img {
    width: 200rpx;
    height: 200rpx;
    background: #d6d6dc;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
  }
  .product_msg {
    height: 200rpx;
    width: 433rpx;
    margin-right: 24rpx;
  }
}
.black {
  color: #00001c;
  font-weight: bold;
}
.silver {
  color: #6e6e79;
}
</style>
