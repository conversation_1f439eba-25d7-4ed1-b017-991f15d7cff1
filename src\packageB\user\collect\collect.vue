<!-- 我的收藏页面 -->
<template>
	<view>
		<!-- 搜索框 -->
		<view class="pt_14  bg-white">
			<view class="d-cc">
				<view class="iconfont icon-member-left" style="color: #747474;font-size: 20rpx;" @tap="navigateBack">
				</view>
				<view class="ml_30">
					<view>
						<view style="width:650rpx;" class="d-bf">
							<u-search height="60" bgColor="#eff0f1" color="#666666" searchIconSize="40"
								:showAction="false" placeholder="请搜索商品名称" @change="getFavoriteProductsInfoList()"
								v-model="$store.state.collectPage.title" v-if="switching == 'commodity'" />
							<u-search height="60" bgColor="#eff0f1" color="#666666" searchIconSize="40"
								:showAction="false" placeholder="请搜索店铺名称" @change="getFavoriteStoreInfoList()"
								v-model="storePage.title" v-else />
							<view class="ml-20 fs-2" @tap="favoriteDisplaySwitch">{{cut}}</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 选项卡切换 -->
			<view class="d-f mt_39">
				<view v-for="(item,index) in list" :key="index" @tap="oneMenuCut(item,index)"
					:style="index == 0 ?'padding-left: 162rpx;':'padding-left: 300rpx;padding-right:162rpx'">
					<view :style="item.show == true?'color: #f14e4e;':'color: #2f2f2f;'" class="fs-2">{{item.name}}
					</view>
					<view style="width: 50rpx;height: 5rpx;background-color: #f14e4e;" v-show="item.show == true"
						class="mt_22 ml-5"></view>
				</view>
			</view>
		</view>
		<!--  -->
		<view v-show="switching == 'commodity'">
			<view class="d-cf pl_30 mt_26">
				<view class="d-cc-c" style="position：relative" @tap="secondaryMenuCut()">
					<view class="fs-1 d-cc"><text :style="secondaryMenu.show == true?'color:#f14e4e':''">商品状态</text>
						<!-- #ifdef APP-PLUS || H5 -->
						<u-icon :name="secondaryMenu.iconName" style="transform: scale(0.7);"
							:color="secondaryMenu.valueColor" class="ml-5">
						</u-icon>
						<!-- #endif -->
						<!-- #ifdef MP-WEIXIN-->
						<view class="ml-10">
							<u-icon :name="secondaryMenu.iconName" size="23" :color="secondaryMenu.valueColor">
							</u-icon>
						</view>
						<!-- #endif -->
					</view>
					<view class="d-cc-c" style="z-index: 999;position: absolute;top: 228rpx;"
						v-show="secondaryMenu.show">
						<view class="mydiv mt_11"></view>
						<view style="background:rgba(0,0,0,0.75);"
							class="pt_40 pl_20 pr_20 c-white radius10 fs-0-5">
							<view class="mb_40" :class="$store.state.collectPage.lose_efficacy == 2?'c-f14e4e':''"
								@click="screeningOfJudgment(2)">在架商品</view>
							<view class="mb_40" :class="$store.state.collectPage.lose_efficacy == 1?'c-f14e4e':''"
								@click="screeningOfJudgment(1)">失效商品</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 商品收藏列表 -->
			<view>
				<view class="mt_22">
					<view>
						<crosswiseTosideSetup :list="list1"></crosswiseTosideSetup>
					</view>
				</view>
			</view>
		</view>
		<!--  -->
		<!-- 店铺收藏列表 -->
		<view v-show="switching == 'store'">
			<cancelStoreCollection :list="list2"></cancelStoreCollection>
		</view>
		<!--  -->
		<!-- 底部按钮 -->
		<view class="fixed-bottom d-bf fs-2 pt-10 d-bf pl-20 pr-20 bg-white"
			style="width: 100%;height: 100rpx;box-shadow:0rpx 0rpx 5rpx #ccc;"
			v-show="cut == '完成'">
			<view class="d-cc">
				<view class="">
					<u-checkbox-group @change="checkAll">
						<u-checkbox shape="circle" size="35" :checked="haveAll" activeColor="#f14e4e"></u-checkbox>
					</u-checkbox-group>
				</view>
				<view class="fs-1-5 ml-20">全选</view>
			</view>
			<view style="background-color: #f14e4e;"
				class="d-cc pt_17 pb_17 pr_26 pl_26 mr_30 radius30 c-white" @click="deleteFavoriteByIds">取消收藏</view>
		</view>
		<!--  -->
	</view>
</template>

<script>
	import crosswiseTosideSetup from "../../components/crosswiseTosideSetup/crosswiseTosideSetup.vue";
	import cancelStoreCollection from "../../components/collect/cancelStoreCollection/cancelStoreCollection.vue";
	export default {
		options: { styleIsolation: 'shared' },
		components: {
			crosswiseTosideSetup,
			cancelStoreCollection
		},
		computed: {
			haveAll() { //判断全选按钮是否选中
				let num = 0;
				for (var i = 0; i < this.$store.state.cancellationArray.length; i++) {
					if (String(this.$store.state.cancellationArray[i].check).indexOf("false") == 0) {
						num++
					}
				}
				for (var i = 0; i < this.$store.state.storeToCancel.length; i++) {
					if (String(this.$store.state.storeToCancel[i].check).indexOf("false") == 0) {
						num++
					}
				}
				if (num > 0) {
					return false
				} else {
					return true
				}
			}
		},
		data() {
			return {
				switching: 'commodity',
				cut: '管理',
				list: [{
					name: '商品',
					show: true,
				}, {
					name: '店铺',
					show: false
				}, ],
				list1: [], //已收藏的商品列表
				list2: [], //已收藏的店铺信息
				storePage: { //店铺分页信息
					page: 1,
					pageSize: 5,
					title: '',
				},
				secondaryMenu: {
					valueColor: '#7a7a7a',
					iconName: 'arrow-down-fill',
					show: false,
				}, //商品二级菜单判断
			}
		},
		onLoad(option) {
			if (option.type == 'commodity') { //根据外部跳转进来传的值来默认选中选项卡
				this.list[0].show = true
				this.list[1].show = false
			} else {
				this.list[0].show = false
				this.list[1].show = true
			}
			this.switching = option.type
		},
		onShow() {
			this.getFavoriteStoreInfoList() //店铺收藏
			this.getFavoriteProductsInfoList() //商品收藏
		},
		onHide() {
			this.$store.commit("upCollectionShows", false)
		},
		onUnload() {
			this.$store.commit("upCollectionShows", false)
		},
		methods: {
			oneMenuCut(item, index) { //一级菜单栏切换
				/* 颜色切换 */
				if (item.show == false) {
					this.$set(this.list[index], 'show', true)
					for (var i = 0; i < this.list.length; i++) {
						if (i != index) {
							this.$set(this.list[i], 'show', false)
						}
					}
				}

				/* 选项卡内容切换 */
				if (index == 0) {
					this.switching = 'commodity'
				} else {
					this.switching = 'store'
				}
			},

			secondaryMenuCut() { //二级菜单栏切换
				/* 颜色切换 */
				if (this.secondaryMenu.valueColor == '#7a7a7a') {
					this.secondaryMenu.valueColor = '#f14e4e'
				} else {
					this.secondaryMenu.valueColor = '#7a7a7a'
				}
				/* 图标切换 */
				if (this.secondaryMenu.iconName == 'arrow-down-fill') {
					this.secondaryMenu.iconName = 'arrow-up-fill'
				} else {
					this.secondaryMenu.iconName = 'arrow-down-fill'
				}

				/* 弹框切换显示 */
				if (this.secondaryMenu.show == true) {
					this.secondaryMenu.show = false
				} else {
					this.secondaryMenu.show = true
				}
			},
			favoriteDisplaySwitch() { //取消收藏显示切换
				if (this.cut == '管理') {
					this.cut = '完成'
					this.$store.commit("upCollectionShows", true)
				} else if (this.cut == '完成') {
					this.cut = '管理'
					this.$store.commit("upCollectionShows", false)
				}
			},

			goCommodity_details() { //跳转到商品详情页面
				uni.navigateTo({
					url: '/packageA/commodity/commodity_details/commodity_details'
				})
			},
			checkAll() { //全选
				let arry = [...this.$store.state.cancellationArray]
				let arry1 = [...this.$store.state.storeToCancel]
				if (this.haveAll == true) {
					for (var i = 0; i < arry.length; i++) {
						this.$set(arry[i], "check", false)
					}
					for (var i = 0; i < arry1.length; i++) {
						this.$set(arry1[i], "check", false)
					}
				} else {
					for (var i = 0; i < arry.length; i++) {
						this.$set(arry[i], "check", true)
					}
					for (var i = 0; i < arry1.length; i++) {
						this.$set(arry1[i], "check", true)
					}
				}

				this.$store.commit("upCancellationArray", arry)
				this.$store.commit("upStoreToCancel", arry1)
			},
			getFavoriteProductsInfoList(value) { //商品收藏列表
				if (value != "refresh") {
					this.list1 = [], this.$store.commit("upCollectPage", {
						page: 1
					})
				}
				this.post('/api/favorite/getFavoriteProductsInfoList', this.$store.state.collectPage, true).then(data => {
					if (data.data.page <= data.data.total / data.data.pageSize) {
						this.$store.commit("upHomeNoMore", false)
						this.$store.commit("upCollectPage", {
							page: this.$store.state.collectPage.page + 1
						})
					} else {
						this.$store.commit("upHomeNoMore", true)
					}
					for (var i = 0; i < data.data.list.length; i++) { //获取已收藏的商品列表
						/* 分转元 */
						data.data.list[i].product.price = this.toYuan(data.data.list[i].product.price)
						data.data.list[i].product.guide_price = this.toYuan(data.data.list[i].product.guide_price)
						data.data.list[i].product.origin_price = this.toYuan(data.data.list[i].product
							.origin_price)
						this.$set(data.data.list[i], "check", false)
						this.list1.push(data.data.list[i])
					}
					this.$store.commit("upCancellationArray", this.list1)
				})
			},
			getFavoriteStoreInfoList(value) { //店铺收藏列表
				if (value != "refresh") {
					this.list2 = [], this.storePage.page = 1
				}
				this.post('/api/favorite/getFavoriteStoreInfoList', this.storePage, true).then(data => {
					if (data.data.page <= data.data.total / data.data.pageSize) {
						this.$store.commit("upHomeNoMore", false)
						this.storePage.page += 1
					} else {
						this.$store.commit("upHomeNoMore", true)
					}
					for (var i = 0; i < data.data.list.length; i++) { //获取已收藏的商品列表
						this.$set(data.data.list[i], "check", false)
						this.list2.push(data.data.list[i])
					}
					this.$store.commit("upStoreToCancel", this.list2)
				})
			},
			screeningOfJudgment(value) { //一级赛选
				this.$store.commit("upCollectPage", {
					lose_efficacy: value
				}) //一级筛选传值
				this.getFavoriteProductsInfoList()
			},
			deleteFavoriteByIds() { //取消收藏(批量)
				let arry = []
				let arry1 = [...this.$store.state.cancellationArray, ...this.$store.state.storeToCancel]
				for (var i = 0; i < arry1.length; i++) {
					if (arry1[i].check == true) {
						arry.push(arry1[i].id)
					}
				}
				if (arry.length == 0) {
					this.showText("您还未选择要取消的收藏商品/店铺")
					return
				}
				this.post('/api/favorite/deleteFavoriteByIds', {
					ids: arry
				}, true).then(data => {
					this.getFavoriteProductsInfoList()
					this.getFavoriteStoreInfoList()
					this.showText("取消收藏成功")
				})
			},
			onReachBottom() {
				if (this.switching == 'commodity') {
					if (!this.$store.state.homeNoMore) {
						this.getFavoriteProductsInfoList("refresh")
					}
				} else {
					if (!this.$store.state.homeNoMore) {
						this.getFavoriteStoreInfoList("refresh")
					}
				}
			},
		}
	}
</script>

<style scoped>
	.mydiv {
		height: 0px;
		width: 0px;
		border-left: 12rpx solid transparent;
		border-right: 12rpx solid transparent;
		border-bottom: 12rpx solid;

	}

	.fixed-bottom {
		position: fixed;
		right: 0;
		bottom: calc(var(--window-bottom) + 0px);
		left: 0;
		z-index: 1030;
		margin-bottom: 6;
	}
	
	/* ::v-deep .uni-checkbox-input{
		border-radius: 50% !important;
	} */
</style>
