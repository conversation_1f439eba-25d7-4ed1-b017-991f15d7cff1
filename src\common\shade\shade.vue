<!-- 分享组件 -->
<template>
	<view>
		<view>
			<u-overlay :show="$store.state.shadeShow" @click="$store.commit('upShadeShow',false)">
				<view class="share">
					<u--image src="https://supply-chain-yunzmall.oss-cn-beijing.aliyuncs.com/2022517/<EMAIL>" width="150rpx" heigth="100rpx"></u--image>
				</view>
				<view class="warp c-white">
					<view class="d-cc-c">
						<view class="fw-b fs-6">立即分享给好友吧</view>
						<view class="fs-2 mt-15">点击屏幕右上角将页面分享给好友</view>
					</view>
				</view>
			</u-overlay>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
			}
		},
		methods: {
		}
	}
</script>

<style>
	.warp {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
	}

	.share {
		position: fixed;
		right: 40rpx;
		top: 45rpx;
	}
</style>
