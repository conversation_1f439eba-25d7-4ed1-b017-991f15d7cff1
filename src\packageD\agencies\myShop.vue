<template>
    <!-- 小商店 -->
    <div>
        <template v-if="total > 0">
            <view class="p-25 f fac fjsb radius15" @click="distributorsAndShops">
                <view class="fs-1-5 sum">总数：{{total}}</view>
            </view>
            <view v-for="item in viplist" :key="item.id" class="bg-white mb_20 p-25 radius15 vip_card">
                <view class="fs-2 f fac">
                    <view class="f-bold">会员昵称</view>
                    <view class="vipNum f fac fjc ml-15">{{ item.user_info.nickname }}</view>
                </view>
                <view class="fs-1 f fac fjsb mt-30">
                    <view>{{ formatDateTime(item.created_at) }}</view>
                    <view>小商店名称：{{item.small_shop.title }}</view>
                </view>
            </view>
        </template>
        <view v-else class="mt-20 fs-1 d-c">暂无数据~</view>
    </div>
</template>

<script>
export default {
    name:'myShop',
    data() {
        return {
            viplist: [],
            total:null
        }
    },
    onShow() {
        this.tablerListFun()
    },
    methods: {
        // 获取小商店数据表
        async tablerListFun() {
            let res = await this.get('/api/institution/distributor/teamShopList', {
                page: 1
            })
            console.log(res);
            if (res.code === 0) {
                this.viplist = res.data.list
                this.total = res.data.total
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.sum {
    color: rgba(16, 16, 16, 0.72);
}

.vip_card {
    margin: 0 20rpx 20rpx;
}

.vipNum {
    height: 46rpx;
    padding: 0 20rpx;
    background-color: rgba(239, 84, 82, 0.14);
    border-radius: 23rpx;
    color: rgba(239, 84, 82, 1);

}
</style>