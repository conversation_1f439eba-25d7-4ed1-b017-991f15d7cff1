<!-- 地址列表 -->
<template>
	<view>
		<view class="wallet-list addressmb" >
			<u-radio-group placement="column" v-model="addressId"  v-if="addressListdata.length > 0">
				<block v-for="(item,index) in addressListdata" :key="index" >
					<view class="mb_20 card-content">
						<view class="item d-be" >
							<view class="d-bf c-29 font_size16">
								<!-- 渲染地址-->
									<view class="mb_26 ">{{item.realname}}</view>
									<view class="mb_26">{{item.mobile}}</view>
							</view>
							<view class="d-bf text-r font_size12">
								<view class="mb_26 c-74 font_size14">{{item.province}} {{item.city}} {{item.county}} {{item.town}} {{item.detail}}</view>
							</view>
						</view>
						<view class="d-bf action">
							<view class="d-cf font_size12 c-74">
								<view class="action-del" @click="addressDel(item.id)">
									<text class="iconfont icon-ht_operation_delete add-icon"></text>
									<text>删除</text>
								</view>
								<view @click="navTo('/packageB/member/alterAddress?id=' + item.id)">
									<text class="iconfont icon-fontclass-xiugai add-icon"></text>
									<text>编辑</text>
								</view>
		
							</view>
							<view class="d-cf">
								<u-radio
										shape="square"
										size="40"
										:name="item.id"
										activeColor="#f14e4e"
										@change="radioChange(item.is_default,item.id)"
								>
								</u-radio>
									<text class="font_size12 c-orange" >默认地址</text>
							</view>
						</view>
					</view>
				</block>
			</u-radio-group>
			<view v-else>
				<u-empty
				        mode="address" marginTop="100rpx" textSize="28rpx" iconSize="150">
				</u-empty>
			</view>
		</view>
		
		<view class="content03 d-cc" @click="navTo('/packageB/member/appendAddress')">
			<view class="iconfont icon-customform_add addColor"></view>
			<view class="new-add">新增收货地址</view>
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				type:'address',
				addressListdata:[],
				addressId:0,
				page: 1,
				pageSize: 10,
				total: 0
			}
		},
		onLoad() {
			
		},
		onShow() {
			this.addressListdata = [];
			this.addressListData();
		},
		onReachBottom(){
			if(this.page > 1 && this.addressListData.length < this.total){
				this.page += 1
				this.addressListData()
			}
		},
		methods: {
			addressListData(page = this.page, pageSize = this.pageSize) { //地址列表
				this.post('/api/address/list', {page,pageSize}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						this.total = res.data.total
						if(this.addressListdata.length < this.total){
							this.addressListdata = [...this.addressListdata, ...data.list]
						}
						// this.addressListdata = data.list;
						for(let i in this.addressListdata) {
							if(this.addressListdata[i].is_default) {
								this.addressId = this.addressListdata[i].id;
							}
						}
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			radioChange(is_default,id) {  //设置默认地址
				this.post('/api/address/setDefault',{id}, true).then((res) => {
					if(res.code === 0) {
						this.toast(res.msg);
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			addressDel(id) { //删除地址
				this.post('/api/address/delete', {id}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						this.toast('删除成功');
						this.addressId = 0;
						setTimeout(() => {
							this.addressListdata = []
							this.addressListData();
						},500);
						
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.content03 {
		width: 100%;
		height: 90rpx;
		background-color: #f15353;
		position: fixed;
		bottom: 0;
		z-index: 99;
		.new-add {
			font-size: 28rpx;
			color: #fff;
			margin-left: 20rpx;
		}
		.addColor {
			color: #fff;
		}
	}
	
	.addressmb {
		padding-bottom: 90rpx;
	}
	.wallet-list {
		margin-top: 5rpx;
		.card-content {
			padding: 30rpx 26rpx 0rpx 26rpx;
			background-color: #fff;
			.item {
				border-bottom: 1px solid #f2f2f2;
				&:last-of-type {
					border-bottom: none;
				}
			}
			.action {
				background-color: #fff;
				line-height: 60rpx;
				.action-del {
					margin-right: 57rpx;
				}
				.icon-ht_operation_delete {
					color:#696969;
					font-size: 24rpx;
					margin-right: 10rpx;
				}
				.icon-fontclass-xiugai {
					color:#696969;
					font-size: 24rpx;
					margin-right: 10rpx;
				}
				.add-icon {
					font-size: 34rpx;
					margin-right: 16rpx;
				}
			}
		}
		
	}
</style>
