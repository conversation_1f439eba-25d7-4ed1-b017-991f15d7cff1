<template>
	<view>
		<view v-for="(item,index) in list" :key="index" class="d-f">
			<view class="ml-15 d-cc" v-if="collectionShows" style="width: 5%">
				<checkbox checked="true" :value="String(item.id)" :checked="storeToCancel[index].check"
					style="transform: scale(0.7);border-radius: 50%;" color="#f14e4e" @click="collectionOfSelected(index)" />
			</view>
			<view class="bg-white p-20 radius10 d-bf mt-20 mr-20" style="width: 700rpx;" :class="collectionShows?'ml-5':'ml-20'">
				<view class="d-cc">
					<view>
						<u--image :src="item.supplier.shop_logo" width="90rpx" height="90rpx" radius="10">
						</u--image>
					</view>
					<view class="ml-20">
						<view class="fs-1-5">{{item.supplier.shop_name}}</view>
						<view class="fs-0-5 mt_20" style="color: #999999;">商品数:{{item.supplier.goods_count}}</view>
					</view>
				</view>
				<view style="background-color: #f14e4e;color:#FFFFFF;width: 110rpx;height: 46rpx;"
					class=" radius45 fs-0 d-cc pb-5" @click="navTo('../../store/storeDetails?id='+item.supplier.id+'&type=0')">进店</view>
			</view>
		</view>
		<view class="d-cc fs-1 c-5e5e5e mb-25 mt-25">
			<view v-if="list.length !=0">暂无更多~</view>
			<view v-if="list.length == 0">暂无数据~</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			list: { //店铺收藏列表信息
				type: Array
			},
		},
		computed: {
			collectionShows() { //为了适配小程序端{{}}不能直接读取vuex值的问题
				return this.$store.state.collectionShows
			},
			storeToCancel(){
				return this.$store.state.storeToCancel
			}
		},
		data() {
			return {

			}
		},
		methods: {
			collectionOfSelected(e) { //复选框选中
				let arry = []
				if (this.list[e].check == true){
					this.list[e].check = false
				} else {
					this.list[e].check = true
				}
				console.log(this.list)
				this.$store.commit("upStoreToCancel", this.list)
			},
		}
	}
</script>

<style scoped>
	/* ::v-deep .uni-checkbox-input:hover{
		border:none;
	} */
</style>
