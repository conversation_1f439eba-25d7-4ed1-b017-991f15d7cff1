<!-- 售后信息 -->
<template>
	<view>
		<view 
			class="aftersales-status" 
			v-if="details.after_sales_audit && details.after_sales_audit.status === -1 && details.status !== -1">
			{{details.after_sales_audit.status_name}}
		</view>
		<view class="aftersales-status" v-else>
			{{details.status_name}}
		</view>
		<view class="page-wrapper">
			<view class="return-info">
				<view class="return-info-text d-cf">售后信息</view>
				<view class="return-goods-list d-f" @click="navTo('/packageA/commodity/commodity_details/commodity_details?id=' + details.product_id)">
					<view class="return-goods-face">
						<image :src="order_item.image_url"></image>
					</view>
					<view class="return-goods-content">
						<text style="color: #333;font-size: 20rpx;">
							{{order_item.title}}
						</text>
						<view class="return-goods-desc d-bf mt_40" style="width: 510rpx;">
							<view class="return-goods-num">数量：{{order_item.qty}}</view>
							<view class="return-goods-price">
								申请退款金额：<text class="c-orange">￥{{amount || 0.00}}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="return-details">
					<view class="return-details-item d-f">售后类型：{{details.type === 0?'退款':'退款退货'}}</view>
					<view class="return-details-item d-f">退款金额：{{amount || 0.00}}</view>
					<view class="return-details-item d-f">售后原因：{{details.refund_reason_name}}</view>
					<view class="return-details-item d-f">申请说明：{{details.description}}</view>
					<view class="return-details-item d-f">申请时间：{{details.created_at}}</view>
					<view class="return-details-item d-f">卖家留言：{{details.after_sales_audit && details.after_sales_audit.cause}}</view>
					<view class="return-details-item d-f">
						<block v-for="(item,index) in details.detail_images" :key="index">
							<view class="photoBox flow">
								<image :src="item" @click="largeIcon(index)"></image>
							</view>
						</block>
					</view>
				</view>
			</view>
			<view class="address-info" v-if="(details.status === 3 || details.status === 1 || details.status === 2) && details.type === 1">
				<view class="address-info-text d-cf">退货地址信息</view>
				<view class="address-contact address-text" >{{contacts}} {{addressDetails.tel}}</view>
				<view class="address-site address-text">
					{{province_name.name}} {{city_name.name}} {{district_name.name}} {{addressDetails.address}}
				</view>
				<view class="address-express address-text d-bc" v-if="return_order_express.id !== 0">
					<view class="address-express-num">快递单号：{{return_order_express.express_no}}</view>
					<view class="d-cf">
						<view class="address-express-btn"
							@click="navTo(`/packageB/member/logistics?code=${return_order_express.express_no}&company_name=${return_order_express.company_name}`)">
							查看物流
						</view>
						<view class="iconfont icon-advertise-next"></view>
					</view>
				</view>
			</view>

			<view class="negotiation-record d-bf" @click="navTo('/packageB/member/negotiationRecord')">
				<text>协商记录</text>
				<text class="iconfont icon-advertise-next"></text>
			</view>

			<view class="mb_96"></view>
			<view 
				class="supplierBtn d-cc"
				v-if="details.after_sales_audit && details.after_sales_audit.status === -1 || details.status === -1"
				@click="navTo(`/packageB/member/refund?id=${details.order_item_id}`)">
				重新申请
			</view>
			
		</view>

		<view class="detail_pay d-ef" v-if="details.after_sales_audit && details.after_sales_audit.status === 0">
			<view class="order_delete"
				@click="navTo(`/packageB/member/refund?mid=7&orderId=${details.id}&id=${order_item.id}`)">修改申请</view>
			<view class="order_delete" @click="cancel">取消申请</view>
		</view>
		<view class="detail_pay d-ef" v-if="details.status === 1">
			<view class="order_delete"
				@click="navTo(`/packageB/expressage/expressage?after_sales_id=${details.id}&shipping_address_id=${shipping_address_id}`)">填写快递</view>
		</view>
		<u-modal width="300px" :content="hintContent" :showCancelButton="true" :show="orderShow" @cancel="orderCancel"
			@confirm="orderConfirm"></u-modal>
			
	</view>
</template>

<script>
	export default {
		data() {
			return {
				itemId: 0,
				id:0,
				shipping_address_id:null,
				orderUrl:'',
				hintContent: '是否确认取消申请',
				orderShow:false,
				contacts:'', //商家名字
				amount: 0,
				freight: 0,
				technical_services_fee: 0,
				afterSalesAudit:null, //售后状态
				pageType: '', //区别售后列表进来还是订单列表进来
				details: {},
				negotiation: {},
				addressDetails: {},
				province_name: {},
				city_name: {},
				district_name: {},
				return_order_express: {},
				order_item: {},
				supplier: {},
				image_url: '',
				reason_type: null
			}
		},
		onLoad(options) {
			if ((options.afterSaleId ?? '') !== '') {
				this.itemId = parseInt(options.afterSaleId);
			}
			if ((options.order ?? '') !== '') {
				this.pageType = options.order;
			}
			if (this.pageType === 'order') {
				 this.orderUrl = `/api/afterSales/getAfterSalesByOrderItemId?order_item_id=${this.itemId}`;
				
			} else {
				this.orderUrl = `/api/afterSales/get?id=${this.itemId}`;
			}
			
			uni.$on('refreshData',() => {
				this.detail(this.orderUrl);
			});
			this.detail(this.orderUrl);
		},
		onUnload() {
		  // 移除监听事件  
		   uni.$off('refreshData');  
		},
		methods: {
			largeIcon(item) {
				uni.previewImage({
					current: item,
					indicator: "number",
					loop: true,
					urls: this.details.detail_images
				})
			},
			orderCancel() {
					this.orderShow = false;
			},
			orderConfirm() {
				this.post('/api/afterSales/close', {
					id: this.id
				}, true).then((res) => {
					if (res.code === 0) {
						let data = res.data;
						console.log(data);
						this.toast(res.msg);
						this.orderShow = false;
						setTimeout(() => {
							if (this.pageType === 'order') {
								let orderUrl = `/api/afterSales/getAfterSalesByOrderItemId?order_item_id=${this.itemId}`;
								this.detail(orderUrl);
							} else {
								let orderUrl = `/api/afterSales/get?id=${this.itemId}`;
								this.detail(orderUrl);
							}
						}, 1000)
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			detail(url) { //商品信息
				this.get(url, {}, true).then((res) => {
					if (res.code === 0) {
						let data = res.data;
						this.details = data.after_sales;
						this.details.created_at = this.formatDateTime(this.details.created_at,6)
						this.afterSalesAudit = 
						this.id = data.after_sales.id;
						this.order_item = data.after_sales?.order_item;
						this.return_order_express = data.after_sales?.return_order_express;
						this.amount = this.toYuan(this.details.amount);
						this.freight = this.toYuan(this.details.freight);
						this.technical_services_fee = this.toYuan(this.details.technical_services_fee);
						this.negotiation = data.after_sales?.logs;
						if (data.after_sales.logs) { //传递协商记录
							uni.setStorageSync('negotiation', this.negotiation);
						}
						let addressId = data.after_sales?.order_id;
						this.findShopAddressByOrderId(addressId);
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			findShopAddressByOrderId(id) { //获取商家的地址和物流信息
				this.get(`/api/supplier/findShopAddressByOrderId?order_id=${id}`, {}, true).then((res) => {
					if (res.code === 0) {
						let data = res.data;
						this.addressDetails = data;
						this.shipping_address_id = data.id;
						this.supplier = data.supplier; //三级层次会报错
						this.province_name = data.province_name;
						this.city_name = data.city_name;
						this.district_name = data.district_name;
						this.contacts = data.contacts;
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			cancel() { //取消订单
				this.orderShow = true;
			}
		}
	}
</script>
<style lang="scss" scoped>
	::v-deep .u-modal__content {
		text-align: center;
	}
</style>
<style lang="scss">
	page {
		color: #333;
	}

	.aftersales-status {
		line-height: 92rpx;
		background-color: #f15353;
		color: #fff;
		text-align: left;
		font-size: 32rpx;
		padding: 32rpx 28rpx;
		margin-bottom: 20rpx;
	}

	.page-wrapper {
		padding: 20rpx 30rpx;

		.return-info {
			padding: 0 28rpx;
			background-color: #fff;
			border-radius: 20rpx;

			.return-info-text {
				height: 78rpx;
				font-size: 15px;
				color: #292929;
				border-bottom: 2rpx solid #f2f2f2;
			}

			.return-goods-desc {
				.return-goods-num {
					font-size: 22rpx;
					color: #717171;
				}

				.return-goods-price {
					font-size: 24rpx;
					color: #1e1e1e;
				}
			}

			.return-goods-list {
				border-bottom: 2rpx solid #f2f2f2;
				padding: 24rpx 0;

				.return-goods-face {
					image {
						width: 110rpx;
						height: 110rpx;
						border-radius: 6rpx;
					}
				}

				.return-goods-content {
					margin-left: 14rpx;
				}
			}

			.return-details {
				padding: 16rpx 0;
				text-align: left;
				font-size: 22rpx;
				color: #767676;

				.return-details-item {
					line-height: 54rpx;
					font-size: 26rpx;
					color: #292929;
					flex-wrap: wrap;

					.photoBox {
						position: relative;
						width: 140rpx;
						height: 140rpx;
						border: 1px dashed #c0ccda;
						margin: 16rpx 16rpx 16rpx 0;

						image {
							width: 100%;
							height: 100%;
						}
					}
				}
			}
		}

		//退货地址
		.address-info {
			padding: 0 27rpx 16rpx 27rpx;
			margin-top: 20rpx;
			text-align: left;
			background-color: #fff;
			border-radius: 18rpx;

			.address-express {
				// padding-bottom: 16rpx;
			}

			.address-text {
				line-height: 48rpx;
				font-size: 26rpx;
				color: #454545;
			}

			.address-info-text {
				height: 78rpx;
				font-size: 30rpx;
				color: #292929;
				border-bottom: 2rpx solid #f2f2f2;
			}

			.address-contact {
				padding-top: 16rpx;
			}

			.address-express-btn {
				font-size: 22rpx;
				color: #f14e4e;
			}

			.icon-advertise-next {
				color: #f14e4e;
			}
		}

		.negotiation-record {
			height: 80rpx;
			padding: 0 27rpx;
			margin-top: 20rpx;
			font-size: 15px;
			color: #292929;
			background-color: #fff;
			border-radius: 20rpx;
		}
	}
	.supplierBtn {
		width: 650rpx;
		height: 70rpx;
		position: relative;
		background-color: #f14e4e;
		border-radius: 6rpx;
		margin: 70rpx auto 0rpx auto;
		color: #fff;
	}
	.detail_pay {
		min-height: 96rpx;
		width: 100%;
		box-sizing: border-box;
		background: #fff;
		padding: 0 24rpx;
		border-top: 2rpx solid #ebebeb;
		position: fixed;
		bottom: 0;

		.order_delete {
			display: inline-block;
			min-width: 130rpx;
			margin-left: 16rpx;
			background: #fff;
			padding: 8rpx 20rpx;
			border-radius: 15px;
			color: #333;
			border: 2rpx solid #666;
		}
	}
</style>
