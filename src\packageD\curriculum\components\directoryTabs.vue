<template>
  <!-- <view>选项卡</view> -->
  <view class="bg-white m-20 radius15 pt_30 pl_23 pr_20 pb_28">
    <u-tabs
      :list="tabsList"
      :scrollable="false"
      :current="current"
      :activeStyle="{
        color: '#000000',
        fontWeight: 700,
        transform: 'scale(1.05)',
      }"
      lineColor="#ef1225"
      lineWidth="50"
      lineHeight="4"
      @click="clickTabs"
    ></u-tabs>
    <!-- 目录 -->
    <view class="curriculum" v-if="current === 0">
      <view v-if="curriculum.chapter_count !== 0">
        <view class="curriculum-count">
          共{{ curriculum.chapter_count }}章，
          {{ curriculum.subsection_count }}小节
        </view>
        <view class="curriculum-chapter">
          <u-collapse
            class="collapse"
            accordion
            :border="false"
            :value="chapterId"
          >
            <view
              v-for="item in chapter"
              :key="item.id"
              @click="chooseChapter(item.id)"
            >
              <u-collapse-item
                class="chapter-name"
                :title="item.chapter_name"
                :name="item.id"
              >
                <view
                  class="curriculum-subsection mb-20"
                  v-for="items in item.subsection"
                  :key="items.id"
                  @click="chooseSubsection(items)"
                >
                  <view
                    class="subsection-name d-f"
                    :class="{ 'is-active': isActive === items.id }"
                  >
                    <view
                      v-if="isActive !== items.id"
                      class="subsection-title"
                      style="
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        width: 500rpx;
                      "
                    >
                      {{ items.subsection_name }}
                    </view>
                    <view
                      v-else
                      class="subsection-title"
                      style="
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        width: 500rpx;
                        color: #f42121;
                      "
                    >
                      {{ items.subsection_name }}
                    </view>
                    <view v-if="curriculum.is_purchase === 0 && curriculum.retail_price !== 0">
                      <view
                        v-if="items.try"
                        class="try"
                        style="background-color: #ffeded; border-radius: 10rpx"
                      >
                        <text style="color: #e7555a; padding: 10rpx 8rpx">
                          试看
                        </text>
                      </view>
                      <u-icon
                        v-if="!items.try"
                        name="lock"
                        color="#9e9e9e"
                        size="40"
                      ></u-icon>
                    </view>
                  </view>
                  <view class="subsection-time d-f mt-10">
                    <u-icon
                      v-if="isActive === items.id"
                      name="clock"
                      size="28"
                      color="#f42121"
                    ></u-icon>
                    <u-icon v-else name="clock" size="28"></u-icon>
                    <text
                      v-if="isActive !== items.id"
                      class="lock ml-10"
                      :class="{ 'is-active': isActive === items.id }"
                    >
                      {{ items.video_minute }}:{{ items.video_second }}
                    </text>
                    <text
                      v-else
                      class="lock ml-10"
                      :class="{ 'is-active': isActive === items.id }"
                      style="color: #f42121"
                    >
                      {{ items.video_minute }}:{{ items.video_second }}
                    </text>
                  </view>
                </view>
              </u-collapse-item>
            </view>
          </u-collapse>
        </view>
        <view
          class="more"
          v-if="allChapter.length > 5 && showMore"
          @click="getMore"
        >
          <text>查看全部</text>
          <u-icon
            class="arrow-down"
            color="#9e9e9e"
            name="arrow-down"
            size="28"
          ></u-icon>
        </view>
      </view>
      <u-empty
        v-else
        icon="http://cdn.uviewui.com/uview/empty/data.png"
      ></u-empty>
    </view>
    <!-- 介绍 -->
    <view v-if="current === 1">
      <view v-if="detailed !== ''">
        <h3 class="mb40">介绍</h3>
        <view v-for="(item, index) in images" :key="index">
          <p>{{ item.text }}</p>
          <img
            v-for="(image, imgIndex) in item.images"
            :key="imgIndex"
            :src="image.src"
            :style="{ width: image.width }"
            alt="Image"
          />
        </view>
      </view>
      <u-empty
        v-else
        icon="http://cdn.uviewui.com/uview/empty/data.png"
      ></u-empty>
    </view>
    <!-- 评价 -->
    <view class="appraise" v-if="current === 2">
      <view v-if="total !== 0">
        <view class="appraise-title">
          <h3>
            评价
            <span v-if="total !== 0">({{ total }})</span>
          </h3>
          <text @click="toAppraise" class="appraise">
            好评率{{ curriculum.product.feedback_rate }}% >
          </text>
        </view>
        <view
          class="appraise-main"
          v-for="item in appraiseList.list"
          :key="item.id"
          @click="toAppraise"
        >
          <view class="appraise-avatar">
            <u-avatar class="mr-15" :src="item.avatar" size="60"></u-avatar>
            <view>
              <text>{{ item.nickname }}</text>
              <view class="rate">
                <u-rate
                  :value="item.level"
                  :readonly="true"
                  size="30"
                  inactiveColor="#9e9e9e"
                  activeColor="#fb9a3d"
                ></u-rate>
                <text>{{ item.level }}.0</text>
              </view>
            </view>
          </view>
          <view class="appraise-content">
            <text>{{ item.content }}</text>
          </view>
          <view class="appraise-img" v-if="item.imageUrls != ''">
            <img
              v-for="(item1, index1) in item.imageUrls"
              :key="index1"
              :src="item1"
              class="ml_15"
              style="width: 180rpx; height: 180rpx"
            />
          </view>
        </view>
        <view class="more" v-if="total > 10" @click="toAppraise">
          <text>查看全部</text>
          <u-icon
            class="arrow-down"
            color="#9e9e9e"
            name="arrow-down"
            size="28"
          ></u-icon>
        </view>
      </view>
      <u-empty
        v-else
        icon="http://cdn.uviewui.com/uview/empty/data.png"
      ></u-empty>
    </view>
  </view>
</template>

<script>
export default {
  name: 'directoryTabs',

  data() {
    return {
      isActive: '', // 选中时间颜色
      chooseSubsections: [], // 选中小节数据
      chapterId: '', // 打开章节ID
      tabsList: [{ name: '目录' }, { name: '介绍' }, { name: '评价' }], // tabs列表
      current: 0, // 默认显示tabs
      curriculum: [], // 课程信息
      chapter: [], // 课程信息
      allChapter: [], // 全部课程
      showMore: true, // 显示加载更多
      detailed: '', // 存储介绍的信息
      images: [], // 存储提取的图片数据
      page: 1, // 页数
      pageSize: 10, // 每页条数
      appraiseList: [], // 评价列表
      total: 0, // 总数
      imageUrls: [], // 评价图片
    }
  },

  onLoad() {},
  methods: {
    // 获取课程列表
    getCurriculum(data) {
      this.curriculum = data
      if (JSON.parse(data.chapter).length > 5) {
        this.chapter = JSON.parse(data.chapter).slice(0, 5)
        this.allChapter = JSON.parse(data.chapter)
        this.chapterId = this.allChapter[0].id
        this.chooseChapter(this.allChapter[0].id)
      } else {
        this.chapter = JSON.parse(data.chapter)
        this.chapterId = this.chapter[0].id
        this.chooseChapter(this.chapter[0].id)
      }
      this.detailed = data.detailed
      this.extractAndRenderContent()
      console.log('课程详情', data)
      console.log('章节详情', this.chapter)
    },
    // 点击tabs
    clickTabs(item) {
      this.current = item.index
    },
    // 加载更多
    getMore() {
      this.chapter = this.allChapter
      this.showMore = false
    },
    // 点击章节
    chooseChapter(id) {
      this.chapterId = id
      if (this.allChapter.length === 0) {
        const data = this.chapter.find(item => id === item.id)
        this.chooseSubsections = data.subsection
      } else {
        const data = this.allChapter.find(item => id === item.id)
        this.chooseSubsections = data.subsection
      }
    },
    // 点击小节
    async chooseSubsection(item) {
      if (!item.try && this.curriculum.is_purchase === 0 && this.curriculum.retail_price !== 0) {
        this.toast('请先购买')
        return
      }
      const val = this.chooseSubsections.find(items => item.id === items.id)
      this.isActive = val.id
      const data = {
        // product_id: parseInt(this.$route.query.id),
        product_id: parseInt(this.curriculum.product.id),
        curriculum_id: this.curriculum.id.toString(),
        chapter_id: this.chapterId,
        subsection_id: item.id,
        order_sn: '',
      }
      const res = await this.post('/api/Curriculum/getVideoUrl', data)
      if (res.code === 0) {
        this.$emit('videoUrl', res.data.url, item)
        // 试看时间结束
        if (item.try === 1 && this.curriculum.is_purchase === 0 && this.curriculum.retail_price !== 0) {
          const time = item.try_time * 60 * 1000
          setTimeout(() => {
            this.$emit('videoUrl', '')
            this.toast('请先购买')
          }, time)
        }
      }
    },
    // 将介绍渲染到页面
    extractAndRenderContent() {
      const tempElement = document.createElement('view')
      tempElement.innerHTML = this.detailed
      const pElements = tempElement.querySelectorAll('p')
      const extractedImages = []
      pElements.forEach(p => {
        const text = p.textContent.trim()
        const images = Array.from(p.querySelectorAll('img')).map(img => ({
          src: img.getAttribute('src'),
          width: '100%', // 设置默认宽度
        }))
        extractedImages.push({ text, images })
      })
      this.images = extractedImages // 将提取的图片数据存储到Vue实例的data中
    },
    // 获取评价信息
    async getAppraiseList() {
      const params = {
        // productId: parseInt(this.$route.query.id),
        productId: parseInt(this.curriculum.product.id),
        page: this.page,
        pageSize: this.pageSize,
      }
      const res = await this.get('/api/comment/list', params)
      if (res.code === 0) {
        this.appraiseList = res.data
        this.total = res.data.total
        // this.imageUrls = res.data
        for (var i = 0; i < this.appraiseList.list.length; i++) {
          //商品评论图片转为数组存储方式
          this.appraiseList.list[i].imageUrls =
            res.data.list[i].imageUrls.split(',')
        }
        // if (res.data.total > 1) {
        //   console.log('asdsadasdas');
        // } else {
        //   this.imageUrls.push(res.data.list[0].imageUrls)
        // }
      }
    },
    // 跳转到全部评价页面
    toAppraise() {
      // 跳转到全部评价页面
      this.navTo(
        '/packageB/goods/commentDetails/commentDetails?goodsID=' +
          this.$route.query.id,
      )
    },
  },
}
</script>

<style lang="scss" scoped>
// #ifdef H5
::v-deep .u-tabs__wrapper__nav__line {
  margin-left: 24rpx;
}
::v-deep .u-tag {
  background-color: #ffeeeb;
  border-width: 0rpx;
}
// #endif
// #ifdef MP-WEIXIN
::shadow .u-tabs__wrapper__nav__line {
  margin-left: 24rpx;
}
::shadow .u-tag {
  background-color: #ffeeeb;
  border-width: 0rpx;
}
// #endif
.curriculum {
  .curriculum-count {
    margin-top: 30rpx;
  }
  .curriculum-chapter {
    .collapse {
      .chapter-name {
        background-color: #f6f6f6;
        margin-top: 20rpx;
      }
      .curriculum-subsection {
        height: 120rpx;
        line-height: 50rpx;

        .subsection-name {
          display: flex;
          justify-content: space-between;
          align-items: center;
          // #ifdef MP-WEIXIN
          width: 500rpx;
          // #endif
          .subsection-title {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            // #ifdef H5
            width: 500rpx;
            // #endif
          }
          .try {
            background-color: #ffeded;
            border-radius: 10rpx;
            height: 40rpx;
            line-height: 40rpx;
            text {
              padding: 10rpx 8rpx;
              font-size: 24rpx;
              color: #e7555a;
            }
          }
        }
        .is-active {
          color: #f42121;
        }
        .subsection-time {
          display: flex;
          justify-content: start;
          align-items: center;
          color: #9e9e9e;
          .lock {
            margin-left: 10rpx;
          }
        }
      }
    }
  }
  .more {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 40rpx;
    color: #9e9e9e;
    .arrow-down {
      margin-left: 10rpx;
    }
  }
}
.mb40 {
  margin-bottom: 40rpx;
}
.appraise {
  margin-top: 20rpx;
  .appraise-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .appraise {
      font-size: 28rpx;
      color: #9e9e9e;
    }
  }
  .appraise-main {
    .appraise-avatar {
      display: flex;
      justify-content: start;
      align-items: center;
      margin-top: 20rpx;
      img {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 10rpx;
      }
      .rate {
        display: flex;
        justify-content: start;
        align-items: center;
        color: #fb9a3d;
      }
    }
    .appraise-content {
      margin-top: 10rpx;
    }
    .appraise-img {
      margin-top: 10rpx;
    }
  }
  .more {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 40rpx;
    color: #9e9e9e;
    .arrow-down {
      margin-left: 10rpx;
    }
  }
}
</style>
