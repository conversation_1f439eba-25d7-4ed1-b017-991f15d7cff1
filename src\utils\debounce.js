/**
 * @desc 函数防抖 ； 开始执行一次 和  开始结束都执行一次 
 * @param func 函数
 * @param wait 不执行延迟毫秒数
 * @param implement true 表示开始结束都执行一次，false 表示开始执行一次
 */
export const Debounce = (fn,wait, implement = true) => {
	
	let delay = wait || 500
	let timer
	return function() {
		let args = arguments;
		if (timer) {
			clearTimeout(timer)
		}
		
		let callNow = !timer;
		
		timer = setTimeout(() => {
			if(implement) {fn.apply(this, args);}
			timer = null;
		}, delay);
		
		if(callNow) fn.apply(this, args);
	}
};

/**
 * @desc 函数防抖
 * @param func 函数
 * @param wait 延迟执行毫秒数
 * @param immediate true 表立即执行，false 表非立即执行
 */
	export const  DebounceOrder = (func,wait,immediate) => {
			let timer;
			return function () {
				let context = this;
				let args = arguments;

				if (timer) clearTimeout(timer);
				if (immediate) {
					var callNow = !timer;
					timer = setTimeout(() => {
						timer = null;
					}, wait)
					if (callNow) func.apply(context, args)
				} else {
					timer = setTimeout(function(){
					func.apply(context, args)
					}, wait);
				}
			}
	}

/**
 * @desc 函数节流
 * @param func 函数
 * @param wait 延迟执行毫秒数
 * @param immediate true 表立即执行，false 表非立即执行
 */
export const ThrottleOrder = (func, wait, immediate) => {
	let lastTime = 0;
	let timer;
  
	return function () {
	  let context = this;
	  let args = arguments;
	  let now = Date.now();
  
	  if (immediate) {
		// 如果 immediate 为 true，表示第一次立即执行
		if (!lastTime) {
		  func.apply(context, args);
		}
		lastTime = now;
	  } else {
		// 如果 immediate 为 false，表示按时间间隔执行
		if (now - lastTime >= wait) {
		  func.apply(context, args);
		  lastTime = now;
		} else {
		  // 如果时间未到达间隔，则使用 setTimeout 延迟执行
		  if (!timer) {
			timer = setTimeout(function () {
			  func.apply(context, args);
			  lastTime = Date.now();
			  timer = null;
			}, wait - (now - lastTime));
		  }
		}
	  }
	};
  };
  