<template>
	<view>
		<!-- 支付 -->
		<view class="my_order_pays d-ef">
			<view class="pay_together d-cc" @click="allBillBtn">合并开票</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"payTogether",
		data() {
			return {
				
			};
		},
		methods:{
			allBillBtn() {
				this.$emit('allBillBtnTest');
			}
		}
	}
</script>

<style lang="scss" scoped>
	.my_order_pays {
		width: 100%;
		height: 100rpx;
		position: fixed;
		bottom: 0;
		left:0;
		background-color: #fff;
		.pay_together {
			width: 170rpx;
			height: 74rpx;
			background-color: #f15353;
			border-radius: 36rpx;
			color: #fff;
			margin-right: 30rpx;
		}
	}
</style>
