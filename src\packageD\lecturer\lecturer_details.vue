<template>
  <!-- 讲师主页 -->
  <view class="container">
    <view class="lecturer">
      <view class="lecturer-message">
        <img class="lecturer-img" :src="lecturer.img" alt="" />
        <view class="lecturer-name">{{ lecturer.lecturer_name }}</view>
      </view>
      <view class="lecturer-label">
        <u-tag
          v-for="item in label"
          :key="item"
          :text="item"
          color="#a5702c"
          bgColor="#FEEBCD"
          borderColor="#FEEBCD"
          size="mini"
          class="labelTag"
          shape="square"
          mode="light"
          type="warning"
        ></u-tag>
      </view>
      <p class="tip">{{ lecturer.introduce }}</p>
    </view>
    <h3 class="headline">主讲课程</h3>
    <!-- 课程 -->
    <view
      class="bg-white p-20 mb-20 ml-20 swhb radius10 mr-20"
      v-for="item in curriculum"
      :key="item.id"
      @click="jumpCurriculum(item.product_id)"
    >
      <view class="d-f">
        <view>
          <u--image
            :showLoading="true"
            :src="item.curriculum_img"
            width="150rpx"
            height="150rpx"
            radius="10"
          ></u--image>
        </view>
        <view class="ml-20">
          <view style="width: 501rpx" class="fs-2 ell">
            {{ item.curriculum_name }}
          </view>
          <view>
            <view class="d-bf mt_17 fs-1-5" style="width: 510rpx">
              <view class="c-7e7e7e fs-1">
                <view class="chapter-count">
                  <text>
                    {{ item.chapter_count }}章{{ item.subsection_count }}节
                  </text>
                </view>
              </view>
            </view>
            <view class="fw-b c-f14e4e mt-10 fs-1">
              <text>利润:</text>
              <text>
                {{ '¥' + toYuan(item.profit) }}
              </text>
            </view>
          </view>
        </view>
      </view>
      <view class="d-bf mt-25 pr-20 fs-1" v-if="item.is_display != 0">
        <view class="d-cc-c">
          <view class="c-f14e4e">
            <text v-if="item.retail_price !== 0">
              {{ '¥' + toYuan(item.price) }}
            </text>
            <text v-if="item.retail_price === 0">免费</text>
          </view>
          <view class="mt-15">供货价</view>
        </view>
        <view class="d-cc-c">
          <view class="c-f14e4e">
            <text v-if="item.retail_price !== 0">
              {{ '¥' + toYuan(item.cost_price) }}
            </text>
            <text v-if="item.retail_price === 0">免费</text>
          </view>
          <view class="mt-15">批发价</view>
        </view>
        <view class="d-cc-c">
          <view class="c-f14e4e">
            <text v-if="item.retail_price !== 0">
              {{ '¥' + toYuan(item.retail_price) }}
            </text>
            <text v-if="item.retail_price === 0">免费</text>
          </view>
          <view class="mt-15">建议零售价</view>
        </view>
        <view class="d-cc-c">
          <view v-if="item.retail_price !== 0">
            <view class="c-f14e4e" style="text-align: center">
              {{ item.product.sales }}
            </view>
            <view class="mt-15">已售</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'lecturerDetails',

  data() {
    return {
      lecturer: '', // 讲师循信息
      label: [], // 职称
      curriculum: [], // 课程列表
    }
  },

  onLoad(opation) {
    this.getLecturerDetails(opation.lecturerID)
  },

  methods: {
    // 获取讲师信息
    async getLecturerDetails(id) {
      const data = {
        // id: parseInt(this.$route.query.lecturerID),
        id: parseInt(id),
      }
      const res = await this.post(
        '/api/Curriculum/findLecturerAndCurriculum',
        data,
      )
      if (res.code === 0) {
        this.lecturer = res.data
        this.curriculum = res.data.curriculum
        if (res.data.label !== '') {
          this.label = res.data.label.split('|')
        }
      }
    },
    // 跳转课程详情
    jumpCurriculum(id) {
      this.navTo(
        '/packageA/commodity/commodity_details/commodity_details?id=' + id,
      )
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .u-tag {
  height: 34rpx;
  line-height: 34rpx;
  background-color: #faf6db;
  border: 0rpx;
  span {
    color: #9d783e;
  }
}
.container {
  width: 100vw;
  height: 100vh;
  background-color: #f6f6f6;
  .lecturer {
    margin: 80rpx 20rpx 0rpx 20rpx;
    border-radius: 20rpx;
    background: linear-gradient(to top, #ffffff 50%, #fbf1e4);
    .lecturer-message {
      .lecturer-img {
        width: 140rpx;
        height: 140rpx;
        border-radius: 50%;
        position: absolute;
        // #ifdef H5
        top: -55rpx;
        // #endif
        // #ifdef MP-WEIXIN
        top: 20rpx;
        // #endif
        left: 55rpx;
      }
      .lecturer-name {
        font-weight: 600;
        font-size: 40rpx;
        padding: 20rpx 0rpx 0rpx 180rpx;
      }
    }
    .lecturer-label {
      display: flex;
      justify-content: start;
      align-items: center;
      margin: 30rpx 0px 0rpx 40rpx;
       .labelTag {
        margin-right: 10rpx;
        font-size: 24rpx;
      }
    }
    .tip {
      color: rgba(0, 0, 0, 0.4);
      margin: 20rpx 0px 0rpx 40rpx;
      padding-bottom: 40rpx;
    }
  }
  .headline {
    margin: 30rpx 30rpx 30rpx 40rpx;
  }
  .chapter-count {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 5rpx;
    text {
      padding: 5rpx 10rpx;
    }
  }
}
</style>
