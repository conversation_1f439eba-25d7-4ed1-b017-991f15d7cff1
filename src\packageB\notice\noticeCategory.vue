<template>
  <view>
    <!-- 一级分类 -->
    <view class="tabs">
      <u-tabs
        :list="categoryData"
        keyName="title"
        lineWidth="30"
        lineHeight="5"
        lineColor="#F15353"
        @change="changeTbas"
        :activeStyle="{
          color: '#F15353',
          fontWeight: 'bold',
          transform: 'scale(1.05)',
        }"
        :inactiveStyle="{
          color: '#00001C',
          transform: 'scale(1)',
        }"
      ></u-tabs>
    </view>
    <!-- 二级分类 -->
    <view class="child-tabs">
      <u-tabs
        :list="articleCategoriesChild"
        keyName="title"
        lineHeight="0"
        :current="childCurrent"
        @change="changeTbasChild"
        :activeStyle="{
          color: '#F15353',
          fontWeight: 'bold',
          backgroundColor: '#f5e4e4',
          paddingLeft: '16rpx',
          paddingRight: '16rpx',
          paddingTop: '13rpx',
          paddingBottom: '13rpx',
          borderRadius: '12rpx 12rpx 12rpx 12rpx',
          border: '1rpx solid #F15353',
        }"
        :inactiveStyle="{
          color: '#6E6E79',
          backgroundColor: '#ffffff',
          paddingLeft: '16rpx',
          paddingRight: '16rpx',
          paddingTop: '13rpx',
          paddingBottom: '13rpx',
          borderRadius: '12rpx 12rpx 12rpx 12rpx',
          border: '1rpx solid #ffffff',
        }"
      ></u-tabs>
    </view>
    <!-- 文章列表 -->
    <view class="article-container">
      <view
        class="d-f articel-solid"
        v-for="(item, index) in articleList"
        :key="item.id"
        @click="chooseArticle(item)"
      >
        <view class="article-title">{{ index + 1 }}.{{ item.title }}</view>
        <u-icon
          class="mt-10"
          name="arrow-right"
          color="#D6D6DC"
          size="28"
        ></u-icon>
      </view>
    </view>
    <!-- <u-divider
      v-if="articleList.length !== 0"
      textSize="36"
      text="暂无更多数据"
    ></u-divider> -->
    <u-empty
      v-if="articleList.length === 0"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      textSize="30"
    ></u-empty>
  </view>
</template>

<script>
export default {
  name: 'noticeCategory',

  data() {
    return {
      categoryData: [], // 分类列表
      articleCategoriesChild: [], // 二级分类列表
      article_category1_id: null, // 一级分类ID
      article_category2_id: null, // 二级分类ID
      childCurrent: 0, // 子分类选中
      articleList: [], // 文章列表
      page: 1, // 当前页数
      pageSize: 15, // 每页条数
      total: null, // 总数
    }
  },

  mounted() {
    this.getArticleCategoriesAll()
  },
  // 触底加载
  async onReachBottom() {
    if (this.articleList.length < this.total) {
      this.page = this.page + 1
      const params = {
        page: this.page,
        pageSize: this.pageSize,
        article_category1_id: parseInt(this.article_category1_id),
        article_category2_id: parseInt(this.article_category2_id),
      }
      const res = await this.post('/api/article/list', params)
      if (res.code === 0) {
        this.articleList = this.articleList.concat(res.data.list)
      }
    }
  },

  methods: {
    // 获取全部分类
    async getArticleCategoriesAll() {
      const res = await this.get('/api/article/getArticleCategoriesAll')
      if (res.code === 0) {
        this.categoryData = res.data
        this.changeTbas(res.data[0], 'first')
      }
    },
    // 切换一节分类
    changeTbas(item, first) {
      this.article_category1_id = item.id
      this.articleCategoriesChild = item.articleCategoriesChild
      // 首次进入自动请求
      if (first) {
        this.changeTbasChild(item.articleCategoriesChild[0])
      } else {
        this.changeTbasChild(item.articleCategoriesChild[0], true)
      }
    },
    // 切换二级分类
    async changeTbasChild(item, flg = false) {
      this.childCurrent = flg ? 0 : item.index
      this.article_category2_id = item.id
      const params = {
        page: 1,
        pageSize: this.pageSize,
        article_category1_id: parseInt(this.article_category1_id),
        article_category2_id: parseInt(this.article_category2_id),
      }
      const res = await this.post('/api/article/list', params)
      if (res.code === 0) {
        this.articleList = res.data.list
        this.total = res.data.total
      }
    },
    // 选择文章
    chooseArticle(item) {
      this.navTo('/packageB/notice/noticeDetails?id=' + item.id)
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .u-tabs__wrapper__nav__item[data-v-0de61367] {
  padding: 0 13rpx;
}
.tabs {
  height: 108rpx;
  font-size: 28rpx;
  background-color: #ffffff;
}
.child-tabs {
  margin-top: 20rpx;
}
.article-container {
  width: 702rpx;
  margin: auto;
  margin-top: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  cursor: pointer;
  .articel-solid {
    padding-bottom: 25rpx;
    border-bottom: 1rpx solid #f5f5f5;
  }
  .article-title {
    width: 592rpx;
    height: 68rpx;
    line-height: 68rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #00001c;
    margin-left: 24rpx;
    margin-top: 30rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
