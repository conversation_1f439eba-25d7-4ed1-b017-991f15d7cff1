<template>
  <view style="background-color: #010101; height: 100vh">
    <!-- <view :style="{ height: menuButtonInfo.bottom + 8 + 'px', backgroundColor: '#010101', position: 'relative' }">
            <view class="f fac fjc top-view" :style="{ top: menuButtonInfo.top - 5 + 'px', left: '0' }">
                <view class="top" v-if="product_id">商品视频</view>
            </view>
            <text class="iconfont icon-member-left member" @click="toMember"></text>
        </view> -->
    <view
      class="f fjc top"
      :style="{ height: menuButtonInfo.bottom + 8 + 'px' }"
    >
      <view
        class="top-view"
        :style="{ top: menuButtonInfo.top - 8 + 'px', left: '0' }"
      >
        <view class="f fac" style="position: absolute; left: 20px; top: 13px">
          <text
            class="iconfont icon-member-left member"
            @click="toMember"
          ></text>
        </view>
        <view class="f fjc">
          <view class="top-title" v-if="product_id">商品视频</view>
        </view>
      </view>
    </view>
    <u-empty
      :style="{
        width: '100%',
        height: windowHeight - bottomHight + 'px',
      }"
      v-if="videoList.length === 0 || !videoList"
      text="暂无小视频"
      mode="list"
      marginTop="100rpx"
      textSize="28rpx"
      iconSize="150"
    ></u-empty>
    <swiper
      v-else
      :style="{
        width: '100%',
        height: windowHeight - bottomHight + 'px',
        backgroundColor: '#FFFFFF',
      }"
      :vertical="true"
      @change="handleSwiperChange"
    >
      <template>
        <swiper-item v-for="(item, index) in videoList" :key="index">
          <view v-if="item.id">
            <video
              :id="index + ''"
              :style="{
                width: '100vw',
                height: windowHeight - bottomHight + 'px',
                backgroundColor: '#010101',
              }"
              :http-cache="true"
              :controls="false"
              :page-gesture="false"
              :show-fullscreen-btn="false"
              :show-loading="false"
              :show-center-play-btn="false"
              :enable-progress-gesture="false"
              :custom-cache="false"
              :src="item.video_url"
              @click="pauseFn"
              :poster="item.cover_url"
              webkit-playsinline
              playsinline
              x5-playsinline
              x5-video-player-type="h5-page"
              preload="auto"
              x5-video-player-fullscreen="false"
              x-webkit-airplay="true"
              :muted="muted"
            ></video>
            <!-- 继续播放 -->
            <view class="play-view" v-if="status === 'pause'" @click="playFn(false)">
              <view class="iconfont icon-play"></view>
            </view>
            <!-- 左侧标题与商品部分 -->
            <view class="f1 left-view">
              <view class="c-white font_size18 font_w700" v-if="item.user.id">
                {{
                  item.user.nickname
                    ? item.user.nickname
                    : '用户' + item.user.username.slice(-4)
                }}
              </view>
              <view v-else></view>
              <view class="mt_21 c-white limit-text-2">{{ item.title }}</view>
              <view
                class="mt_21 goods-view main-space"
                @click.stop="
                  navTo(
                    '/packageA/commodity/commodity_details/commodity_details?id=' +
                      item.product.id,
                  )
                "
              >
                <u--image
                  :src="item.product.image_url"
                  radius="16rpx"
                  width="96rpx"
                  height="96rpx"
                ></u--image>
                <view class="c-white ml_15 goods-tp">
                  <view class="limit-text-1 title">
                    {{ item.product.title }}
                  </view>
                  <view class="f fac">
                    <view class="c-f14d4d font_w700">
                      <text class="font_size12">￥</text>
                      {{ toYuan(item.product.price) }}
                    </view>
                    <view class="c-c1 ml_15">
                      <text>原价:</text>
                      <span style="text-decoration: line-through" class="ml_10">
                        <text class="font_size12">￥</text>
                        <span>{{ toYuan(item.product.origin_price) }}</span>
                      </span>
                    </view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 右侧logo/点赞/转发 -->
            <view class="right-view ml_100">
              <u-avatar
                :src="item.user.avatar"
                size="80rpx"
                shape="circle"
                @click="goUserVideo(item.user.id)"
              ></u-avatar>
              <view class="item mt_42" @click="likeClick(item, index)">
                <view
                  :style="isLike === 1 ? 'color: red;' : 'color:#ffffff'"
                  class="iconfont icon-bc_like"
                ></view>
                <view>{{ item.like_num }}</view>
              </view>
              <view class="item mt_30">
                <!-- #ifdef MP-WEIXIN -->
                <button
                  open-type="share"
                  class="clear-btn c-white font_size14"
                  style="line-height: 45rpx"
                >
                  <view class="iconfont icon-zb_all_share"></view>
                  <view>{{ item.forwarding_num }}</view>
                </button>
                <!-- #endif -->
                <!-- #ifdef APP-PLUS || H5 -->
                <button
                  class="clear-btn c-white font_size14"
                  style="line-height: 45rpx"
                  @click="$store.commit('upShadeShow', true)"
                >
                  <view class="iconfont icon-zb_all_share"></view>
                  <view>{{ item.forwarding_num }}</view>
                </button>
                <!-- #endif -->
              </view>
            </view>

            <!-- 遮罩层 -->
            <view class="shade-view" @click="shadefun"></view>
          </view>
        </swiper-item>
      </template>
    </swiper>
    <!-- 遮罩层 -->
    <shade></shade>
  </view>
</template>

<script>
import shade from '../../../common/shade/shade.vue'
export default {
  components: { shade },
  data() {
    return {
      menuButtonInfo: {
        top: 5,
        bottom: 45,
      },
      tabCurrent: 0,
      windowHeight: 0,
      bottomHight: 0,
      muted: true,
      status: 'pause', // 视频 play播放 pause暂停
      isLike: 0, // 点赞
      id: null, // 视频id
      product_id: null, // 商品id
      user_id: null,
      videoList: [], // 展示数组
      displayIndex: 0, // 记录videoList下标
      total: 0,
      s: null, // 向上滑动还是向下滑动 1，向下 0，向上
      isShow: false,

      isPlay: false
    }
  },
  async onLoad(e) {
    if (e.product_id) {
      this.id = parseInt(e.id)
      this.product_id = parseInt(e.product_id)
      let res = await this.post('/api/video/center/list', {
        page: 1,
        pageSize: 9999,
        product_id: this.product_id,
      })
      if (res.code === 0) {
        this.videoList = res.data.list
        this.total = res.data.total
        let k = this.videoList.findIndex(item => item.id === this.id)
        let element = this.videoList.splice(k, 1)[0]
        this.videoList.unshift(element)
        // await this.playFirst() // 播放
        await this.getIsLike() // 获取是否打赞

        if (this.checkWenxin()) {
          // h5分享参数变动 微信浏览器分享
          let share = this.videoList[this.displayIndex]
          let arr = [
            { linkName: 'product_id', linkNum: this.product_id },
            { linkName: 'id', linkNum: share.id },
            { linkName: 'pause', linkNum: 'false' },
          ]
          this.setWeixinShare(share.title, '', share.cover_url, arr)
        }
      }
    }
    // #ifdef H5
    // pause == true  代表是从别的页面进入 没有则是直接点击链接进入此页面
    if (e.pause !== 'true' && e.id) {
      //  h5分享 分享次数加1 分享的视频会放在首位所以直接调用分享接口即可
      await this.linkfun()
    }
    // #endif
  },
  async onShow() {
    this.windowHeight = uni.getSystemInfoSync().windowHeight
    let bottomHight = uni.getSystemInfoSync().safeAreaInsets.bottom
    if (uni.getSystemInfoSync().platform == 'ios') {
      this.bottomHight = bottomHight ? bottomHight : 32
    }
    // #ifdef MP-WEIXIN
    this.menuButtonInfo = uni.getMenuButtonBoundingClientRect()
    // #endif
  },
  async onReady() {
    // #ifdef H5
    this.pauseFn() // h5进入暂停视频
    // #endif
    // #ifdef MP-WEIXIN
    await this.playFirst() // 播放
    // #endif
  },
  // 分享给朋友
  onShareAppMessage() {
    let that = this
    that.linkfun()
    return {
      title: that.videoList[that.displayIndex].title,
      imageUrl: that.videoList[that.displayIndex].cover_url,
      path:
        '/packageE/marketingTool/miniVideo/miniVideo?product_id' +
        this.product_id +
        '&id=' +
        that.videoList[that.displayIndex].id,
    }
  },
  // 分享朋友圈
  onShareTimeline() {
    let that = this
    that.linkfun()
    // 分享朋友圈
    return {
      title: that.videoList[that.displayIndex].title,
      imageUrl: that.videoList[that.displayIndex].cover_url,
      path:
        '/packageE/marketingTool/miniVideo/miniVideo?product_id' +
        this.product_id +
        '&id=' +
        that.videoList[that.displayIndex].id,
    }
  },
  watch: {
    async displayIndex(k, old_k) {
      this.isShow = false
      uni.createVideoContext(old_k + '', this).pause()
      this.isShow = true
      this.getIsLike()
    },
  },

  methods: {
    // <------------------跳转页面 ---------------------->
    // 去往我的
    toMember() {
      this.navBack()
    },
    // 去往作者视频
    goUserVideo(id) {
      this.navTo('/packageE/marketingTool/miniVideo/authorVideo?id=' + id)
    },
    // swiper切换
    async handleSwiperChange(event) {
      this.displayIndex = event.detail.current
      // 获取数据后播放
      if (uni.getSystemInfoSync().platform == 'ios' && this.isPlay == true) {
        await this.playFn(true)
      } else if (uni.getSystemInfoSync().platform != 'ios') {
        await this.playFn(true)
      } else {
        await this.pauseFn()
      }
      let share = this.videoList[this.displayIndex];

      if (this.checkWenxin()) {
        // h5分享参数变动 微信浏览器分享
        let share = this.videoList[this.displayIndex]
        let arr = [
          { linkName: 'product_id', linkNum: this.product_id },
          { linkName: 'id', linkNum: share.id },
          { linkName: 'pause', linkNum: 'false' },
        ]
        this.setWeixinShare(share.title, '', share.cover_url, arr)
      }
    },

    // <------------------视频操作相关 ---------------------->
    // 获取是否点赞
    async getIsLike() {
      let res = await this.post('/api/video/user/getLikeById', {
        id: parseInt(this.videoList[this.displayIndex].id),
      })
      if (res.code === 0) {
        this.isLike = res.data.is_like
      }
    },
    // 点赞或取消点赞
    async likeClick(item, index) {
      // 判断登录
      let user = uni.getStorageSync('user')
      if (!this.checkNull(user)) {
        this.$toast('未登录,3秒后跳至登录页', 3000)
        setTimeout(() => {
          this.$fn.navTo('/pages/login/login')
        }, 3000)
        return
      }
      let res
      // 已登录处理 新增/减少点赞数量
      switch (this.isLike) {
        case 1: // 已点赞
          // 取消点赞
          res = await this.post('/api/video/user/reduceLikeNum', {
            id: item.id,
          })
          this.isLike = res.code === 0 ? 0 : 1
          this.$set(this.videoList[index], 'like_num', item.like_num - 1)
          break
        case 0: // 未点赞
          // 点赞
          res = await this.post('/api/video/user/addLikeNum', { id: item.id })
          this.isLike = res.code === 0 ? 1 : 0
          this.$set(this.videoList[index], 'like_num', item.like_num + 1)
          break
      }
    },
    // 首次加载播放第一个视频
    playFirst() {
      uni
        .createVideoContext(this.displayIndex + '', this)
        .play()
      this.status = 'play'
    },
    // 播放
    async playFn(clearTime = false) {
      if (clearTime) {
        uni.createVideoContext(this.displayIndex + '', this).seek(0)
      } else {
        this.isPlay = true
      }
      this.status = 'play'
      this.muted = false
      setTimeout(() => {
        uni.createVideoContext(this.displayIndex + '', this).play()
      }, 500)
    },
    // 暂停
    pauseFn() {
      this.muted = true
      this.status = 'pause'
      setTimeout(() => {
        uni.createVideoContext(this.displayIndex + '', this).pause()
      }, 500)
    },
    shadefun() {
      if (this.status === 'play') {
        this.pauseFn()
      } else {
        this.playFn(false)
      }
    },

    // <------------------ 分享相关 ---------------------->
    linkfun() {
      this.post('/api/video/addForwardingNum', {
        id: this.videoList[this.displayIndex].id,
      }).then(res => {
        if (res.code === 0) {
          this.$set(
            this.videoList[this.displayIndex],
            'forwarding_num',
            this.videoList[this.displayIndex].forwarding_num + 1,
          )
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.top {
  width: 100%;
  z-index: 10000;
  position: fixed;
  background-image: url('https://yxgyl.obs.cn-south-1.myhuaweicloud.com/2025225/b4158855a711d8e43cd219c9a3911f1d');
  background-repeat: no-repeat;
  /* 禁止图片重复 */
  background-size: 100% 100%;
  background-position: center center;
  /* 让图片居中显示 */

  .top-view {
    position: fixed;
    width: 100%;

    .top-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #ffffff;
      margin-top: 24rpx;
    }
  }
}

.iconfont {
  font-size: 58rpx;
}

.member {
  bottom: 32rpx;
  left: 32rpx;
  font-size: 35rpx;
  color: #ffffff;
}

.play-view {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;

  .icon-play {
    font-size: 92rpx;
  }
}

.left-view {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  position: absolute;
  padding-bottom: 20rpx;
  bottom: 0;
  left: 0;
  padding-left: 23rpx;
  width: calc(100% - 260rpx);
  z-index: 1000;

  .goods-view {
    display: flex;
    padding-top: 20rpx;
    padding-bottom: 20rpx;
    padding-left: 20rpx;
    border-radius: 20rpx;
    background-color: rgba($color: #000000, $alpha: 0.5);

    // 白色边框阴影 区分黑背景
    // box-shadow: 0px 0px 5rpx rgba($color: #FFFFFF, $alpha: 0.5);
    .goods-tp {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
    }
  }
}

.right-view {
  width: 80rpx;
  padding-right: 24rpx;
  position: absolute;
  padding-bottom: 20rpx;
  bottom: 0;
  right: 0;
  z-index: 1000;

  .item {
    color: #ffffff;
    text-align: center;

    .iconfont {
      font-size: 58rpx;
    }
  }
}

.shade-view {
  width: 100%;
  height: 370rpx;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-image: url('https://yxgyl.obs.cn-south-1.myhuaweicloud.com/2025225/94497541c987415435678370e3408f45');
  background-repeat: no-repeat;
  /* 禁止图片重复 */
  background-size: 100% 100%;
  background-position: center center;
}

.clear-btn {
  border-radius: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
  padding: unset;
  margin: unset;

  &::after {
    border: unset;
  }
}

.icon-fabu {
  background: #f15353;
  height: 80rpx;
  width: 80rpx;
  border-radius: 50%;
  color: #ffffff;
  font-size: 32rpx;
}

.title {
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.limit-text-2 {
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
