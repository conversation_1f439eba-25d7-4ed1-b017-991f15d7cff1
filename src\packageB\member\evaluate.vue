<!-- 订单评论列表 -->
<template>
	<view>
		<view class="shop">
			<evaluate-goods :evaluateList="evaluateOrder"></evaluate-goods>
			
			<view class="pjall">
				<view class="pjallTitle">评论:</view>
				<view>
					<u--textarea v-model="formData.content" placeholder="请输入内容" height="140rpx"></u--textarea>
				</view>
				<view class="pjallTitle">晒图:</view>
				<view>
					<u-upload
							width="140rpx"
							height="140rpx"
							:fileList="evaluateImages"
							@afterRead="afterRead"
							@delete="deletePic"
							name="1"
							multiple
							:maxCount="10"
							:previewFullImage="true"
						></u-upload>
				</view>
				<view class="d-bf">
					<view class="pjallTitle">商品评分:</view>
					<uni-rate v-model="formData.level" active-color="red" />
				</view>
				<view class="d-bf">
					<view class="pjallTitle">描述相符:</view>
					<uni-rate v-model="formData.des_level" active-color="red" />
				</view>
				<view class="d-bf">
					<view class="pjallTitle">卖家服务:</view>
					<uni-rate v-model="formData.shop_level" active-color="red"/>
				</view>
				<view class="d-bf">
					<view class="pjallTitle">物流服务:</view>
					<uni-rate v-model="formData.express_level"  active-color="red" />
				</view>
			</view>
			
			
		</view>
		<view class="submit-btn" @click="evaluateBtn">提交评价</view>
	</view>
</template>

<script>
	import evaluateGoods from '@/packageB/components/evaluateGoods.vue';
	export default {
		components:{
			evaluateGoods //商品信息
		},
		data() {
			return {
				id:0,
				orderDetail:{},
				evaluateOrder:{},
				evaluateImages:[],
				remarkImages:[],
				formData:{
					content:'',  //评价内容
					des_level:0, //	描述相符
					express_level:0, //物流服务
					level:0, //商品总评价
					shop_level: 0,//卖家服务
					avatar: '', //用户头像
					imageUrls: '', //评价图片
					nickname: '', //昵称
					orderId: 0, // 订单id
					productId: 0, //	商品id
					order_item_id:0, //子订单id
					product_attr: '', //商品规格名称
					type: 1, //1评论2回复
				}
			}
		},
		onLoad(options) {
			if((options.id??'') !== '') {
				this.id = parseInt(options.id);
			}
			if((options.details??'') !== '') {
				this.evaluateOrder = JSON.parse(options.details);
				this.formData.orderId = this.evaluateOrder.order_id;
				this.formData.productId = this.evaluateOrder.product_id;
				this.formData.order_item_id = this.evaluateOrder.id;
				this.formData.product_attr = this.evaluateOrder.sku_title;
				console.log(this.evaluateOrder);
				
			}
		},
		onShow() {
			this.getIndex();
		},
		methods: {
			// 新增图片
			async afterRead(event) {
				// 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.evaluateImages.length
				lists.map((item) => {
					this.evaluateImages.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let item = this.evaluateImages[fileListLen];
					this.evaluateImages.splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: result
					}))
					fileListLen++
				}
			},
			deletePic(event) { //删除图片
				this.evaluateImages.splice(event.index, 1);
				this.remarkImages.splice(event.index, 1);
			},
			uploadFilePromise(url) {
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: this.api.host + '/api/common/upload',
						filePath: url,
						name: 'file',
						success: (res) => {
							setTimeout(() => {
								resolve(res.data.data)
								let data = JSON.parse(res.data);
								console.log(this.evaluateImages);
								this.remarkImages.push(data.data.file.url);
							}, 1000)
						}
					});
				})
			},
			evaluateBtn() { //提交评论
				console.log(this.formData);
				if(this.formData.level === 0) {
					this.toast('请选择商品打分');
				} else if(this.formData.des_level === 0) {
					this.toast('请选择描述打分');
				} else if (this.formData.shop_level === 0) {
					this.toast('请选择卖家服务打分');
				} else if (this.formData.shop_level === 0) {
					this.toast('请选择物流服务打分');
				} else {
					let evaluateImages = this.remarkImages;
					this.formData.imageUrls = evaluateImages.join(',');
					this.post('/api/comment/create', this.formData, true).then((res) => {
						console.log(res);
						if(res.code === 0) {
							this.toast(res.msg);
							setTimeout(() => {
								this.backRefresh();
							},1000)
						} else {
							this.toast(res.msg);
						}
					}).catch((Error) => {
						console.log(Error);
					})
				}
			},
			getIndex() { //获取用户最新头像和昵称
				this.post('/api/center/index', {}, true).then(data => {
					let user = data.data.user;
					this.formData.avatar = user.avatar;
					this.formData.nickname = user.nickname;
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.shop {
		background: #fff;
		padding: 20rpx 0 0 0;
		.pjall {
			background: #fff;
			padding: 20rpx 28rpx;
			.pjallTitle {
				text-align: left;
				line-height: 64rpx;
			}
		}
	}
	.submit-btn {
		width:90%;
		height: 92rpx;
		line-height: 92rpx;
		margin: 40rpx auto 0 auto;
		color: #fff;
		background-color: #f15353;
		text-align: center;
		border-radius: 15rpx;
	}
</style>
