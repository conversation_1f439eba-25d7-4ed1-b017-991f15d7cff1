<template>
	<view>
		<view class="wallet-list" v-if="!$isEmpty.isEmpty(withdrawList)">
				<block v-for="(item,index) in withdrawList" :key="index" >
					<view class="item d-bf" @click="balanceBtn(item)">
						<view class="d-f-c font_size12 c-29">
							<view class="mb_30 " >{{item.title}}</view>
							<view>剩余：{{item.balance.toFixed(2) || 0.00 }}</view>
						</view>
						<view class="d-f-c text-r">
							<view class="mb_30  c-green " v-if="item.business_type === 1 || item.business_type === 2 || item.business_type === 6">-{{item.amount.toFixed(2)}}</view>
							<view class="mb_30 c-orange" v-else>+{{item.amount.toFixed(2)}}</view>
							<view class="font_size11 c-8a">{{item.created_at}}</view>
						</view>
					</view>
				</block>
		</view>
		<view v-else class="bg-white white-pb">
			<u-empty mode="data">
			</u-empty>
		</view>
		
	</view>
</template>

<script>
	
	export default {
		name:"withdrawList",
		props:{
			withdrawList: {
				type:Array,
				default:[]
			},
		},
		data() {
			return {
				// balanceItem:{}
			};
		},
		methods:{
			balanceBtn(item) {
				// this.balanceShow = true;
				console.log(item);
				this.$emit('withdrawItem',item);
				// this.balanceItem = item;
				// console.log(this.balanceItem);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.wallet-list {
		background-color: #fff;
		padding: 0 26rpx;
			.item {
				padding: 28rpx  0 26rpx;
				border-bottom: 1px solid #f2f2f2;
				&:last-of-type {
					border-bottom: none;
				}
			}
	}
</style>
