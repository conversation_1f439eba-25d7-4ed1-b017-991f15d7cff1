<!-- 地址列表组件 -->
<template>
	<view>
		<u-popup  :show="addressShow"
							:round="20"
							:closeable="true"
							@close="closeAddress('msg')" 
							:zIndex="10080"
							:overlay="false"
							>
			<view class="address-main">
				<view class="invoice_title d-cc">
					<view class="title c-20 flex_max"></view>
					<view class="d-cf ">
						<!-- <view class="iconfont icon-close11 title_close" @click="close"></view> -->
					</view>
				</view>
				<view class="blank-text" v-if="goodsAddList.length === 0">当前暂无地址记录！</view>
				<scroll-view v-else scroll-y class="address-list" @scrolltolower="loadingList">
					<block v-for="(item,index) in goodsAddList" :key="item.id">
						<view class="address-list-child d-cf" >
							<view class="address-list-child-left flex_max" @click="defaultAdd(item)">
								<view class="address-namenick d-f ">
									<view class="f-bold">{{item.realname}} {{item.mobile}}</view>
									<view class="isdefault" v-if="item.is_default">默认</view>
								</view>
								<view class="ell">{{item.province}} {{item.city}} {{item.county}} {{item.town}} {{item.detail}}</view>
							</view>
							<view class="address-list-child-right" @click="editAddress(item)">编辑</view>
						</view>
					</block>
				</scroll-view>
				<!-- <view class="address-list" v-else>
					<block v-for="(item,index) in goodsAddList" :key="item.id">
						<view class="address-list-child d-cf" >
							<view class="address-list-child-left flex_max" @click="defaultAdd(item)">
								<view class="address-namenick d-f ">
									<view class="f-bold">{{item.realname}} {{item.mobile}}</view>
									<view class="isdefault" v-if="item.is_default">默认</view>
								</view>
								<view class="ell">{{item.province}} {{item.city}} {{item.county}} {{item.town}} {{item.detail}}</view>
							</view>
							<view class="address-list-child-right" @click="editAddress(item)">编辑</view>
						</view>
					</block>
					
				</view> -->
				<view class="confirm-address-btn" @click="addressOpen('form',chooseName)">新增地址</view>
			</view>
		</u-popup>
		
	</view>
</template>

<script>
	import eventBus from '@/utils/eventBus'
	export default {
		name:"receivingPopup",
		components:{
		},
		data() {
			return {
				goodsAddList:[],
				addressForm:{},
				
				chooseName:'',
				addressShow:false,
				page: 1,
				pageSize: 10,
				total: 0
			};
		},
		created() {
			eventBus.$on('upaddressList', (val) => { //使用bus兄弟传值
				this.addressShow = val;
			})
			eventBus.$on('refresh', (val) => { //刷新页面
				setTimeout(() => {
					console.log(111);
					this.goodsAddList = []
					this.addressListData();
				},1000);
			})
			eventBus.$on('chooseName', (val) => { //使用bus兄弟传值
				this.chooseName = val;
			})
		},
		computed:{
		},
		mounted() {
			this.addressListData();
		},
		beforeDestroy(){
			eventBus.$off("upaddressList");
		},
		destroyed(){
			eventBus.$off("chooseName");
			// eventBus.$emit('orderAddress', item);
		},
		methods:{
			loadingList(){
				if(this.page > 1 && this.addressListData.length < this.total){
					this.page += 1
					this.addressListData()
				}
			},
			addressListData(page = this.page, pageSize = this.pageSize) { //地址信息
				this.post('/api/address/list', {page,pageSize}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						this.total = res.data.total
						if(this.goodsAddList.length < this.total){
							this.goodsAddList = [...this.goodsAddList, ...data.list]
						}
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					// console.log(Error);
				})
			},
			editAddress(item) { //解构赋值编辑
				this.addressName = item.province + item.city + item.county
				this.addFormShow = true;
				eventBus.$emit('overlay', 10085);
				eventBus.$emit('editForm', item,this.addressName,this.addFormShow); //传递编辑地址的
			},
			closeAddress(type) {
				if(type === 'msg' && this.chooseName === 'order') {
					this.addressShow = false;
					eventBus.$emit('overlayBill', false); 
				} else if (this.chooseName !== 'order') {
					this.addressShow = false;
				} else {
					// console.log('剩下的添加')
				}
				this.page = 1
				this.pageSize = 10
				this.total = 0
				eventBus.$emit('overlay', 10070);
			},
			addressOpen(type,name) {
					this.chooseName = name;
					eventBus.$emit('addFormShow', true,'append'); //传一个标识符，判断是否新增还是编辑
					eventBus.$emit('overlay', 10085);
			},
			defaultAdd(item) {
				if(this.chooseName === 'order') {
					eventBus.$emit('orderAddress', item);
					eventBus.$emit('overlayBill', false);
				} else {
					eventBus.$emit('invoiceAddress', item);
					eventBus.$emit('overlay', 10070);
					
				}
				this.addressShow = false;
			},
		},
	}
</script>

<style lang="scss" scoped>
	
	.address-main {
		padding: 80rpx 28rpx 0 28rpx;
		.address-list {
			max-height: 576rpx;
			overflow: scroll;
			position: relative;
			.address-list-child {
				padding: 28rpx 0 0 0;
				.address-list-child-left {
					padding-right: 16rpx;
					.address-namenick {
						.isdefault {
							color: #f14e4e;
							margin-left: 10rpx;
						}
					}
				}
				.address-list-child-right {
					width: 120rpx;
					height: 56rpx;
					line-height: 56rpx;
					color: #f14e4e;
				}
			}
		}
		.blank-text {
			padding: 64rpx 0 0 0;
			text-align: center;
			font-size: 32rpx;
			color: #666;
		}
		.confirm-address-btn {
			// position: absolute;
			
			// left: 50%;
			// -webkit-transform: translateX(-50%);
			// transform: translateX(-50%);
			width: 728rpx;
			height: 88rpx;
			bottom: 32rpx;
			background-color: #f14e4e;
			border-radius: 40rpx;
			margin:64rpx auto 0;
			text-align: center;
			margin-bottom: 32rpx;
			font-weight: 700;
			font-size: 32rpx;
			color: #fff;
			line-height: 88rpx;
		}
	}
</style>
