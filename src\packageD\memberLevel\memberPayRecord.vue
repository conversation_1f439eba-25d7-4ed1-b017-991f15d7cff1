<!-- 会员开通记录 -->
<template>
  <view class="mt_30 pb_20">
    <!-- #ifdef H5-->
    <text class="con-title">开通记录</text>
    <!-- #endif -->
    <block v-for="item in tableData" :key="item.id">
      <view class="m-card-view mt_20 mb_20 ml_20 mr_20">
        <view class="m-card-title d-cf mb_20">
          {{ formatDateTime(item.created_at) }}
        </view>
        <view class="con-record">
          <view class="item-record">
            <text class="font_size14">
              开通前等级：{{ item.before_level_name }}
            </text>
          </view>
          <view class="item-record">
            <text class="font_size14">
              开通后等级：{{ item.after_level_name }}
            </text>
          </view>
          <view class="item-record">
            <text class="font_size14">
              <!-- 开通前有效期：{{ formatValidity(item.before_validity_at) }} -->
              开通前有效期：{{ item.before_validity }}
            </text>
          </view>
          <view class="item-record">
            <text class="font_size14">
              <!-- 开通后有效期：{{ formatValidity(item.after_validity_at) }} -->
              开通后有效期：{{ item.after_validity }}
            </text>
          </view>
          <view class="item-record">
            <text class="font_size14">
              开通类型：{{ formatPurchase(item.purchase_type) }}
            </text>
          </view>
          <view class="item-record">
            <text class="font_size14">
              支付金额（元）：{{ toYuan(item.order_amount) }}
            </text>
          </view>
          <view class="item-record">
            <text class="font_size14">
              <!-- 支付状态：{{ formatOrderStatus(item.order_status) }} -->
              支付状态：{{ item.order_status_name }}
            </text>
          </view>
          <view class="item-record">
            <text class="font_size14">支付方式：{{ item.order_pay_name }}</text>
          </view>
        </view>
      </view>
    </block>
  </view>
</template>
<script>
export default {
  name: 'memberPayRecord',
  data() {
    return {
      tableData: [],
      page: 1,
      pageSize: 10,
      total: 0,
    }
  },
  onShow() {
    this.getRecord()
  },
  methods: {
    formatValidity(value) {
      value = this.formatDateTime(value)
      let result = ''
      if (value !== '长期有效') {
        const end = value.indexOf(' ')
        result = value.slice(0, end)
      }
      return result
    },
    formatPurchase(v) {
      let s = ''
      switch (v) {
        case 1:
          s = '升级'
          break
        case 2:
          s = '续费'
          break
      }
      return s
    },
    formatOrderStatus(v) {
      let s = ''
      switch (v) {
        case 1:
          s = '已支付'
          break
        case 2:
          s = '已退款'
          break
      }
      return s
    },
    getRecord() {
      const params = {
        page: this.page,
        pageSize: this.pageSize,
      }
      this.get('/api/user/getPurchaseRecordList', params).then(res => {
        if (res.code === 0) {
          this.tableData = res.data.list
          this.total = res.data.total
        } else {
          this.$message.error(res.msg)
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.m-card-view {
  .m-card-title {
    font-size: 32rpx;
  }
}
::v-deep .u-grid-item.data-v-99a45d26 {
  align-items: left;
}
.con-record {
  display: flex;
  flex-wrap: wrap;
  .item-record {
    margin-bottom: 20rpx;
    width: 48%;
  }
  .item-record:nth-child(2n) {
    margin-left: 2%;
  }
}
.con-title {
  display: block;
  margin: 40rpx 0;
  text-align: center;
  font-size: 28rpx;
}
</style>
