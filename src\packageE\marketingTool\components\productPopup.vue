<template>
    <view>
        <u-popup :show="show" :round="30" mode="bottom" @close="groupClose">
            <view class="classify">
                <view class="classify_title d-bf">
                    <view class="c-b5 font_size13" @click="noGroupChange">暂不添加</view>
                    <view class="c-20 font_size17">添加商品</view>
                    <view class="font_size13 c-orange yuan f fac fjc" @click="groupClose">
                        <u-icon name="close" size="26"></u-icon>
                    </view>
                </view>
                <view class="d-bf pr_20 pt_14 pb_14 mt_20">
                    <view class="d-cc">
                        <view style="width: 680rpx">
                            <u-search height="60" bgColor="#eff0f1" color="#666666" @custom="searchBt(boxValue)"
                                @search="searchBt(boxValue)" v-model="boxValue" searchIconSize="40"
                                :placeholder="defaultValue"></u-search>
                        </view>
                    </view>
                </view>
                <view class="classify_content">
                    <u-radio-group v-model="product_id" placement="column" @change="groupChange">
                        <scroll-view class="scrollStyle" scroll-y="true" lower-threshold="100"
                            @scrolltolower="scrolltolower">
                            <template v-for="item in list">
                                <view class="option f fac fjsb" :key="item.id" @click="groupChange(item.id)">
                                    <view class="f fac">
                                        <image :src="item.thumb" mode="widthFix" class="product-img" />
                                        <view>
                                            <view class="product-title">{{ item.title }}</view>
                                            <view class="f fac">
                                                <view class="product-num">￥<span class="product-num2">{{
                                                        toYuan(item.price) }}</span>
                                                </view>
                                                <view class="product-num-w" style="">
                                                    ￥<span class="product-num2" style="color: #999999">{{
                                                        toYuan(item.origin_price) }}</span>
                                                </view>
                                            </view>
                                        </view>
                                    </view>
                                    <u-radio shape="circle" size="36" activeColor="#FE5E56" :name="item.id"></u-radio>
                                </view>
                            </template>
                        </scroll-view>
                    </u-radio-group>
                </view>
            </view>
        </u-popup>
    </view>
</template>
<script>
export default {
    data() {
        return {
            defaultValue: '搜索商品标题', // searchBox的默认搜索值(由后端返回)
            boxValue: '', // searchBox搜索值

            show: false,
            list: [],
            page: 1,
            total: null,
            product_id: null,
        }
    },
    onLoad() {

    },
    methods: {
        // 获取更多商品
        scrolltolower() {
            if (this.list.length < this.total) {
                this.page = this.page + 1
                this.getProduct()
            }
        },
        // 点击选中商品
        groupChange(id) {
            this.product_id = id;
            let product = this.list.filter(item => item.id === id)
            this.close()
            this.$emit("affirm", product[0])
        },
        // 暂不选择
        noGroupChange() {
            this.close()
            this.$emit("affirm", {})
        },
        // 关闭
        groupClose() {
            this.close()
            this.$emit('close')
        },
        // 清空记录
        close() {
            this.list = [];
            this.product_id = null;
            this.total = 0;
            this.page = 1;
            this.show = false;
        },
        // 获取商品
        async getProduct() {
            let params = {
                page: this.page,
                pageSize: 10
            }
            let res = await this.get('/api/product/listLogin', params);
            if (res.code === 0) {
                this.list = [...this.list, ...res.data.list];
                this.total = res.data.total;
            }
        },
        // 搜索商品
        async searchBt(title) {
            this.page = 1
            let params = {
                page: this.page,
                pageSize: 10,
                title
            }
            let res = await this.get('/api/product/listLogin', params);
            if (res.code === 0) {
                this.list = res.data.list;
                this.total = res.data.total;
            }
        },
        info(product_id) {
            console.log(product_id);

            this.show = true;
            this.product_id = product_id;
            this.getProduct()
        }
    }
}
</script>
<style lang="scss" scoped>
.classify {
    padding: 40rpx 30rpx 30rpx 30rpx;

    .classify_content {
        margin-top: 20rpx;
        height: 800rpx;
        overflow-y: scroll;

        .scrollStyle {
            height: 800rpx;
        }

        .option {
            height: 160rpx;
            border-bottom: 1px solid #f0f0f1;
            font-size: 28rpx;
            font-weight: 500;

            .product-img {
                width: 100rpx;
                height: 100rpx;
                border-radius: 16rpx;
                margin-right: 12rpx;
            }

            .product-title {
                color: #00001C;
                width: 500rpx;
                font-size: 28rpx;
                font-weight: 500;
                word-break: break-all;
                text-overflow: ellipsis;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
            }

            .product-num {
                font-weight: 500;
                font-size: 24rpx;
                color: #F15353;
                margin-top: 24rpx;
            }

            .product-num-w {
                font-weight: 500;
                font-size: 24rpx;
                margin-top: 24rpx;
                color: #999999;
                margin-left: 13rpx;
                text-decoration: line-through;
            }

            .product-num2 {
                font-weight: 500;
                font-size: 28rpx;
                color: #F15353;
            }
        }
    }

}

.yuan {
    height: 48rpx;
    width: 48rpx;
    background: #F0F0F1;
    border-radius: 50%;
}
</style>