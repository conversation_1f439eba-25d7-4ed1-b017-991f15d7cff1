<template>
  <view class="f fac pointer">
    <view @click="onClickText">
      <slot></slot>
    </view>
    <view class="yz-icon-d-caret">
      <!-- class改成name -->
      <u-icon
        :color="upClass"
        name="arrow-up-fill"
        size="16"
        @click="onClickUp"
      ></u-icon>
      <u-icon
        :color="downClass"
        name="arrow-down-fill"
        size="16"
        @click="onClickDown"
      ></u-icon>
    </view>
  </view>
</template>

<script>
// import Emitter from 'element-ui/src/mixins/emitter'
export default {
  name: 'SortButton',
  // mixins: [Emitter],
  props: {
    value: {
      type: String,
      default: '',
    },
  },
  computed: {
    upClass() {
      let parentValue
      // #ifdef H5
      parentValue = this.$parent.$parent.value
      // #endif
      // #ifdef MP-WEIXIN
      parentValue = this.$parent.value
      // #endif
      if (parentValue.value !== this.value) {
        return '#909399'
      }
      return parentValue.sort === '1' ? '#fa3534' : '#909399' // 1:升序，2：降序
    },
    downClass() {
      let parentValue
      // #ifdef H5
      parentValue = this.$parent.$parent.value
      // #endif
      // #ifdef MP-WEIXIN
      parentValue = this.$parent.value
      // #endif
      if (parentValue.value !== this.value) {
        return '#909399'
      }
      return parentValue.sort === '2' ? '#fa3534' : '#909399' // 1:升序，2：降序
    },
  },
  methods: {
    onClickText() {
      let parentValue
      // #ifdef H5
      parentValue = this.$parent.$parent.value
      // #endif
      // #ifdef MP-WEIXIN
      parentValue = this.$parent.value
      // #endif
      if (parentValue.value !== this.value) {
        return this.onClickUp()
      }
      return parentValue.sort === '1' ? this.onClickDown() : this.onClickUp() // 1:升序，2：降序
    },
    onClickUp() {
      let parent
      // #ifdef H5
      parent = this.$parent.$parent
      // #endif
      // #ifdef MP-WEIXIN
      parent = this.$parent
      // #endif
      const payload = { value: this.value, sort: '1' }
      parent.$emit('input', payload)
      parent.$emit('change', payload)
    },
    onClickDown() {
      let parent
      // #ifdef H5
      parent = this.$parent.$parent
      // #endif
      // #ifdef MP-WEIXIN
      parent = this.$parent
      // #endif
      const payload = { value: this.value, sort: '2' }
      parent.$emit('input', payload)
      parent.$emit('change', payload)
    },
  },
}
</script>
<style scoped>
::v-deep .u-icon {
  color: #8d8d8d;
}
</style>
<style lang="scss" scoped>
.f {
  display: flex;
}
.fac {
  align-items: center;
}
.pointer {
  cursor: pointer;
  user-select: none;
}
.yz-icon-d-caret {
  display: flex;
  flex-direction: column;
  // margin: 0 3px;
  width: 10px;
  u-icon {
    color: #8d8d8d;
  }
  .el-icon-caret-top {
    height: 7px;
  }
  .red {
    color: #f11111;
  }
}
</style>
