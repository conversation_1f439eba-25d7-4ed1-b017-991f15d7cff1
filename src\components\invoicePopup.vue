<template>
	<view class="invoice-main">
		<!-- 电子发票的模态框 -->

		<u-popup
			:show="billShow"
			:round="10"
			@close="billClose"
			:overlay="false"
			:zIndex="zIndex"
			>
				<view class="invoice">
						<view class="invoice_title d-cc">
							<view class="title c-20 flex_max">发票</view>
							<view class="title-rightOff d-cc">
								<view @click="informationBtn">
									<text class="iconfont icon-fontclass-gantanhao"
												style="color: rgb(239, 82, 82); font-size: 14px; cursor: pointer; margin: 0 6rpx;"></text>
									<text class="font_size12 c-invoice">发票须知</text>
								</view>
								<view class="iconfont icon-close11 title_close" @click="billClose"></view>
							</view>
						</view>

						<view class="invoice-main">
							<!--发票类型-->
							<view class="invoice_type">
								<view class="font_size13 f-bold c-37 title">发票类型</view>
								<invoice-tab :tabList="radios" @invoiceTypeVal="invoiceType" :tabIndex="typeIndex"></invoice-tab>
								<view class="c-7c font_size12 mt-26">平台启用电子发票，与纸质发票具备同等法律效力。</view>
							</view>

							<!--发票抬头-->
							<view class="invoice_type  mt-50" v-show="typeVal === 1">
								<view class="font_size13 f-bold c-37 title">发票抬头</view>
								<invoice-tab :tabList="invoiceName" @invoiceNameVal="nameUnit"  :tabIndex="nameIndex"></invoice-tab>
								<view class="font_size12 d-cf mt-26" v-if="!billShow">
									<view class="c-20">
										<text class="iconfont icon-info-must type_icon"></text>
										<text>发票抬头</text>
									</view>
									<view class="c-7c invoice_ml">请填写需要开具发票的姓名</view>
								</view>
							</view>

							<!-- 电子发票个人 -->
							<view class="invoice_unit mt-40" v-show="nameVal === 1 && typeVal === 1">
								<u--form
												labelPosition="left"
												:model="order_bill"
												ref="billPerson"
												:rules="rulesPerson"
										>
										<u-form-item
												prop="person_name"
												:borderBottom="false"
												ref="person_name"
										>
											<view class="d-cf mb-30">
												<text class="font_size12 c-20 mr-18">个人名称</text>
												<u--input
													placeholder="请输入您的姓名（必填）"
													border="none"
													clearable
													fontSize="24rpx"

													placeholderStyle="font-size:24rpx;color: #858585;"
													style="font-size: 24rpx;"
													v-model="order_bill.person_name"
												 ></u--input>
											</view>
											</u-form-item>
											<u-form-item
													prop="mobile"
													:borderBottom="false"
													ref="mobile"
											>
												<view class="d-cf mb-30">
													<text class="font_size12 c-20 mr-18">收票人手机</text>
													<u--input
														placeholder="请输入您的手机号（必填）"
														border="none"
														clearable
														fontSize="24rpx"

														v-model="order_bill.mobile"
														placeholderStyle="font-size:12px;color: #858585;"
														style="font-size: 24rpx;"
													 ></u--input>
												</view>
											</u-form-item>
											<u-form-item
													prop="email"
													:borderBottom="false"
													ref="email"
											>
												<view class="d-cf">
													<text class="font_size12 c-20 mr-18">收票人邮箱</text>
													<u--input
														placeholder="请输入您的邮箱"
														border="none"
														clearable
														fontSize="24rpx"
														v-model="order_bill.email"
														placeholderStyle="font-size:12px;color: #858585;"
													 ></u--input>
												</view>
											</u-form-item>
									</u--form>


							</view>


							<!-- 电子发票单位 -->
							<view class="invoice_unit mt-40" v-show="nameVal === 2 && typeVal === 1">
								<u--form
									labelPosition="left"
									:model="order_bill"
									ref="billUnit"
									:rules="rulesUnit"
										>
										<u-form-item
												prop="company_name"
												:borderBottom="false"
												ref="company_name"
										>
											<view class="d-cf mb-30">
												<text class="iconfont icon-fontclass-gantanhao"
															style="color: rgb(239, 82, 82); font-size: 14px; cursor: pointer; margin: 0 6rpx;"></text>
												<text class="font_size12 c-20 mr-18">单位名称</text>
												<u--input
													placeholder="请输入单位名称（必填）"
													border="none"
													clearable
													fontSize="24rpx"
													placeholderStyle="font-size:12px;color: #858585;"
													v-model="order_bill.company_name"
												 ></u--input>
											</view>
										</u-form-item>
										<u-form-item
												prop="company_code"
												:borderBottom="false"
												ref="company_code"
										>
											<view class="d-cf mb-30">
												<text class="iconfont icon-fontclass-gantanhao"
															style="color: rgb(239, 82, 82); font-size: 14px; cursor: pointer; margin: 0 6rpx;"></text>
												<text class="font_size12 c-20 mr-18">纳税人识别号</text>
												<u--input
													placeholder="请输入纳税人识别号（必填）"
													border="none"
													clearable
													fontSize="24rpx"
													placeholderStyle="font-size:12px;color: #858585;"
													v-model="order_bill.company_code"
												 ></u--input>
											</view>
										</u-form-item>
										<u-form-item
												prop="mobile"
												:borderBottom="false"
												ref="mobile"
										>
											<view class="d-cf mb-30">
												<text class="iconfont icon-fontclass-gantanhao"
															style="color: rgb(239, 82, 82); font-size: 14px; cursor: pointer; margin: 0 6rpx;"></text>
												<text class="font_size12 c-20 mr-18">收票人手机</text>
												<u--input
													placeholder="请输入您的手机号（必填）"
													border="none"
													clearable
													fontSize="24rpx"
													placeholderStyle="font-size:12px;color: #858585;"
													v-model="order_bill.mobile"
												 ></u--input>
											</view>
										</u-form-item>
										<u-form-item
												prop="email"
												:borderBottom="false"
												ref="email"
										>
											<view class="d-cf mb-30 ml_34">
												<text class="font_size12 c-20 mr-18">收票人邮箱</text>
												<u--input
													placeholder="请输入您的邮箱（选填）"
													border="none"
													clearable
													fontSize="24rpx"
													v-model="order_bill.email"
													placeholderStyle="font-size:12px;color: #858585;"
												 ></u--input>
											</view>
										</u-form-item>
										<u-form-item
												prop="sign_address"
												:borderBottom="false"
												ref="sign_address"
										>
											<view class="d-cf mb-30 ml_34">
												<text class="font_size12 c-20 mr-18">单位地址</text>
												<u--input
													placeholder="请填写单位地址（选填）"
													border="none"
													clearable
													fontSize="24rpx"
													placeholderStyle="font-size:12px;color: #858585;"
													v-model="order_bill.sign_address"
												 ></u--input>
											</view>
										</u-form-item>
										<u-form-item
												prop="sign_mobile"
												:borderBottom="false"
												ref="sign_mobile"
										>
											<view class="d-cf mb-30 ml_34">
												<text class="font_size12 c-20 mr-18">单位电话</text>
												<u--input
													placeholder="请填写单位地址（选填）"
													border="none"
													clearable
													fontSize="24rpx"
													placeholderStyle="font-size:12px;color: #858585;"
													v-model="order_bill.sign_mobile"
												 ></u--input>
											</view>
										</u-form-item>
										<u-form-item
												prop="opening_bank"
												:borderBottom="false"
												ref="opening_bank"
										>
											<view class="d-cf mb-30 ml_34">
												<text class="font_size12 c-20 mr-18">开户银行</text>
												<u--input
													placeholder="请填写单位开户银行（选填）"
													border="none"
													clearable
													fontSize="24rpx"
													placeholderStyle="font-size:12px;color: #858585;"
													v-model="order_bill.opening_bank"
												 ></u--input>
											</view>
										</u-form-item>
										<u-form-item
												prop="bank_account"
												:borderBottom="false"
												ref="bank_account"
										>
											<view class="d-cf mb-30 ml_34">
												<text class="font_size12 c-20 mr-18">银行账号</text>
												<u--input
													placeholder="请填写单位银行账号（选填）"
													border="none"
													clearable
													fontSize="24rpx"
													placeholderStyle="font-size:12px;color: #858585;"
													v-model="order_bill.bank_account"
												 ></u--input>
											</view>
										</u-form-item>
									</u--form>


							</view>


							<!--增值税专用发票 -->
							<view class="invoice_unit mt-40" v-if="typeVal === 2 ">
								<u--form
									labelPosition="left"
									:model="order_bill"
									ref="billAddedTax"
									:rules="rulesAddedTax"
										>
										<u-form-item
												prop="company_name"
												:borderBottom="false"
												ref="company_name"
										>
											<view class="d-cf mb-30">
												<text class="iconfont icon-fontclass-gantanhao"
															style="color: rgb(239, 82, 82); font-size: 14px; cursor: pointer; margin: 0 6rpx;"></text>
												<text class="font_size12 c-20 mr-18">单位名称</text>
												<u--input
													placeholder="请输入单位名称（必填）"
													border="none"
													clearable
													fontSize="24rpx"
													placeholderStyle="font-size:12px;color: #858585;"
													v-model="order_bill.company_name"
												 ></u--input>
											</view>
										</u-form-item>
										<u-form-item
												prop="company_code"
												:borderBottom="false"
												ref="company_code"
										>
											<view class="d-cf mb-30">
												<text class="iconfont icon-fontclass-gantanhao"
															style="color: rgb(239, 82, 82); font-size: 14px; cursor: pointer; margin: 0 6rpx;"></text>
												<text class="font_size12 c-20 mr-18">纳税人识别号</text>
												<u--input
													placeholder="请输入纳税人识别号（必填）"
													border="none"
													clearable
													fontSize="24rpx"
													placeholderStyle="font-size:12px;color: #858585;"
													v-model="order_bill.company_code"
												 ></u--input>
											</view>
										</u-form-item>
										<u-form-item
												prop="sign_address"
												:borderBottom="false"
												ref="sign_address"
										>
											<view class="d-cf mb-30 ml_34">
												<text class="font_size12 c-20 mr-18">注册地址</text>
												<u--input
													placeholder="请填写注册地址（必填）"
													border="none"
													clearable
													fontSize="24rpx"
													placeholderStyle="font-size:12px;color: #858585;"
													v-model="order_bill.sign_address"
												 ></u--input>
											</view>
										</u-form-item>
										<u-form-item
												prop="sign_mobile"
												:borderBottom="false"
												ref="sign_mobile"
										>
											<view class="d-cf mb-30 ml_34">
												<text class="font_size12 c-20 mr-18">注册电话</text>
												<u--input
													placeholder="请填写注册电话（必填）"
													border="none"
													clearable
													fontSize="24rpx"
													placeholderStyle="font-size:12px;color: #858585;"
													v-model="order_bill.sign_mobile"
												 ></u--input>
											</view>
										</u-form-item>
										<u-form-item
												prop="opening_bank"
												:borderBottom="false"
												ref="opening_bank"
										>
											<view class="d-cf mb-30 ml_34">
												<text class="font_size12 c-20 mr-18">开户银行</text>
												<u--input
													placeholder="请填写单位开户银行（必填）"
													border="none"
													clearable
													fontSize="24rpx"
													placeholderStyle="font-size:12px;color: #858585;"
													v-model="order_bill.opening_bank"
												 ></u--input>
											</view>
										</u-form-item>
										<u-form-item
												prop="bank_account"
												:borderBottom="false"
												ref="bank_account"
										>
											<view class="d-cf mb-30 ml_34">
												<text class="font_size12 c-20 mr-18">银行账号</text>
												<u--input
													placeholder="请填写单位银行账号（必填）"
													border="none"
													clearable
													fontSize="24rpx"
													placeholderStyle="font-size:12px;color: #858585;"
													v-model="order_bill.bank_account"
												 ></u--input>
											</view>
										</u-form-item>
										<u-form-item
												prop="mobile"
												:borderBottom="false"
												ref="mobile"
										>
											<view class="d-cf mb-30">
												<text class="iconfont icon-fontclass-gantanhao"
															style="color: rgb(239, 82, 82); font-size: 14px; cursor: pointer; margin: 0 6rpx;"></text>
												<text class="font_size12 c-20 mr-18">收票人手机</text>
												<u--input
													placeholder="请输入您的手机号（必填）"
													border="none"
													clearable
													fontSize="24rpx"
													placeholderStyle="font-size:12px;color: #858585;"
													v-model="order_bill.mobile"
												 ></u--input>
											</view>
										</u-form-item>
										<u-form-item
												prop="email"
												:borderBottom="false"
												ref="email"
										>
											<view class="d-cf mb-30 ml_34">
												<text class="font_size12 c-20 mr-18">收票人邮箱</text>
												<u--input
													placeholder="请输入您的邮箱（选填）"
													border="none"
													clearable
													fontSize="24rpx"
													v-model="order_bill.email"
													placeholderStyle="font-size:12px;color: #858585;"
												 ></u--input>
											</view>
										</u-form-item>
										<u-form-item
												prop="address_id"
												:borderBottom="false"
												ref="address_id"
										>
											<view class="d-cf mb-30 ml_34">
												<text class="font_size12 c-20 mr-18">收货地址：</text>
												<view class="bill-add d-cf font_size12">
													<u-button
														class="mr_20"
														type="primary"
														size="mini"
														:plain="true"
														text="选择收货地址"
														@click="addressOpen('msg','invoice')">
													</u-button>
												</view>
											</view>
										</u-form-item>
										<view class="font_size13 f-bold c-37 title mb-30 ml_34">发票人信息</view>
										<view class="d-cf mb-30 ml_34">
											<view class="d-f-c flex_max ex_name">
												<view></view>
												<text>{{invoiceAddress.realname}} {{invoiceAddress.mobile}}</text>
												<text>{{invoiceAddress.province}} {{invoiceAddress.city}} {{invoiceAddress.county}} {{invoiceAddress.town}} {{invoiceAddress.detail}}</text>
											</view>
										</view>
									</u--form>
							</view>
							<!--收票信息 -->

							<!--发票内容 -->
							<view class="invoice_type  mt-50" v-show="typeVal === 1">
								<view class="font_size13 f-bold c-37 title">发票内容</view>
								<invoice-tab :tabList="detailType" @cateGoryVal='invoiceVal' :tabIndex="cateGoryIndex"></invoice-tab>
								<view class="c-7c font_size12 mt-26">发票金额为实际支付金额，不含折扣，优惠等扣减金额</view>
							</view>
							<view class="invoice_btn" @click="orderBillBtn">确定</view>
						</view>
				</view>
		</u-popup>
		<u-popup
			:show="informationShow"
			:round="20"
			mode="center"
			:overlay="false"
			:zIndex="zIndexInfo"
			>
			<view class="information-main d-f-c">
				<view class="title">发票说明</view>
				<view>{{setting.explain}}</view>

			</view>
			<view class="explain-btn" @click="explainOn">我知道了</view>
		</u-popup>
	</view>
</template>

<script>
	import eventBus from '@/utils/eventBus'
	import invoiceTab from '@/packageA/components/invoiceTab/invoiceTab.vue';
	export default {
		name:"invoicePopup",
		components:{
			invoiceTab
		},
		inject: ['bill_type'], //获取传给孙组件的值
		props:{
			billShow: {
				type:Boolean,
				default:() => false
			},
			overlay: {
				type:Boolean,
				default:() => false
			},
			order_bill: {
				type:Object,
				default:() => {}
			},
			setting: {
				type:Object,
				default:() => {}
			}
		},
		data() {
			return {
				zIndex:10075,
				zIndexInfo:10080,
				informationShow:false,
				invoiceAddress:{
					realname:'',
					mobile:'',
					province:'',
					city:'',
					county:'',
					town:'',
					detail:'',
				},
				invoiceAddressId:0, //地址ID
				radios: [{
						name:'电子发票',
						type: 1,
						checked:false
					},
					{
						name:'增值税发票',
						type: 2,
						checked:false
					},
				],
				invoiceName: [
					{
						name:'个人',
						type:1,
						checked:true
					},
					{
						name:'单位',
						type:2,
						checked:true
					}
				],
				detailType: [
					{
						name:'商品明细',
						type:1,
						checked:true
					},
					{
						name:'商品类别',
						type:2,
						checked:true
					},
					{
						name:'不开发票',
						type:3,
						checked:true
					}
				],
				cateGoryVal:1, //发票内容
				nameVal:1, // 个人还是单位
				typeVal:1, // //1电子普通发票 2增值税发票
				typeIndex:0,
				cateGoryIndex:0,
				nameIndex:0,
				rulesPerson: {
					'person_name':[
						{
							type: 'string',
							required: true,
							message: '请输入收件人姓名',
							trigger: ['blur']
						},
						{
							min: 2,
							max: 16,
							message: '长度在6-16个字符之间',
							trigger: ['blur']
						},
					],
					'mobile':[
						{
							type: 'string',
							required: true,
							message: '请输入您的手机号',
							trigger: ['blur']
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// uni.$u.test.mobile()就是返回true或者false的
								return uni.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['blur'],
						}
					]

				},
				rulesAddedTax: {
					'company_name':[
						{
							type: 'string',
							required: true,
							message: '请输入单位名称',
							trigger: ['blur']
						},
						{
							min: 2,
							max: 16,
							message: '长度在6-16个字符之间',
							trigger: ['blur']
						}
					],
					'company_code': [
						{
							type: 'string',
							required: true,
							message: '请输入纳税人识别号',
							trigger: ['blur']
						},
						{
							min: 2,
							max: 16,
							message: '长度在6-16个字符之间',
							trigger: ['blur']
						}
					],
					'sign_address':[
						{
							type: 'string',
							required: true,
							message: '请输入注册地址',
							trigger: ['blur']
						},
						{
							min: 2,
							max: 50,
							message: '长度在2-50个字符之间',
							trigger: ['blur']
						}
					],
					'sign_mobile': [
						{
							type: 'string',
							required: true,
							message: '请输入注册电话',
							trigger: ['blur']
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								let RegEx = /^((0\d{2,3}-\d{7,8})|(1[3584]\d{9}))$/;
								if (RegEx.test(value)) {
									return value;
								} else {
									return false
								}

							},
							message: '您输入的电话号码错误',
							// 触发器可以同时用blur和change
							trigger: ['blur'],
						}
					],
					'opening_bank': [
						{
							type: 'string',
							required: true,
							message: '请输入您的开户银行',
							trigger: ['blur']
						},
						{
							min: 2,
							max: 16,
							message: '长度在6-16个字符之间',
							trigger: ['blur']
						}
					],
					'bank_account':[
						{
							type: 'string',
							required: true,
							message: '请输入您的银行账号',
							trigger: ['blur']
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								let RegEx = /^\d{5,30}$/;
								if (RegEx.test(value)) {
									return value;
								} else {
									return false
								}

							},
							message: '您输入的银行卡号格式错误',
							// 触发器可以同时用blur和change
							trigger: ['blur'],
						}
					],
					'mobile':[
						{
							type: 'string',
							required: true,
							message: '请输入您的手机号',
							trigger: ['blur']
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// uni.$u.test.mobile()就是返回true或者false的
								return uni.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['blur'],
						}
					],
				},
				rulesUnit: {
					'company_name':[
						{
							type: 'string',
							required: true,
							message: '请输入单位名称',
							trigger: ['blur']
						},
						{
							min: 2,
							max: 16,
							message: '长度在6-16个字符之间',
							trigger: ['blur']
						}
					],
					'company_code':[
						{
							type: 'string',
							required: true,
							message: '请输入纳税人识别号',
							trigger: ['blur']
						},
						{
							min: 2,
							max: 16,
							message: '长度在6-16个字符之间',
							trigger: ['blur']
						}
					],
					'mobile': [
						{
							type: 'string',
							required: true,
							message: '请输入您的手机号',
							trigger: ['blur']
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// uni.$u.test.mobile()就是返回true或者false的
								return uni.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['blur'],
						}
					]
				},
			};
		},
		created() {
			eventBus.$on('invoiceAddress', (val) => { //使用bus兄弟传值
					this.invoiceAddress = val;
					this.invoiceAddressId = val.id;
				// this.$refs.rulesAddedTax.validateField('address_id') //重新校验一次
			})
		},
		onShow() {
		},
		computed: {
			 billTypeChild() { //使用计算属性监听响应式数据
				 return this.bill_type()
			 }
		},
		mounted() {
			this.accountRadio();
		},
		options: { styleIsolation: 'shared' }, //兼容小程序修改uview样式，有的修改不生效
		methods:{
			//子组件传过来的数据
			invoiceVal(data,index) { //发票内容
				if(data === 3) {
					this.$emit('billClose', this.billShow,this.overlay);
				} else {
					this.cateGoryVal = data;
					this.cateGoryIndex = index;
				}
			},
			addressOpen(type,name) {
				eventBus.$emit('destroyShow', true);

				this.$nextTick(function(){
						eventBus.$emit('upaddressList', true); //打开地址列表弹窗
						eventBus.$emit('overlay', 10075);
						eventBus.$emit('chooseName', name); //发票功能里面的选择地址
				},0)
			},
			nameUnit(data,index) {
				this.nameVal = data;
				this.nameIndex = index;
			},
			invoiceType(data,index) {
				this.typeVal = data;
				this.typeIndex = index;
			},
			explainOn() {
				this.informationShow = false;
				eventBus.$emit('overlay', 10070);
			},
			accountRadio() { //检测发票选项是否开启
				for(let j in this.radios) {  //隐藏显示发票选项
					for(let i in this.billTypeChild) {
						if(this.billTypeChild[i] === this.radios[j].type) {
							this.radios[j].checked = true;
						}
					}
				}
			},
			informationBtn() { //发票须知
				if(this.setting.explain) {
					this.informationShow = true;
					eventBus.$emit('overlay', 10075);
				}
			},
			billClose() {
				this.$emit('billClose', this.billShow);
			},
			orderBillBtn() {
				this.order_bill.account_type = this.nameVal; //1个人 2公司
				this.order_bill.type = this.typeVal; ///1普通订单 2技术服务费
				this.order_bill.detail_type = this.cateGoryVal; //1商品明细2商品类别
				if (this.typeVal === 1 && this.nameVal === 1) {
					this.$refs.billPerson.validate().then(res => {
						this.$emit('BillConfirm', this.order_bill,this.billShow);
						uni.$u.toast('发票填写成功');
					}).catch(errors => {
						uni.$u.toast('还未填写内容')
					})
				} else if (this.typeVal === 1 && this.nameVal === 2) {
					this.$refs.billUnit.validate().then(res => {
						this.$emit('BillConfirm', this.order_bill,this.billShow);
						uni.$u.toast('发票填写成功');
					}).catch(errors => {
						uni.$u.toast('还未填写内容')
					})
				} else if (this.typeVal === 2) {
					if (this.invoiceAddressId) {
						this.order_bill.address_id = this.invoiceAddressId;
						this.$refs.billAddedTax.validate().then(res => {
							this.$emit('BillConfirm', this.order_bill,this.billShow);
							uni.$u.toast('发票填写成功');
						}).catch(errors => {
							uni.$u.toast('还未填写内容')
						})
					} else {
						uni.$u.toast('请填写收货地址');
					}

				} else {

				}



			}
		}
	}
</script>
<style scoped>
	.invoice ::v-deep  .u-form-item__body {
		padding: 0;
	}

	 .invoice_name ::v-deep .u-cell__body {
		padding: 0;
	}
	.from-content ::v-deep .u-form-item__body__right .u-form-item__body__right__content__slot {
		justify-content: flex-end;
	}
	.from-content .area ::v-deep .u-form-item__body__right .u-form-item__body__right__content__slot {
		justify-content: flex-start;
	}


</style>
<style>

</style>
<style lang="scss" scoped>
	/*#ifdef MP-WEIXIN*/
		.invoice ::v-deep .u-form-item .u-form-item__body {
			padding: 0rpx!important;
		}
	/*#endif*/
	/*#ifdef H5*/
	.invoice	::v-deep .u-form-item__body__right__content__slot  {
			flex: 5;
			display: block;

		}
	/*#endif*/
	/*弹窗样式*/
	.invoice-main {

	}
	.invoice {
		padding: 0 40rpx 0 30rpx;
		height: 1100rpx;
		max-height: 1100rpx;
		.invoice_title {
			padding: 38rpx 0 40rpx 0;
			position: relative;
			.title {
					font-family: SourceHanSansCN-Regular;
					font-size: 36rpx;
					font-weight: normal;
					font-stretch: normal;
					letter-spacing: 0px;
					color: #202020;
					text-align: center;
			}
			.title-rightOff {
				position: absolute;
				right:0rpx;
			}
			.title_close {
				margin-left: 40rpx;
			}
		}
		.invoice-main {
			height: 974rpx;
			max-height: 974rpx;
			overflow-y: scroll;
			.invoice_type {
				.title {
					margin-bottom: 32rpx;
				}
				.invoice_ml {
					padding-left: 16rpx;
				}
			}
			.invoice_btn{
				width: 600rpx;
				height: 66rpx;
				line-height: 66rpx;
				position: relative;
				text-align: center;
				margin: 84rpx auto 22rpx auto;
				color: #fff;
				background-color: #f15353;
				border-radius: 33rpx;
			}
			.invoice_unit {
				padding-bottom: 35rpx;
				border-bottom: 1px solid #f2f2f2;
			}
		}

	}
	.information-main {
		width: 510rpx;
		height: 632rpx;
		padding: 0 56rpx;
		.title {
			font-size: 36rpx;
			margin: 0 auto;
			padding: 34rpx 0 52rpx 0;
			text-align: center;
		}
	}
	.explain-btn {
		width: 416rpx;
		height: 78rpx;
		line-height: 78rpx;
		text-align: center;
		background-color: rgba(248, 60, 34, 100);
		border-radius: 100rpx;
		font-size: 28rpx;
		color: #fff;

		margin: 0 auto 40rpx auto;
	}
</style>
