<template>
  <view>
    <!-- 头部搜索 -->
    <view class="head">
      <view class="head_select">
        <uni-data-select
          v-model="value"
          :localdata="liveOptions"
          :clear="false"
          @change="changeSelect"
        ></uni-data-select>
      </view>
      <view class="head_search">
        <u-search
          height="70"
          searchIconSize="40"
          color="#a4a4a4"
          placeholder="搜索直播名称"
          @search="changeSearch"
          v-model="keywords"
          :showAction="false"
        ></u-search>
      </view>
      <view class="head_setting" @click="show = true">
        <view class="ml_20 mt_5">
          <uni-icons type="gear" size="20" />
        </view>
        <view class="ml_15">设置</view>
      </view>
    </view>
    <view class="tabs">
      <u-tabs
        lineWidth="80rpx"
        lineColor="#ef5452"
        lineHeight="8rpx"
        itemStyle="padding-right: 20rpx; height: 80rpx"
        :list="shareLiveCategorys"
        :activeStyle="{ color: '#101010' }"
        :inactiveStyle="{ color: '#101010' }"
        @change="onChangeshareLiveCategorys"
      ></u-tabs>
    </view>
    <view class="switch">
      <u-row>
        <u-col :span="7"></u-col>
        <u-col :span="2">
          <u-button
            type="error"
            size="small"
            shape="circle"
            text="一键开启"
            @click="allOpen"
          ></u-button>
        </u-col>
        <u-col :span="2">
          <u-button
            type="error"
            size="small"
            shape="circle"
            text="一键关闭"
            class="switch_button"
            @click="allClose"
          ></u-button>
        </u-col>
      </u-row>
    </view>
    <view class="shops" v-for="item in shareLiveList" :key="item.id">
      <u-row>
        <u-col :span="3">
          <view>
            <img class="shops_avatar" :src="item.image_url" alt="加载失败" />
          </view>
        </u-col>
        <u-col :span="7">
          <view class="shops_message" @click="getShareLiveRoomById(item.id)">
            <view class="shops_title">{{ item.title }}</view>
            <view>主播：{{ item.user.nickname }}</view>
            <view class="shops_icons">
              <view>
                <u-icon name="bag" size="40" color="rgba(0,0,0,0.4)" />
              </view>
              <view class="mt_5">
                {{ item.share_live_room_products.length }}
              </view>
              <view class="mt_5">
                <u-icon name="arrow-right" size="20" color="rgba(0,0,0,0.4)" />
              </view>
            </view>
          </view>
        </u-col>
        <u-col :span="2" class="shops_switch">
          <view>
            <u-switch
              v-model="item.share_live_room_small_shop.status"
              size="30"
              activeColor="#f56c6c"
              @change="
                onChangeSwitch(item.share_live_room_small_shop.status, item.id)
              "
            ></u-switch>
          </view>
        </u-col>
      </u-row>
    </view>
    <!-- 直播设置弹出层 -->
    <u-popup :show="show" mode="bottom" :round="30" @close="closePopup">
      <view class="showPop">
        <view class="showSetting">
          <text @click="closePopup">取消</text>
          <text>设置</text>
          <text @click="btnOK">确定</text>
        </view>
        <view class="showMessage">
          一键同步直播：开启后，后面有新增的直播间则会自动开启同步到小商店显示，直播间的商品也会自动导入小商店
        </view>
        <view class="showSwitch">
          <text>一键同步直播</text>
          <u-switch
            v-model="is_liveStream"
            size="30"
            activeColor="#f56c6c"
            @change="changeLiveStream"
          ></u-switch>
        </view>
      </view>
    </u-popup>
    <!-- 直播间商品弹层 -->
    <u-popup :show="showShops" mode="bottom" :round="30">
      <view class="shopsPop">
        <view class="shopsPop_setting">
          <text></text>
          <text>直播间商品</text>
          <u-icon name="close" @click="closeShopPopup"></u-icon>
        </view>
        <scroll-view
          scroll-y
          class="shopsPop_scroll"
          @scrolltolower="scrollReachBottom"
        >
          <view
            class="shopsPop_shop"
            v-for="item in shareLiveShopsList"
            :key="item.id"
          >
            <img :src="item.image_url" alt="" />
            <view class="shopsPop_message">
              <text>{{ item.title }}</text>
              <view class="shopsPop_price">
                售价
                <u-icon name="rmb" size="20rpx"></u-icon>
                {{  toYuan(item.sale_price) }}
              </view>
              <view class="shopsPop_profit">
                <text class="profit_text">赚</text>
                <u-icon name="rmb" size="30rpx" color="#e1564c"></u-icon>
                <text>{{ toYuan(item.profit) }}</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </u-popup>
    <!-- 提示信息 -->
    <u-toast ref="uToast" />
    <!-- 没有数据 -->
    <view class="noData" v-if="loadMore">加载中...</view>
    <view class="noData" v-if="noMore">——暂无更多数据——</view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      page: 2, // 当前页码
      pageSize: 10, // 每页数据数量
      total: null, // 总数
      loadMore: false, //加载更多
      noMore: false, // 没有更多数据
      shopTotal: null, // 商品总数
      shopPage: 2, // 商品页码
      // 选择框
      liveOptions: [
        { value: 0, text: '直播间名称' },
        { value: 1, text: '主播昵称/手机号' },
        { value: 2, text: '商品' },
      ],
      value: 0,
      // 搜索关键字
      keywords: '',
      // 分类tabs
      shareLiveCategorys: [{ id: 'all', name: '全部' }],
      // 直播设置显隐
      show: false,
      // 一键同步直播
      is_liveStream: false,
      // 临时存储状态
      tempLiveStream: false,
      // 直播间列表
      shareLiveList: [],
      // 直播间商品列表
      shareLiveShopsList: [],
      // 直播间弹层显隐
      showShops: false,
      // 分类存储的状态
      categoryStatus: '',
      // 直播间分类ID
      CategoryRoomId: [],
      // 直播间ID
      shareLiveRoomsId: '',
      // 重置页数按钮
      resetPage: false,
    }
  },
  onShow() {
    // 调用获取直播分类
    this.getShareLiveCategorys()
    // 调用获取同步状态
    this.getSysShareLiveSettingIsSyn()
    // 调用获取直播间列表
    this.getShareLiveRoomList()
  },
  // 触底加载事件
  async onReachBottom() {
    this.loadMore = true
    this.noMore = false
    // let res = {}
    if (
      this.shareLiveList.length < this.total &&
      typeof this.categoryStatus !== 'number'
    ) {
      const params = {
        page: this.page++,
        pageSize: 10,
      }
      const res = await this.get(
        '/api/shareLive/getShareLiveRoomList',
        params,
        true,
      )
      // 单独控制直播间状态
      res.data.list.forEach(item => {
        item.share_live_room_small_shop.status =
          item.share_live_room_small_shop.status === 0 ? false : true
      })
      if (res.data.list.length < 10) {
        this.shareLiveList.push(...res.data.list)
        this.noMore = true
      } else {
        this.shareLiveList.push(...res.data.list)
        this.noMore = true
      }
    } else if (
      this.shareLiveList.length < this.total &&
      typeof this.categoryStatus === 'number'
    ) {
      const params = {
        share_live_category_id: this.categoryStatus,
        page: this.page++,
        pageSize: 10,
      }
      const res = await this.get(
        '/api/shareLive/getShareLiveRoomList',
        params,
        true,
      )
      // 单独控制直播间状态
      res.data.list.forEach(item => {
        item.share_live_room_small_shop.status =
          item.share_live_room_small_shop.status === 0 ? false : true
      })
      if (res.data.list.length < 10) {
        this.shareLiveList.push(...res.data.list)
        this.noMore = true
      } else {
        this.shareLiveList.push(...res.data.list)
        this.noMore = true
      }
    }
    this.loadMore = false
    this.noMore = true
  },
  // 监听事件
  watch: {
    // 监听弹出层事件
    show(newShow) {
      if (newShow) {
        uni.setNavigationBarTitle({
          title: '直播设置',
        })
      } else {
        uni.setNavigationBarTitle({
          title: '直播管理',
        })
      }
    },
    // 监听商品弹层事件
    showShops(newValue) {
      if (newValue) {
        this.shopPage = 2
      }
    },
    // 监听重置页面按钮事件
    resetPage(newValue) {
      if (newValue) {
        this.page = 2
      }
    },
  },
  // 手动清除事件监听
  beforeDestroy() {
    // 移除对 resetPage 事件的监听
    this.$off('resetPage')
  },
  methods: {
    // 选择框方法
    changeSelect(e) {
      console.log('选择选项：', e)
    },
    // 搜索框方法
    changeSearch() {
      this.page = 2
      let params = {}
      if (this.value === 0) {
        params = {
          title: this.keywords,
        }
      } else if (this.value === 1) {
        params = {
          keyword: this.keywords,
        }
      } else {
        params = {
          product_name: this.keywords,
        }
      }
      this.get('/api/shareLive/getShareLiveRoomList', params, true).then(
        res => {
          // 单独控制直播间状态
          res.data.list.forEach(item => {
            item.share_live_room_small_shop.status =
              item.share_live_room_small_shop.status === 0 ? false : true
          })
          if (res.data.list.length < 10) {
            this.noMore = true
            this.shareLiveList = res.data.list
          } else {
            this.shareLiveList = res.data.list
            this.noMore = false
          }
          this.total = res.data.total
        },
      )
    },
    // 分类改变的方法
    onChangeshareLiveCategorys(event) {
      this.page = 2
      this.categoryStatus = event.id
      this.getShareLiveRoomList()
    },
    // 获取直播分类
    getShareLiveCategorys() {
      this.get('/api/shareLive/getShareLiveCategorys', {}, true).then(res => {
        let newShareLiveCategorys = res.data.map(item => {
          return {
            name: item.title,
            id: item.id,
          }
        })
        this.shareLiveCategorys = this.shareLiveCategorys.concat(
          newShareLiveCategorys,
        )
      })
    },
    // 确认直播设置
    btnOK() {
      const params = {
        is_syn: this.is_liveStream ? 1 : 0,
      }
      // 保存同步状态
      this.post(
        '/api/shareLive/saveSysShareLiveSettingIsSyn',
        params,
        true,
      ).then(res => {
        if (res.code === 0) {
          this.tempLiveStream = this.is_liveStream
          this.show = false
          this.$refs.uToast.show({ message: '操作成功！', type: 'default' })
        }
      })
    },
    // 获取同步状态
    getSysShareLiveSettingIsSyn() {
      this.get('/api/shareLive/getSysShareLiveSettingIsSyn', {}, true).then(
        res => {
          if (res.data.is_syn !== 0) {
            this.is_liveStream = true
            this.tempLiveStream = true
          }
        },
      )
    },
    // 修改一键同步直播状态
    changeLiveStream(event) {
      this.is_liveStream = event
    },
    // 关闭弹层和取消
    closePopup() {
      this.is_liveStream = this.tempLiveStream
      this.show = false
    },
    // 获取直播间列表
    getShareLiveRoomList() {
      if (typeof this.categoryStatus !== 'number') {
        const params = {
          page: 1,
          pageSize: 10,
        }
        this.get('/api/shareLive/getShareLiveRoomList', params, true).then(
          res => {
            // 单独控制直播间状态
            res.data.list.forEach(item => {
              item.share_live_room_small_shop.status =
                item.share_live_room_small_shop.status === 0 ? false : true
            })
            // 全部分类的直播间
            // this.shareLiveList = res.data.list
            if (res.data.list.length < 10) {
              this.noMore = true
              this.shareLiveList = res.data.list
            } else {
              this.shareLiveList = res.data.list
              this.noMore = false
            }
            this.total = res.data.total
          },
        )
      } else {
        const params = {
          share_live_category_id: this.categoryStatus,
          page: 1,
          pageSize: 10,
        }
        this.get('/api/shareLive/getShareLiveRoomList', params, true).then(
          res => {
            // 单独控制直播间状态
            res.data.list.forEach(item => {
              item.share_live_room_small_shop.status =
                item.share_live_room_small_shop.status === 0 ? false : true
            })
            // 筛选分类的直播间
            // this.shareLiveList = res.data.list
            if (res.data.list.length < 10) {
              this.noMore = true
              this.shareLiveList = res.data.list
            } else {
              this.shareLiveList = res.data.list
              this.noMore = false
            }
            this.total = res.data.total
          },
        )
      }
    },
    // 获取直播详情
    getShareLiveRoomById(id) {
      this.shareLiveRoomsId = id
      const params = { share_live_id: id, page: 1, pageSize: 10 }
      this.showShops = true
      this.get(
        '/api/shareLive/getShareLiveRoomProductListByShareLiveId',
        params,
        true,
      ).then(res => {
        this.shareLiveShopsList = res.data.list
        this.shopTotal = res.data.total
      })
    },
    // 关闭直播间商品弹层
    closeShopPopup() {
      this.showShops = false
      this.shareLiveShopsList = []
    },
    // 单独控制直播间按钮
    onChangeSwitch(status, id) {
      const targetRoom = this.shareLiveList.find(item => item.id === id)
      if (targetRoom) {
        const params = {
          share_live_room_id: id,
          status: status ? 1 : 0,
        }
        this.post(
          '/api/shareLive/saveShareLiveRoomSmallShop',
          params,
          true,
        ).then(res => {
          if (res.code === 0) {
            this.$refs.uToast.show({ message: '操作成功！', type: 'default' })
          }
        })
      }
    },
    // 一键开启
    allOpen() {
      this.resetPage = 2
      this.keywords = ''
      this.page = 2
      if (typeof this.categoryStatus !== 'number') {
        const params = {
          status: 1,
        }
        this.post(
          '/api/shareLive/saveShareLiveRoomSmallShopAll',
          params,
          true,
        ).then(res => {
          if (res.code === 0) {
            this.getShareLiveRoomList()
            this.$refs.uToast.show({ message: '操作成功！', type: 'default' })
          }
        })
      } else {
        const params = {
          status: 1,
          share_live_category_id: this.categoryStatus,
        }
        this.post(
          '/api/shareLive/saveShareLiveRoomSmallShopAll',
          params,
          true,
        ).then(res => {
          if (res.code === 0) {
            this.getShareLiveRoomList()
            this.$refs.uToast.show({ message: '操作成功！', type: 'default' })
          }
        })
      }
    },
    // 一键关闭
    allClose() {
      this.resetPage = 1
      this.keywords = ''
      this.page = 2
      if (typeof this.categoryStatus !== 'number') {
        const params = {
          status: 0,
        }
        this.post(
          '/api/shareLive/saveShareLiveRoomSmallShopAll',
          params,
          true,
        ).then(res => {
          if (res.code === 0) {
            this.getShareLiveRoomList()
            this.$refs.uToast.show({ message: '操作成功！', type: 'default' })
          }
        })
      } else {
        const params = {
          status: 0,
          share_live_category_id: this.categoryStatus,
        }
        this.post(
          '/api/shareLive/saveShareLiveRoomSmallShopAll',
          params,
          true,
        ).then(res => {
          if (res.code === 0) {
            this.getShareLiveRoomList()
            this.$refs.uToast.show({ message: '操作成功！', type: 'default' })
          }
        })
      }
    },
    // 直播间商品触底事件
    scrollReachBottom() {
      const params = {
        share_live_id: this.shareLiveRoomsId,
        page: this.shopPage++,
        pageSize: 10,
      }
      if (this.shareLiveShopsList.length < this.shopTotal) {
        this.get(
          '/api/shareLive/getShareLiveRoomProductListByShareLiveId',
          params,
          true,
        ).then(res => {
          this.shareLiveShopsList.push(...res.data.list)
        })
      }
    },
  },
}
</script>
<style scoped>
::v-deep .uni-stat__select {
  padding: 0;
}
::v-deep .uni-select {
  border: none;
}
::v-deep .uni-select__input-box {
  padding: 0;
  width: 182rpx;
  height: 64rpx;
}
::v-deep .uni-select__selector {
  width: 260rpx;
}
::v-deep .u-search {
  width: 450rpx;
  border-radius: 100rpx;
}
::v-deep .u-col {
  margin-right: 26rpx;
  align-self: flex-end;
}
</style>
<style lang="scss" scoped>
.head {
  display: flex;
  margin-top: 30rpx;
  font-size: 24rpx;
  .head_select {
    margin-left: 6rpx;
    margin-right: 18rpx;
  }
}
.switch {
  padding-top: 20rpx;
  padding-right: 20rpx;
  margin-bottom: 14rpx;
}
.shops {
  width: 740rpx;
  height: 210rpx;
  background-color: #ffffff;
  margin-left: 5rpx;
  margin-top: 22rpx;
  font-size: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  .shops_avatar {
    width: 160rpx;
    height: 160rpx;
  }
  .shops_message {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-size: 24rpx;
    height: 160rpx;
    width: 360rpx;
    font-weight: 600;
    .shops_title {
      font-size: 28rpx;
    }
    .shops_icons {
      display: flex;
      align-items: center;
      color: rgba(0, 0, 0, 0.4);
    }
  }
  .shops_switch {
    display: flex;
    align-self: flex-end;
  }
}
.showPop {
  height: 400rpx;
  padding-left: 30rpx;
  padding-top: 30rpx;
  padding-right: 30rpx;
  color: #101010;
  font-weight: 500;
  .showSetting {
    display: flex;
    justify-content: space-between;
    font-size: 28rpx;
  }
  .showMessage {
    padding-top: 100rpx;
    padding-bottom: 60rpx;
    font-size: 24rpx;
  }
  .showSwitch {
    display: flex;
    justify-content: space-between;
    padding-bottom: 100rpx;
  }
}
.shopsPop {
  height: 600rpx;
  padding-left: 30rpx;
  padding-top: 30rpx;
  padding-right: 30rpx;
  color: #101010;
  font-weight: 600;
  .shopsPop_setting {
    display: flex;
    justify-content: space-between;
    font-size: 28rpx;
    padding-bottom: 30rpx;
  }
  .shopsPop_scroll {
    height: 500rpx;
    .shopsPop_shop {
      display: flex;
      justify-content: start;
      padding-top: 30rpx;
      > img {
        height: 160rpx;
        width: 160rpx;
      }
      .shopsPop_message {
        padding-left: 30rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .shopsPop_price {
          display: flex;
          align-items: baseline;
          font-size: 20rpx;
          color: rgba(16, 16, 16, 0.5);
        }
        .shopsPop_profit {
          display: flex;
          align-items: baseline;
          color: #e1564c;
          font-size: 32rpx;
          .profit_text {
            font-size: 22rpx;
          }
        }
      }
    }
  }
}
.noData {
  text-align: center;
  height: 100rpx;
  line-height: 100rpx;
}
</style>
