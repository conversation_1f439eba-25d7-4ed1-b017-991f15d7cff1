<!-- 收入明细 -->
<template>
	<view>
		<view class="wallet-list" v-if="!$isEmpty.isEmpty(incomeList)">
				<block v-for="(item,index) in incomeList" :key="index" >
					<view class="item d-bf" >
						<view class="d-f-c font_size12 c-29">
							<view class="mb_30 " >{{item.income_name}}</view>
							<view class="mb_30 " >订单号</view>
							<view class="mb_30 " >分销商昵称</view>
							<view>收入余额：{{item.balance || 0.00 }}</view>
						</view>
						<view class="d-f-c text-r">
							<view class="mb_30 c-orange" >+{{item.amount || 0.00}}</view>
							<view class="mb_30">{{item.order.order_sn}}</view>
							<view class="mb_30">{{item.order.user.nickname}}</view>
							<view class="font_size11 c-8a mt-10">{{item.created_at}}</view>
						</view>
					</view>
				</block>
		</view>
		<view v-else class="bg-white white-pb">
			<u-empty mode="data">
			</u-empty>
		</view>
	</view>
</template>

<script>
	export default {
		name:"incomeList",
		props:{
			incomeList:{
				type:Array,
				default:[]
			}
		},
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss">
	.wallet-list {
		background-color: #fff;
		padding: 0 26rpx;
			.item {
				padding: 28rpx  0 26rpx;
				border-bottom: 1px solid #f2f2f2;
				&:last-of-type {
					border-bottom: none;
				}
			}
	}
</style>
