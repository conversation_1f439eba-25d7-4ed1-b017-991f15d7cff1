<!-- 评价的商品信息 -->
<template>
	<view>
			<view class="goods d-f">
				<view class="img">
					<image :src="evaluateList.image_url"></image>
				</view>
				<view class="warp d-f">
					<view class="inner">
						<view class="name">{{evaluateList.title}}</view>
						<view class="option">规格: {{evaluateList.sku_title}}</view>
					</view>
					<view class="price">
						<view class="product_price">￥{{evaluateList.amount || 0.00}} </view>
						<view class="product_price">×{{evaluateList.qty}}</view>
					</view>
				</view>
			</view>
	</view>
</template>

<script>
	export default {
		name:"evaluateGoods",
		props:{
			evaluateList:{
				type:Object,
				default:{}
			}
		},
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss" scoped>
	.goods {
		background: #fafafa;
		.img {
			width: 136rpx;
			height: 136rpx;
			padding: 28rpx;
			display: inline-block;
			image {
				width: 136rpx;
				height: 136rpx;
			}
		}
		.warp {
			width: 70vw;
			padding: 20rpx 32rpx 0 0;
			.inner {
				width: 70%;
				-webkit-box-sizing: border-box;
				box-sizing: border-box;
				padding: 0 20rpx;
				text-align: left;
				.name {
					text-align: left;
					color: #333;
					margin-bottom: 20rpx;
					font-size: 28rpx;
				}
				.option {
					color: #8c8c8c;
					font-size: 24rpx;
					flex:1;
				}
			}
			.price {
				width: 30%;
				text-align: right;
				box-sizing: border-box;
				color: #333;
				.product_price {
					margin-bottom: 20rpx;
					font-size: 14px;
				}
				.option {
					color: #8c8c8c;
					font-size: 24rpx;
					flex:1;
				}
			}
		}
	}
</style>
