<template>
    <view>
        <view class="top">
            <view style="height: 130rpx;"></view>
            <view class="img-user">
                <u-avatar :src='user.avatar' size="128rpx" shape="circle"></u-avatar>
            </view>
            <view class="top-user">
                <span class="user-name">{{ user.nickname ? user.nickname : '用户' + user.username.slice(-4) }}</span>
            </view>
        </view>
        <view class="f fac fjsb fw video-card">
            <template v-for="item in videoList">
                <view class="video-box" @click="goVideo(item)" :key="item.id">
                    <view style="position: relative;">
                        <image :src="item.cover_url" mode="widthFix" class="video-img" />
                        <view class="f fac fjc imgbut">
                            <span class="iconfont icon-you icon-but"></span>
                        </view>
                    </view>
                    <view class="title">
                        {{ item.title }}
                    </view>
                    <view class="video-ures f fac">
                        <u-avatar :src='item.user.avatar' size="56rpx" shape="circle"></u-avatar>
                        <view style="margin-left: 8rpx;">
                            <view class="video-user-name">{{ item.user.nickname ? item.user.nickname : '用户' + item.user.username.slice(-4)}}</view>
                            <view class="video-user-time">{{ formatDateTime(item.created_at) }}</view>
                        </view>
                    </view>
                </view>
            </template>
        </view>
    </view>
</template>
<script>
export default {
    data() {
        return {
            id: null,
            user: {},
            videoList: [],
            total: 0,
            page: 1,
            pageSize: 10
        }
    },
    onLoad(e) {
        if (e.id) {
            this.id = parseInt(e.id)
            this.getVideoList()
        }
    },
    // 加载更多数据
    async onReachBottom() {
        if (this.videoList.length < this.total) {
            this.page = ++this.page
            let params = {
                page: this.page,
                pageSize: this.pageSize,
                user_id: this.id
            }
            if (this.status) {
                params.status = this.status
            }
            let res = await this.post('/api/video/center/list', params)
            if (res.code === 0) {
                this.total = res.data.total;
                this.videoList = this.videoList.concat(res.data.list)
            }
        }
    },
    methods: {
        async getVideoList() {
            let params = {
                page: this.page,
                pageSize: this.pageSize,
                user_id: this.id
            }
            let res = await this.post('/api/video/center/list', params)
            if (res.code === 0) {
                this.videoList = res.data.list
                this.total = res.data.total
                this.user = res.data.list[0].user
            }
        },
        goVideo(item) {
            this.navTo('/packageE/marketingTool/miniVideo/miniVideo?id=' + item.id + '&pause=true')
        },
    }
}
</script>
<style lang="scss" scoped>
.top {
    height: 248rpx;
    width: 100%;
    background-color: #F15353;
    position: relative;
    box-sizing: border-box;

    .img-user {
        width: 128rpx;
        height: 128rpx;
        border-radius: 50%;
        position: absolute;
        // border: 6rpx solid #fff;
        background-color: #FFFFFF;
        padding: 6rpx;
        top: 60rpx;
        left: 30rpx;
    }

    .top-user {
        height: 122rpx;
        background: #F5F5F5;
        border-radius: 30rpx 30rpx 0rpx 0rpx;
        padding-top: 22rpx;
        box-sizing: border-box;

        .user-name {
            margin-left: 177rpx;
            // padding-top: 24rpx;
            color: #00001C;
            font-size: 40rpx;
            font-weight: bold;
        }
    }
}

.video-card {
    width: 702rpx;
    margin: 17rpx auto;

    .video-box {
        width: 345rpx;
        // height: 470rpx;
        box-sizing: border-box;
        background: #FFFFFF;
        border-radius: 16rpx;
        position: relative;
        margin-top: 10px;

        .video-img {
            min-width: 345rpx;
            max-width: 345rpx;
            min-height: 278rpx;
            max-height: 278rpx;
            border-radius: 16rpx 16rpx 0rpx 0rpx;
        }

        .imgbut {
            width: 72rpx;
            height: 72rpx;
            position: absolute;
            background: rgba($color: #000000, $alpha: 0.3);
            border-radius: 50%;
            top: 103rpx;
            left: 136rpx;

            .icon-but {
                font-size: 55rpx;
                color: #FFFFFF;
            }
        }

        .title {
            width: 305rpx;
            height: 75rpx;
            margin: 24rpx auto 0;
            font-weight: 500;
            font-size: 28rpx;
            color: #00001C;
            line-height: 38rpx;
            word-break: break-all;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .video-ures {
            width: 305rpx;
            height: 56rpx;
            margin: 14rpx auto 22rpx;
        }

        .video-user-name {
            font-weight: 400;
            font-size: 24rpx;
            color: #232426;
            word-break: break-all;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
        }

        .video-user-time {
            font-weight: 400;
            margin-top: 10rpx;
            font-size: 20rpx;
            color: #808080;
        }

        .video-modal {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            background: rgba($color: #000000, $alpha: 0.5);
            border-radius: 16rpx;
        }
    }
}
</style>