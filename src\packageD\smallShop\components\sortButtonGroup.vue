<template>
  <view class="f fjsb h100">
    <slot></slot>
  </view>
</template>

<script>
export default {
  name: 'SortButtonGroup',
  componentName: 'SortButtonGroup',
  props: {
    value: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
}
</script>

<style lang="scss" scoped>
.f {
  display: flex;
}
.fjsb {
  justify-content: space-between;
}
.h100 {
  height: 100%;
}
</style>
