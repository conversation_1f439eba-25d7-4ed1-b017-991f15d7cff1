<template>
  <view>
    <!-- 加入小商店 start -->
    <view>
      <u-popup
        :closeable="true"
        :show="pricePopupShow"
        @close="onClosePricePopup('pricePopupShow')"
        @open="onOpenPricePopup"
      >
        <view class="d-c mt_40 fs-3">{{ headTitle }}</view>
        <view class="con-pop">
          <view class="p-20 bg-white b-r-10 d-f">
            <u-image
              width="140rpx"
              height="140rpx"
              :showLoading="true"
              :src="product.image_url"
            >
              <u-icon
                slot="error"
                size="40"
                color="#d0d0d1"
                name="photo"
              ></u-icon>
            </u-image>
            <view class="ml_20">
              <!-- 标题 start-->
              <view class="d-f mb_20">
                <view class="pr_20">
                  <text class="fs-2 ell">
                    {{ product.title }}
                  </text>
                </view>
              </view>
              <!-- 标题 end-->
              <view class="d-bf mt_10">
                <view class="c-orange">
                  <text class="fs-0-5">协议价</text>
                  <text class="fs-2">
                    {{ toYuan(product.agreement_price) }}元
                  </text>
                </view>
              </view>
              <view class="d-bf mt_10">
                <view class="c-orange">
                  <text class="fs-0-5">建议零售价</text>
                  <text class="fs-2">{{ toYuan(product.origin_price) }}元</text>
                </view>
              </view>
              <view class="d-bf mt_10">
                <view class="c-orange">
                  <text class="fs-0-5">指导价</text>
                  <text class="fs-2">{{ toYuan(product.guide_price) }}元</text>
                </view>
              </view>
              <view class="d-bf mt_10">
                <view class="c-orange">
                  <text class="fs-0-5">营销价</text>
                  <text class="fs-2">{{ toYuan(product.activity_price) }}元</text>
                </view>
              </view>
              <!-- 协议价 agreement_price 建议零售价 origin_price 指导价 guide_price  营销价 market_price -->
              <!-- <view class="d-bf mt_10">
                <view class="c-orange">
                  <text class="fs-0-5">利润</text>
                  <text class="fs-2">{{ toYuan(product.profit) }}元</text>
                </view>
              </view> -->
            </view>
          </view>
          <view class="d-bf mt_20">
            <view class="mr_20">定价策略:</view>
            <u-radio-group v-model="price_type">
              <u-radio
                size="30"
                activeColor="#f15353"
                labelSize="22rpx"
                labelColor="#202020"
                :customStyle="{ marginRight: '10rpx' }"
                v-for="(item, index) in priceTypes"
                :key="index"
                shape="circle"
                :name="item.name"
                :label="item.label"
              ></u-radio>
            </u-radio-group>
          </view>
          <view class="d-bf mt_40">
            <view class="mr_3">定价比例:</view>
            <u-input
              border="surround"
              v-model="price_proportion"
              fontSize="28rpx"
              type="digit"
              clearable
              @blur="computedSellingPrice"
            ></u-input>
            <view class="ml_30">%</view>
          </view>
          <!-- 批量改价 start -->
          <view class="ml_120 mt_20">
            <!-- <text
              class="c-fa mr_40"
              @click="onOpenEditPopup"
              v-if="product.single_option === 0"
            >
              批量改价
            </text> -->
            <view class="f fac">
              <view style="width: 200rpx">
                <u-input
                  border="surround"
                  v-model="price"
                  fontSize="28rpx"
                  type="digit"
                  clearable
                ></u-input>
              </view>
              <view class="ml_5 mr_30">元</view>
              <text class="c-fa" @click="calculateRatio">按此金额填充比例</text>
            </view>
            <view class="mt_10 fs-1 c-8a">
              注意协议价不含技术服务费，当前技术服务费比例{{
                toYuan(product.technical_services_fee)
              }}元！
            </view>
          </view>
          <view class="f fac mt_20">
            <view class="mr_20">预估销售价:</view>
            {{ estimate_price }}
            <view class="ml_10">元</view>
          </view>
          <view class="mt_10 fs-1 c-8a">
            此金额为按照当前中台售价的预估销售价，销售价会按照定价策略同步中台商品价格变化
          </view>
          <view class="f fac mt_10 c-orange">
            <view class="mr_20">利润:</view>
            <text>{{ toYuan(profit) }}元</text>
          </view>
          <!-- 批量改价 end -->
          <view class="mt_50">
            <u-button
              :loading="butLoading"
              type="error"
              text="确定"
              @click="onConfirmPricePopup"
            ></u-button>
          </view>
        </view>
      </u-popup>
    </view>
    <!-- 加入小商店 end -->
    <!-- 批量改价 start -->
    <view>
      <u-popup
        :closeable="true"
        :show="editPopupShow"
        @close="onClosePricePopup('editPopupShow')"
        @open="onOpenEditPopup"
        :zIndex="10090"
      >
        <view class="d-c mt_40 fs-3">批量改价</view>
        <view class="con-pop-2">
          <view>
            <u-row gutter="20" style="align-self: flex-start">
              <u-col span="7">
                <view class="d-f">
                  <view class="mr_20">销售价:</view>
                  <u-radio-group
                    placement="column"
                    v-model="takeGroup"
                    iconSize="24rpx"
                  >
                    <u-radio
                      :isDis="true"
                      size="40"
                      activeColor="#f15353"
                      labelSize="28rpx"
                      labelColor="#202020"
                      :customStyle="{ marginBottom: '34rpx' }"
                      v-for="(item, index) in takeValues"
                      :key="index"
                      :label="item.name"
                      :name="item.value"
                    ></u-radio>
                  </u-radio-group>
                </view>
              </u-col>
              <u-col span="5">
                <view class="d-bf" style="margin-bottom: 20rpx">
                  <u-input
                    placeholder="协议价比例"
                    border="surround"
                    v-model="formData.agreement_price_ratio"
                    fontSize="24rpx"
                    type="number"
                  ></u-input>
                  <view class="ml_20">%</view>
                </view>
                <view class="d-bf" style="margin-bottom: 20rpx">
                  <u-input
                    placeholder="建议零售价比例"
                    border="surround"
                    v-model="formData.origin_price_ratio"
                    fontSize="24rpx"
                    type="number"
                  ></u-input>
                  <view class="ml_20">%</view>
                </view>
                <view class="d-bf" style="margin-bottom: 20rpx">
                  <u-input
                    placeholder="指导价比例"
                    border="surround"
                    v-model="formData.guide_price_ratio"
                    fontSize="24rpx"
                    type="number"
                  ></u-input>
                  <view class="ml_20">%</view>
                </view>
                <view class="d-bf" style="margin-bottom: 20rpx">
                  <u-input
                    placeholder="营销价比例"
                    border="surround"
                    v-model="formData.activity_price_ratio"
                    fontSize="24rpx"
                    type="number"
                  ></u-input>
                  <view class="ml_20">%</view>
                </view>
              </u-col>
            </u-row>
          </view>
          <view class="mt_20">
            <view class="mt_10 fs-1 c-8a">
              注意：技术服务比例为{{ product.technical_services_fee / 100 }}%
              默认为建议零售价*100%
            </view>
            <view class="mt_10 fs-1 c-8a">
              如设置协议价*130%，则商品的销售价格为该商品协议价*130%；利润
              为协议价*（130%-100%-3%）；利润包含提现手续费！
            </view>
          </view>
          <view class="mt_50">
            <u-button
              type="error"
              text="确定"
              @click="onConfirmEditPopup"
            ></u-button>
          </view>
        </view>
      </u-popup>
    </view>
    <!-- 批量改价 end -->
  </view>
</template>
<script>
export default {
  name: 'PricePopup',
  props: {
    headTitle: {
      type: String,
      default: () => '',
    },
    pricePopupShow: {
      type: Boolean,
      default: false,
    },
    editPopupShow: {
      type: Boolean,
      default: false,
    },
    product: {
      type: Object,
      default: () => {},
    },
    currentAlbumIndex: {
      type: Number,
      default: 0,
    },
    albumsData: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    estimate_price() {
      let price = this.toFen(this.price)
      if (!price) {
        switch (this.price_type) {
          case 0:
            price = this.product.guide_price
            break
          case 1:
            price = this.product.origin_price
            break
          case 2:
            price = this.product.agreement_price
            break
          case 3:
            price = this.product.activity_price
            break
        }
      }
      return this.toYuan(price)
    },
    profit() {
      return this.toFen(this.estimate_price) - this.product.agreement_price
    },
  },
  data() {
    return {
      butLoading: false,
      price_type: 0,
      priceTypes: [
        {
          name: 2,
          label: '协议价',
        },
        {
          name: 0,
          label: '建议零售价',
        },
        {
          name: 1,
          label: '指导价',
        },
        {
          name: 3,
          label: '营销价',
        },
      ],
      price_proportion: 100,
      price: 0,
      settingId: null,
      takeGroup: 'is_agreement_price',
      takeValues: [
        {
          name: '协议价',
          value: 'is_agreement_price',
        },
        {
          name: '建议零售价',
          value: 'is_origin_price',
        },
        {
          name: '指导价',
          value: 'is_guide_price',
        },
        {
          name: '营销价',
          value: 'is_activity_price',
        },
      ],
      formData: {
        is_agreement_price: 0, // 协议价是否勾选
        is_origin_price: 0, // 建议零售价是否勾选
        is_guide_price: 0, // 指导价是否勾选
        is_activity_price: 0, // 营销价是否勾选
        agreement_price_ratio: 0, // 协议价比例
        origin_price_ratio: 0, // 建议零售价比例
        guide_price_ratio: 0, // 指导价比例
        activity_price_ratio: 0, // 营销价比例
        tip: 0, // 提示是否开启
        temp: 'is_agreement_price',
      },
    }
  },
  methods: {
    // 计算预估售价
    computedSellingPrice() {
      if (this.price_proportion < 100) {
        this.toast('定价比例不能小于100')
        return
      }
      switch (this.price_type) {
        case 0:
          this.price = this.toYuan(
            (this.product.origin_price * this.price_proportion) / 100,
          )
          break
        case 1:
          this.price = this.toYuan(
            (this.product.guide_price * this.price_proportion) / 100,
          )
          break
        case 2:
          this.price = this.toYuan(
            (this.product.agreement_price * this.price_proportion) / 100,
          )
          break
        case 3:
          this.price = this.toYuan(
            (this.product.activity_price * this.price_proportion) / 100,
          )
          break
      }
    },
    // 计算比例
    calculateRatio() {
      if (!this.price) {
        this.toast('请填写金额')
        return
      }
      let n1, n2
      /**
       * 协议价 agreement_price 建议零售价 origin_price 指导价 guide_price  营销价 market_price
       * {
          name: 2,
          label: '协议价',
        },
        {
          name: 1,
          label: '建议零售价',
        },
        {
          name: 0,
          label: '指导价',
        },
        {
          name: 3,
          label: '营销价',
        },
       */
      switch (this.price_type) {
        case 1:
          if (this.toFen(this.price) < this.product.guide_price) {
            this.toast(
              '填写金额不能小于' + this.toYuan(this.product.guide_price),
            )
            return
          }
          n1 = this.accSub(this.toFen(this.price), this.product.guide_price)
          n2 = this.accDiv(n1, this.product.guide_price)
          break
        case 0:
          if (this.toFen(this.price) < this.product.origin_price) {
            this.toast(
              '填写金额不能小于' + this.toYuan(this.product.origin_price),
            )
            return
          }
          n1 = this.accSub(this.toFen(this.price), this.product.origin_price)
          n2 = this.accDiv(n1, this.product.origin_price)
          break
        case 2:
          if (this.toFen(this.price) < this.product.agreement_price) {
            this.toast(
              '填写金额不能小于' + this.toYuan(this.product.agreement_price),
            )
            return
          }
          n1 = this.accSub(this.toFen(this.price), this.product.agreement_price)
          n2 = this.accDiv(n1, this.product.agreement_price)
          break
        case 3:
          if (this.toFen(this.price) < this.product.activity_price) {
            this.toast(
              '填写金额不能小于' + this.toYuan(this.product.activity_price),
            )
            return
          }
          n1 = this.accSub(this.toFen(this.price), this.product.activity_price)
          n2 = this.accDiv(n1, this.product.activity_price)
          break
      }
      this.price_proportion = Number((n2 + 1) * 100).toFixed(2)
    },
    onOpenPricePopup() {
      this.$emit('onOpenPricePopup')
      // this.price = this.toYuan(this.product.sale_price)
    },
    onOpenEditPopup() {
      this.$emit('onOpenEditPopup')
    },
    onConfirmPricePopup() {
      this.butLoading = true
      if (this.price_proportion < 100) {
        this.toast('定价比例不能小于100')
        return
      }
      const params = {
        product_ids: [this.product.id],
        price_proportion: this.price_proportion * 100,
        price_type: this.price_type,
      }
      this.price = 0
      this.price_proportion = 100
      this.price_type = 2
      this.$emit('onConfirmPricePopup', params)
      this.butLoading = false
    },
    onConfirmEditPopup() {
      const params = {
        product_ids: [this.product.id],
      }
      switch (this.takeGroup) {
        case 'is_agreement_price':
          params.price_proportion = this.formData.agreement_price_ratio * 100
          params.price_type = 2
          break
        case 'is_origin_price':
          params.price_proportion = this.formData.origin_price_ratio * 100
          params.price_type = 0
          break
        case 'is_guide_price':
          params.price_proportion = this.formData.guide_price_ratio * 100
          params.price_type = 1
          break
        case 'is_activity_price':
          params.price_proportion = this.formData.activity_price_ratio * 100
          params.price_type = 3
          break
        default:
          break
      }
      this.$emit('onConfirmPricePopup', params)
      this.takeGroup = 'is_agreement_price'
      this.formData = {
        is_agreement_price: 0, // 协议价是否勾选
        is_origin_price: 0, // 建议零售价是否勾选
        is_guide_price: 0, // 指导价是否勾选
        is_activity_price: 0, // 营销价是否勾选
        agreement_price_ratio: 0, // 协议价比例
        origin_price_ratio: 0, // 建议零售价比例
        guide_price_ratio: 0, // 指导价比例
        activity_price_ratio: 0, // 营销价比例
        tip: 0, // 提示是否开启
        temp: 'is_agreement_price',
      }
    },
    onClosePricePopup(flg = '') {
      this.price = 0
      this.price_proportion = 100
      this.price_type = 2

      this.formData = {
        is_agreement_price: 0, // 协议价是否勾选
        is_origin_price: 0, // 建议零售价是否勾选
        is_guide_price: 0, // 指导价是否勾选
        is_activity_price: 0, // 营销价是否勾选
        agreement_price_ratio: 0, // 协议价比例
        origin_price_ratio: 0, // 建议零售价比例
        guide_price_ratio: 0, // 指导价比例
        activity_price_ratio: 0, // 营销价比例
        tip: 0, // 提示是否开启
        temp: 'is_agreement_price',
      }
      this.$emit('onClosePricePopup', flg)
    },
    onCloseEditPopup() {
      this.$emit('onCloseEditPopup')
    },
  },
}
</script>
<style scoped>
/* 样式穿透失效 */
/* ::v-deep .u-input {
  margin-bottom: 20rpx;
  height: 40rpx;
  padding: 6rpx 18rpx!important;
} */
::v-deep .u-col {
  align-self: flex-start;
}
</style>
<style lang="scss" scoped>
.con-pop {
  padding: 30rpx 60rpx 60rpx 60rpx;
  height: 880rpx;
}
.con-pop-2 {
  padding: 30rpx 60rpx 60rpx 60rpx;
  min-height: 600rpx;
  max-height: 700rpx;
}
</style>
