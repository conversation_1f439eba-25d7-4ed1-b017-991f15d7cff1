<!-- 会员权益 -->
<template>
  <view>
    <view class="head">
      <view class="head_my d-f">
        <view class="user-info-img">
          <u-avatar
            size="110rpx"
            shape="circle"
            :src="userInfo.avatar"
          ></u-avatar>
        </view>
        <view class="my_name">
          <view v-if="(isMPWEIXIN && checkNull(userInfo)) || !isMPWEIXIN">
            <view class="font_size16 c-brown f-bold mt-15">
              {{ userInfo.nickname || '普通用户' }}
            </view>
          </view>
          <view class="mt-10 font_size12 c-gray4">
            <text v-if="validity">有效期:{{ validity }}</text>
          </view>
        </view>
        <view class="relative" v-clickoutside="onCloseDrawer">
          <view class="con-ellipsis">
            <view class="iconfont icon-ellipsis" @click="onHandleDrawer"></view>
          </view>
          <view :class="{ blockClass: isBlock, noneClass: isNone }">
            <view class="up-arrow"></view>
            <view class="box-drawer">
              <view
                class="d-fa-start pl-18"
                @click="navTo(`/packageD/memberLevel/memberPayRecord`)"
              >
                <view class="iconfont icon-bijijilu mr-10"></view>
                <view>开通记录</view>
              </view>
              <!-- <view class="d-fa-start mt_15 pl-18">
                <view class="iconfont icon-erweima mr-10"></view>
                <view>激活码</view>
              </view> -->
            </view>
          </view>
        </view>
      </view>
      <view
        v-if="levelList.length >= 1"
        :class="levelList.length === 1 ? 'wrap-one' : 'wrap-some'"
      >
        <u-swiper
          :list="swiperList"
          keyName="image"
          :height="levelList.length === 1 ? 290 : 230"
          previousMargin="0"
          :nextMargin="levelList.length === 1 ? 0 : 150"
          circular
          :autoplay="false"
          radius="5"
          bgColor="opacity:0"
          :showTitle="true"
          @change="onChangeTabs"
        >
          <view slot="indicator" class="indicator-num">
            <!-- <text class="indicator-num__text">当前等级</text> -->
          </view>
        </u-swiper>
      </view>
    </view>
    <view class="my_content">
      <!-- 当前等级权益 -->
      <view class="m-card-view mt_40">
        <view class="m-card-title d-cf">
          <view v-if="displayLevel.purchase_type === 2">当前等级权益</view>
          <view v-else>
            <template v-if="userInfo.nickname">
              <view class="d-fa-start">
                <view class="c-brown">{{ userInfo.nickname }}</view>
                等级权益
              </view>
            </template>
            <template v-else>等级权益</template>
          </view>
        </view>
        <view class="level_bg mt_20">
          <!-- name: {{ displayLevel.name }}
          <br />
          displayLevel.isShopPurchase:{{ displayLevel.isShopPurchase }}
          <br />
          displayLevel.isApiPurchase: {{ displayLevel.isApiPurchase }} -->
          <u-grid :border="false" col="4">
            <u-grid-item>
              <view class="font_size18 c-f14d4d f-bold">
                <image
                  class="icon-img"
                  src="/static/image/icon_rights_discount.png"
                ></image>
              </view>
              <view class="mt_10 mb_20">等级折扣</view>
              <view class="c-f14d4d">{{ toYuan(displayLevel.discount) }}%</view>
            </u-grid-item>
            <u-grid-item>
              <view class="font_size18 c-f14d4d f-bold">
                <image
                  class="icon-img"
                  src="/static/image/icon_rights_ratio.png"
                ></image>
              </view>
              <view class="mt_10 mb_20">服务费比例</view>
              <view class="c-f14d4d">
                {{ toYuan(displayLevel.server_ratio) }}%
              </view>
            </u-grid-item>
            <u-grid-item v-if="displayLevel.isShopPurchase">
              <view class="font_size18 c-f14d4d f-bold">
                <image
                  class="icon-img"
                  src="/static/image/icon_rights_purchase.png"
                ></image>
              </view>
              <view class="mt_10 mb_20">前端采购</view>
            </u-grid-item>
            <u-grid-item v-if="displayLevel.isApiPurchase">
              <view class="font_size18 c-f14d4d f-bold">
                <image
                  class="icon-img"
                  src="/static/image/icon_rights_api.png"
                ></image>
              </view>
              <view class="mt_10 mb_20">Api采购</view>
            </u-grid-item>
          </u-grid>
        </view>
      </view>
      <!-- 权益说明 -->
      <view class="m-card-view mt_30 mb_20">
        <view class="m-card-title d-bf">
          <text>权益说明</text>
        </view>
        <!-- 富文本展示 -->
        <view>
          <rich-text class="rich-text" :nodes="equity_statement"></rich-text>
        </view>
        <!-- <view class="mt_20 txt-wrap line48" v-html="equity_statement"></view> -->
      </view>
      <!-- 结算 -->
      <view class="d-bf con-check" v-if="displayLevel.pay_upgrade === 1">
        <view class="d-cf f-bold">
          {{ displayLevel.purchase_type === 2 ? '续费' : '升级费用' }}
          <text class="c-f1 fs-4 ml_20" v-if="displayLevel.pay_product.price">
            ¥{{ toYuan(displayLevel.pay_product.price) }}
          </text>
        </view>
        <view class="account_btn f-regular c-white" @click="onPay">
          {{ displayLevel.purchase_type === 2 ? '立即续费' : '立即升级' }}
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  name: 'memberRight',
  data() {
    return {
      isBlock: false,
      isNone: true,
      isMPWEIXIN: false, // 是否为微信小程序端
      validity: '',
      equity_statement: '',
      displayIndex: 0,
      displayLevel: {
        discount: 0,
        server_ratio: 0,
        isShopPurchase: false,
        isApiPurchase: false,
      }, // 视图所展示的等级
      swiperList: [],
      levelList: [
        // {
        //   id: 5,
        //   created_at: **********,
        //   updated_at: **********,
        //   name: '会员等级二',
        //   level: 2,
        //   expire_days: 200,
        //   discount: 1500,
        //   independent_ratio: 1,
        //   freight_deduction: 3,
        //   description: 'gdrdgdrg',
        //   order_count: 0,
        //   order_amount: 200,
        //   upgrade_product: null,
        //   is_update: 1,
        //   server_ratio: 300,
        //   cps_ratio: 0,
        //   jh_cps_ratio: 0,
        //   lian_ratio: 0,
        //   pay_upgrade: 1,
        //   is_purchase_perm: 1,
        //   purchase_perm: [
        //     {
        //       code: 1,
        //     },
        //     {
        //       code: 2,
        //     },
        //   ],
        //   pay_switch: 1,
        //   equity_statement: '',
        //   pay_product_id: 11489,
        //   purchase_type: 2,
        //   pay_product: {
        //     id: 11489,
        //     created_at: 1700554833,
        //     updated_at: **********,
        //     title: '会员等级二',
        //     origin_price: 20,
        //     guide_price: 20,
        //     price: 20,
        //     stock: 99999,
        //     cost_price: 20,
        //     activity_price: 20,
        //     sales: 3,
        //     sn: '',
        //     single_option: 0,
        //     desc: '',
        //     image_url: '',
        //     video_url: '',
        //     unit: '',
        //     barcode: '',
        //     freight: 0,
        //     freight_type: 0,
        //     detail_images: '',
        //     freight_template_id: 0,
        //     source: 0,
        //     sort: 0,
        //     source_goods_id: 0,
        //     child_title: '',
        //     skus: null,
        //     max_price: 0,
        //     min_price: 0,
        //     min_buy_qty: 0,
        //     code: '',
        //     weight: 0,
        //     sku_id: 0,
        //     bill_position: 0,
        //     tax_code: '',
        //     tax_product_name: '',
        //     tax_short_name: '',
        //     tax_option: '',
        //     tax_unit: '',
        //     favorable_policy: '',
        //     is_favorable_policy: 0,
        //     free_of_tax: 0,
        //     short_code: '',
        //     tax_measure_price: 0,
        //     tax_rate: 0,
        //     is_tax_logo: 0,
        //     is_display: 1,
        //     gather_supply_id: 29,
        //     status_lock: 0,
        //     md5: '',
        //     sku_image: '',
        //     original_sku_id: 0,
        //     supplier_source_id: 0,
        //     supplier_source_category_id: 0,
        //     is_plugin: 1,
        //     profit_rate: 0,
        //     is_bill: 0,
        //     supply_line: 'ZEddM',
        //     is_supply_line: 1,
        //     not_discount: 1,
        //     not_fee: 1,
        //   },
        // },
      ],
      userId: null,
      userInfo: {
        avatar: '',
      },
      page: 1,
      pageSize: 12,
      total: 0,
      bgOthers: '',
      bgCurrent: '',
    }
  },
  onLoad() {
    // #ifdef MP-WEIXIN
    this.isMPWEIXIN = true
    // #endif
    this.isBlock = false
    this.isNone = true
    this.getValidity()
    this.getSetting()
    this.getLevelList()
    this.getUserInfo()
    this.onCloseDrawer()
    // this.getBackgroundPic()
  },
  methods: {
    getBackgroundPic() {
      const params = {
        id: 532646,
      }
      this.get('/api/product/get', params)
        .then(res => {
          if (res.code === 0) {
            this.bgOthers = res.data.product.gallery[0].src // 其他等级背景图
            this.bgCurrent = res.data.product.gallery[1].src // 当前等级背景图
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    async getValidity() {
      this.post('/api/center/index', {}, true)
        .then(res => {
          if (res.code === 0) {
            this.userId = res.data.user.id
            this.validity = res.data.user.validity
            // this.validity = '2024-09-17 08:00:00'
            if (this.validity !== '长期有效') {
              const end = this.validity.indexOf(' ')
              this.validity = this.validity.slice(0, end)
              this.validity = this.validity.replace('-', '年')
              this.validity = this.validity.replace('-', '月')
              this.validity = this.validity.concat('号')
            }
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    onHandleDrawer() {
      if (!this.isBlock) {
        this.isBlock = true
        this.isNone = false
      } else {
        this.isBlock = false
        this.isNone = true
      }
    },
    onCloseDrawer() {
      this.isBlock = false
      this.isNone = true
    },
    onPay() {
      this.product_id = this.displayLevel?.pay_product_id
      const params = {
        product_id: this.product_id,
      }
      this.post('/api/userPurchase/purchase', params).then(res => {
        if (res.code === 0) {
          this.order_ids = res.data.order_ids
          uni.redirectTo({
            url: '/packageD/memberLevel/memberPayment',
          })
          uni.setStorageSync('orderIds', this.order_ids)
          uni.setStorageSync('pageSourse', 'memberRight')
          } else {
            this.$message.error(res.msg)
          }
        })
    },
    /* getProductId() {
      this.product_id = this.displayLevel?.pay_product?.id
      const params = {
        product_id: this.product_id,
      }
      this.post('/api/userPurchase/purchase', params).then(res => {
        if (res.code === 0) {
          this.order_ids = res.data.order_ids
        } else {
          this.$message.error(res.msg)
        }
      })
    }, */
    getSetting() {
      this.post('/api/user/findSetting').then(res => {
        if (res.code === 0) {
          if (this.swiperList.length === 0) {
            this.equity_statement = res.data.setting.value.equity_statement
            this.equity_statement = this.equity_statement.replace(/<img/g,'<img style="max-width:100%;height:auto;display:block;"',)
            this.equity_statement = this.equity_statement.replace(/figure/g,'div',)
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    async getUserInfo() {
      const res = await this.post(
        '/api/user/info',
        { userId: this.userId },
        true,
      )
      this.userInfo = res.data
    },
    // 切换轮播 -> 切换到的展示等级
    onChangeTabs(index) {
      this.displayIndex = index.current
      this.displayLevel = this.levelList[this.displayIndex]
      this.equity_statement = this.levelList[this.displayIndex].description
      this.equity_statement = this.equity_statement.replace(/<img/g,'<img style="max-width:100%;height:auto;display:block;"',)
      this.equity_statement = this.equity_statement.replace(/figure/g,'div',)
      this.computedPurchaseLogo(this.displayLevel.purchase_perm)
    },
    getLevelList() {
      const params = {
        page: this.page,
        pageSize: this.pageSize,
      }
      this.post('/api/user/getRenewLevels', params).then(res => {
        if (res.code === 0) {
          this.handleResList(res.data.list)
          // this.handleResList(this.levelList) // 测试专用：当只有1张图片时，适配轮播
          this.page = res.data.page
          this.pageSize = res.data.pageSize
          this.total = res.data.total
          this.initSwiperList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 重新拼接数组，使数组的第一项永远为“当前在用等级”
    handleResList(arr) {
      arr.map((item, index) => {
        if (item.purchase_type === 2) {
          const first = arr.splice(index)
          const second = arr
          this.levelList = first.concat(second)
        }else{
          this.levelList = arr
          this.displayIndex = 0
          this.displayLevel = this.levelList[this.displayIndex]
        }
        if(this.levelList && this.levelList.length){
          this.equity_statement = this.levelList[0].description
          this.equity_statement = this.equity_statement.replace(/<img/g,'<img style="max-width:100%;height:auto;display:block;"',)
          this.equity_statement = this.equity_statement.replace(/figure/g,'div',)
        }
        return this.levelList
      })
    },
    // 重新拼接数组后，再初始化swiperList
    initSwiperList() {
      this.levelList.map((item, index) => {
        const temp = {}
        temp.title = item.name
        // temp.image = require('../../static/image/bg_level_others.png')
        temp.image = "https://scympzt.oss-cn-shenzhen.aliyuncs.com/20241031/1730343789111.jpg"
        // temp.image = this.bgOthers // 其他等级背景图
        this.swiperList[index] = temp
        if (item.purchase_type === 2) {
          this.displayLevel = item
          this.swiperList[index].image = "https://scympzt.oss-cn-shenzhen.aliyuncs.com/20241031/1730343807222.jpg"
          // this.swiperList[index].image =require('../../static/image/bg_level_current.png')
          // this.swiperList[index].image = this.bgCurrent // 当前等级背景图
          this.computedPurchaseLogo(item.purchase_perm)
        }
        return this.swiperList
      })
    },
    computedPurchaseLogo(arr) {
      switch (arr.length) {
        case 0:
          // console.log('no')
          this.displayLevel.isShopPurchase = false
          this.displayLevel.isApiPurchase = false
          break
        case 1:
          switch (arr[0].code) {
            case 1:
              // console.log('1')
              this.displayLevel.isShopPurchase = true
              this.displayLevel.isApiPurchase = false
              break
            case 2:
              // console.log('2')
              this.displayLevel.isShopPurchase = false
              this.displayLevel.isApiPurchase = true
              break
            default:
              break
          }
          break
        case 2:
          // console.log('1 and 2')
          this.displayLevel.isShopPurchase = true
          this.displayLevel.isApiPurchase = true
          break
        default:
          break
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.h30 {
  height: 100rpx;
  background: #fce9c0;
}
.m-card-view {
  border-radius: 30rpx;
}
.level_bg {
  padding: 30rpx 0;
  border-radius: 30rpx;
  background-image: linear-gradient(#fbf1de, #fff);
}
.icon-img {
  width: 70rpx;
  height: 70rpx;
}
/*#ifdef H5*/
::v-deep .uni-view .my_content ul {
  list-style-type: none !important;
}
/*#endif*/

.pb_20 {
  padding-bottom: 20rpx;
}
.m-card-title {
  margin: 10rpx 0 30rpx 0;
  font-size: 30rpx;
}
.rich-text img {
  width: 100%;
}
.txt-wrap {
  word-wrap: break-word;
  word-break: normal;
  ::v-deep img {
    margin: 30rpx 0;
    width: 100%;
    height: auto;
  }
}
.line48 {
  line-height: 48rpx;
}
.blockClass {
  display: block;
}
.noneClass {
  display: none;
}
.relative {
  position: relative;
}
.up-arrow {
  position: absolute;
  top: 80rpx;
  right: 20rpx;
  border-left: 16rpx solid transparent;
  border-right: 16rpx solid transparent;
  border-top: 16rpx solid transparent;
  border-bottom: 16rpx solid black;
  opacity: 0.8;
}
.box-drawer {
  position: absolute;
  top: 106rpx;
  right: 0;
  padding: 26rpx 4rpx;
  width: 170rpx;
  line-height: 44rpx;
  text-align: center;
  border-radius: 10rpx;
  color: #fff;
  background: rgb(32, 32, 32);
  opacity: 0.8;
  z-index: 2;
}
.con-ellipsis {
  margin: 20rpx 12rpx 0 0;
  padding: 6rpx 6rpx;
  border-radius: 18rpx;
  background: #fce9c0;
}
.icon-ellipsis {
  font-size: 56rpx;
  color: #88541f;
}
.icon-bijijilu,
.icon-erweima {
  font-size: 24rpx;
}
.con-check {
  position: fixed;
  left: 0;
  bottom: 0;
  padding: 2vw 5vw;
  width: 90vw;
  height: 80rpx;
  border-top: 2rpx solid #dadbde;
  background: white;
}
.account_btn {
  background-color: #f14e4e;
  border-radius: 30px;
  padding: 18rpx 40rpx 18rpx 40rpx;
}
// 轮播 start
.wrap-some {
  margin-top: 60rpx;
  padding: 30rpx 0 30rpx 30rpx;
  border-radius: 30rpx;
  background: #fff;
}
.wrap-one {
  margin-top: 60rpx;
  padding: 30rpx 30rpx 30rpx 30rpx;
  border-radius: 30rpx;
  background: #fff;
}
::v-deep .u-swiper__wrapper__item__wrapper__title.data-v-7b038a67 {
  top: -40rpx;
  // padding: 30rpx 24rpx;
  padding: 60rpx 24rpx;
  height: 152rpx;
  font-size: 40rpx;
  font-weight: bolder;
  // color: black;
  color: #88541f;
  background-color: unset;
}
/*#ifdef H5*/
::v-deep .u-swiper__wrapper__item__wrapper__title {
  top: -40rpx;
  // padding: 30rpx 24rpx;
  padding: 60rpx 24rpx;
  height: 152rpx;
  font-size: 40rpx;
  font-weight: bolder;
  // color: black;
  color: #88541f;
  background-color: unset;
}
/*#endif*/

::v-deep .u-swiper__indicator.data-v-7b038a67 {
  position: absolute;
  top: 0;
  left: 0;
}
/*#ifdef H5*/
::v-deep .u-swiper__indicator {
  position: absolute;
  top: 0;
  left: 0;
}
/*#endif*/
::v-deep .u-grid.data-v-50bc7b32 {
  align-items: flex-start;
}
/*#ifdef H5*/
::v-deep .u-grid {
  align-items: flex-start;
}
/*#endif*/
// .indicator-num {
//   padding: 10rpx 16rpx;
//   background-color: rgba(246, 161, 160);
//   border-radius: 0 0 16rpx 0;
//   @include flex;
//   justify-content: center;
//   &__text {
//     color: #ffffff;
//     font-size: 24rpx;
//   }
// }
// 轮播 end
.head {
  position: relative;
  background-image: linear-gradient(#fbf1de, #f5f5f5);
  padding: 42rpx 30rpx 0 30rpx;
  .head_my {
    .user-info-img {
      width: 110rpx;
      height: 110rpx;
      border-radius: 50%;
      overflow: hidden;

      image {
        width: 110rpx;
        height: 110rpx;
        border-radius: 50%;
      }
    }
    .my_name {
      flex: 2;
      // width:60%;
      margin-left: 28rpx;

      .my_copy {
        width: 68rpx;
        height: 30rpx;
        line-height: 30rpx;
        border-radius: 100rpx;
        padding: 4rpx 8rpx;
        font-size: 22rpx;
        transform: scale(0.9);
        text-align: center;
        font-family: Arial;
        margin-left: 30rpx;
      }

      .my_member {
        // max-width: 210rpx;
        height: 40rpx;
        line-height: 40rpx;
        padding: 0 30rpx 0 14rpx;
        background-color: #a36705;
        color: #ffaa29;
        border-radius: 100rpx;

        .my_member_img {
          width: 33rpx;
          height: 30rpx;
          vertical-align: middle;
          margin: 0 8rpx 8rpx 0;
        }

        text {
          font-size: 22rpx;
          transform: scale(0.9);
        }
      }
    }
  }
  .icon-massage_set {
    font-size: 30rpx;
    color: #fff;
  }
}
.my_content {
  padding: 0 30rpx 130rpx 30rpx;
}
.fontw500 {
  font-weight: 500;
}
.icon-view {
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-radius: 50%;
  padding: 10rpx;
  &.icon-view1 {
    background-color: rgba($color: #fc8d2c, $alpha: 0.2);
    color: #fc8d2c;
  }
  &.icon-view2 {
    background-color: rgba($color: #fc2c49, $alpha: 0.2);
    color: #fc2c49;
  }
}
</style>
