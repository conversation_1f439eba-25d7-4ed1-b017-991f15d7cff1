<!-- 售后协商记录 -->
<template>
	<view>
		<view class="listData">
			<block v-for="(item,index) in negotiationList" :key="index">
				<view class="list-item">
					<view class="user-info d-f">
						<view class="face d-cc">
							<view class="icon-wrapper icon-user d-cc"  v-if="item.admin_id === 0">
								<view class="iconfont icon-fontclass-rengezhongxin"></view>
							</view>
							<view class="icon-wrapper icon-bussin d-cc"  v-else>
								<view class="iconfont icon-icon-test"></view>
							</view>
						</view>
						<view class="right-info">
							<view class="content">{{item.content}}</view>
							<view class="time">{{item.created_at}}</view>
						</view>
					</view>
					<view class="details">
							<view class="details-item">售后类型：{{item.refund_type_name}}</view>
							<view class="details-item">退款金额：{{item.price || 0.00}}</view>
							<view class="details-item">售后原因：{{item.reason_type_name}}</view>
							<view class="details-item">说明：{{item.description}}</view>
					</view>
				</view>
			</block>
			
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				negotiationList:[],
				listData: [
						{
								id: 93, 
								order_id: 378, 
								refund_id: 46, 
								operator: 1, 
								operator_id: 1, 
								operate_type: 0, 
								detail: [
										"售后类型：退款", 
										"退款金额：303.10", 
										"运费:20", 
										"其他费用:14", 
										"售后原因：拍错/多拍/不想要", 
										"说明："
								], 
								remark: "", 
								created_at: "2022-04-02 17:40:02", 
								operate_name: "用户发起申请"
						}, 
						{
								id: 94, 
								order_id: 378, 
								refund_id: 46, 
								operator: 0, 
								operator_id: 1, 
								operate_type: 6, 
								detail: [
										"退款完成", 
										"商家确认退款"
								], 
								remark: "", 
								created_at: "2022-04-02 17:40:21", 
								operate_name: "售后完成"
						}
				], 
			}
		},
		onShow(){
			let negotiationList = uni.getStorageSync('negotiation'); //获取售后详情的协商记录
			if(negotiationList) {
				this.negotiationList = negotiationList;
				for(let i in this.negotiationList) {
					this.negotiationList[i].price = this.toYuan(this.negotiationList[i].price);
					this.negotiationList[i].freight = this.toYuan(this.negotiationList[i].freight);
					this.negotiationList[i].technical_services_fee = this.toYuan(this.negotiationList[i].technical_services_fee);
				}
			}
		},
		methods: {
			
		}
	}
</script>

<style lang="scss" scoped>
	.listData {
		padding: 20rpx  30rpx;
		.list-item {
			margin-bottom: 20rpx;
			padding: 26rpx;
			background: #fff;
			border-radius: 20rpx;
			.user-info {
				padding-bottom: 26rpx;
				border-bottom: 2rpx solid #f2f2f2;
				.face {
					width: 58rpx;
					height: 58rpx;
					.icon-user {
						background-color: #ffa128;
					}
					.icon-bussin {
						background-color: #3b8bec;
					}
					.icon-wrapper {
						width: 36rpx;
						height: 36rpx;
						border-radius: 50%;
						.iconfont {
							color: #fff;
							font-size: 28rpx;
						}
					}
				}
				.right-info {
					padding-left: 15rpx;
					.content {
						font-size: 28rpx;
						color: #333;
					}
					.time {
						font-size: 24rpx;
						color: #666;
					}
				}
				
			}
			.details {
				padding-top: 16rpx;
				.details-item {
					line-height: 48rpx;
					font-size: 24rpx;
					color: #1e1e1e;
				}
			}
		}
	}
</style>
