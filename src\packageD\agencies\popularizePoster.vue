<template>
    <!-- 推广海报 -->
    <view>
        <!-- 选项卡切换 -->
        <view class="pt_20 bg-white d-fc-sa fs-2">
            <template v-for="item in listTab">
                <view class="bm pb-20" :key="item.title" v-if="title === item.title">{{ item.title }}</view>
                <view class="bmw pb-20" :key="item.title" v-else @tap="tabsTap(item)">{{ item.title }}</view>
            </template>
        </view>
        <view class="w-100 f fjc pt-50">
            <u--image :src="img_poster" width="650rpx" height="1000rpx"></u--image>
        </view>
    </view>
</template>

<script>
export default {
    name: 'popularizePoster',
    data() {
        return {
            listTab: [],
            title: '',
            img_poster: '',
            identity_type: ''
        }
    },
    onLoad() {
        this.getPoster()
    },
    methods: {
        
        tabsTap(res) {
            this.title = res.title;
            this.img_poster = res.poster_link
        },
        // 获取海报
        async getPoster() {
            let res = await this.get('/api/institution/getPoster')
            if (res.code === 0) {
                this.listTab = res.data.poster_results
                this.title = res.data.poster_results[0].title
                this.img_poster = res.data.poster_results[0].poster_link
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.bm {
    border-bottom: 5rpx #f14e4e solid;
    box-sizing: border-box;
    color: #f14e4e;
}

.bmw {
    border-bottom: 5rpx white solid;
    box-sizing: border-box;
}

.poster_img {
    width: 80%;
    margin: 50rpx auto 0;
    background-color: #f14e4e;
}

.uButton {
    width: 600rpx;
    margin-top: 20rpx;
}
</style>