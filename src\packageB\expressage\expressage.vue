<template>
	<view class="expressage">
		<u--form
						labelPosition="left"
						:model="expressageForm"
						:rules="rules"
						ref="expressageForm"
				>
					<u-form-item
							label="快递公司"
							prop="company_name"
							borderBottom
							ref="company_name"
							labelWidth="176rpx"
							style="padding:0rpx 32rpx;"
					>
						<select-lay 
							:zindex="1211" 
							:value="expValue" 
							name="name"
							slabel="name" 
							svalue="code"
							:placeholder="expValue || '请选择物流公司'" 
							:options="logisticsList"
							@selectitem="selectitem">
						</select-lay>
					</u-form-item>
					<u-form-item
							label="快递单号"
							prop="express_no"
							borderBottom
							ref="item1"
							labelWidth="176rpx"
							style="padding:0rpx 32rpx;"
					>
						<u--input
								v-model="expressageForm.express_no"
								disabledColor="#ffffff"
								placeholder="请输入快递单号"
								border="none"
						></u--input>
						
					</u-form-item>
				</u--form>
				<view class="mb60"></view>
				<view class="expBtn" @click="$clicks(expFormBtn)">提交</view>
				<view class="mb60"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				logisticsList:[],
				expValue:'',
				expressageForm:{
					after_sales_id:0,
					company_code:'',
					company_name:'',
					express_no:'',
					shipping_address_id:0
				},
				rules:{
					'company_name': {
						type: 'string',
						required: true,
						message: '请输入快递公司信息',
						trigger: ['blur']
					},
					'express_no': [
						{
							type: 'string',
							required: true,
							message: '请输入快递单号',
							trigger: ['blur']
						},
					]
				}
			}
		},
		onLoad(options) {
			if ((options.after_sales_id ?? '') !== '') {
				this.expressageForm.after_sales_id = parseInt(options.after_sales_id);
			}
			if ((options.shipping_address_id ?? '') !== '') {
				this.expressageForm.shipping_address_id = parseInt(options.shipping_address_id);
			}
		},
		onShow() {
			this.logistics();
		},
		methods: {
			logistics() {
				this.get(`/api/shipping/company/list`, {}, true).then((res) => {
					if (res.code === 0) {
						let data = res.data;
						console.log(data);
						this.logisticsList = data.list;
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			selectitem(index, item) {
					console.log(item);
					console.log(index);
					({
						code:this.expressageForm.company_code,
						name:this.expressageForm.company_name,
					} = item);
					if (index >= 0) {
							console.log(item.name);
							this.expValue = item.name;
					} else {
							this.expValue = ""
					}
					this.$refs.expressageForm.validateField('company_name') //重新校验一次
			},
			expFormBtn() {
				this.$refs.expressageForm.validate().then(res => {
					this.post('/api/afterSales/send',this.expressageForm).then((res) => {
						console.log(res);
						if(res.code === 0) {
							let data = res.data;
							uni.$u.toast('填写成功')
							// this.addForm = {};
							this.$refs.expressageForm.clearValidate(this.rules);
							setTimeout(() => {
								this.backRefresh();
							},500)
							
						} else {
							this.toast(res.msg);
						}
					}).catch((Error) => {
						console.log(Error);
					})
				}).catch(errors => {
					uni.$u.toast('填写错误')
				})
			}
		}
	}
</script>
<style scoped>
	.expressage ::v-deep .u-form-item__body__right__content__slot {
		display: block;
	}
</style>
<style lang="scss" scoped>
	.expressage {
		background-color: #fff;
		height: 100vh;
		.mb60 {
			height: 120rpx;
			clear: both;
		}
		.expBtn {
			width: 335px;
			height: 76rpx;
			line-height: 76rpx;
			border-radius: 10rpx;
			text-align: center;
			background-color: rgba(239, 82, 82, 100);
			color: #fff;
			font-size: 28rpx;
			margin: 0 auto;
			
		}
	}
</style>
