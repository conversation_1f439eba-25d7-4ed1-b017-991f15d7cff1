<!-- 下单页信息 -->
<template>
	<view>
		<!--tab栏目切换 -->
		<u-tabs :list="shipping_methods" lineWidth="60rpx" lineHeight='5' lineColor="#f56c6c" :activeStyle="{
								color: '#303133',
								fontWeight: 'bold',

							}" :inactiveStyle="{
								color: '#606266',
							}" @click="tabsTap" itemStyle="width:50%;height: 88rpx;text-align:center;background:#fff">
		</u-tabs>
		<view class="express" v-if="!disabledBtn">
			<!--收件人信息 -->
			<view class="ex_message d-cf" v-if="default_address.id === 0" @click="addressOrderOpen('order')">
				<view class="iconfont icon-service_n"></view>
				<view class="d-f-c flex_max ex_name">
					<text>请点击选择地址</text>

				</view>
			</view>
			<view class="ex_message d-cf" v-else @click="addressOrderOpen('order')">
				<view class="iconfont icon-service_n"></view>
				<view class="d-f-c flex_max ex_name">
					<text>收件人:{{default_address.realname}} {{default_address.mobile}}</text>
					<text>{{default_address.province}} {{default_address.city}} {{default_address.county}}
						{{default_address.town}} {{default_address.detail}}</text>
				</view>
				<view class="iconfont icon-advertise-next"></view>
			</view>
			<view class="ex_message d-cf font_size14" v-if="index == 2">
				抱歉，您所在地区暂无自提点，或<text class="c-f1">手动切换位置</text>
			</view>

			<view class="mb10"></view>
			<!--填写用户信息 -->
			<view class="express_msg" v-if="index === 3">
				<u--form labelPosition="left" labelAlign="center" :model="pickForm" :rules="rules" ref="form1">
					<u-form-item label="提货人姓名" labelWidth='250rpx' labelAlign='right' prop="userInfo.name" borderBottom
						ref="item1">
						<u--input v-model="pickForm.name" border="none" placeholder="请输入提货人姓名"
							placeholderStyle="color: #333;"></u--input>
					</u-form-item>
					<u-form-item label="提货人手机" labelWidth='250rpx' labelAlign='right' prop="userInfo.sex" borderBottom
						ref="item1">
						<u--input v-model="pickForm.mobile" placeholder="请输入提货人手机" border="none"
							placeholderStyle="color: #333;"></u--input>
					</u-form-item>
				</u--form>
			</view>

			<view class="mb30"></view>

			<view class="shop">
				<block v-for="(item,index) in ordersList" :key="index">
					<view class="shop_title">店铺名称：{{item.title}}</view>
					<view class="shop_detail">
						<!-- 商品信息-->
						<block v-for="(goodsitem,goodsindex) in item.order_items" :key="goodsitem.shopping_cart_id">
							<view class="goods d-f">
								<view class="img">
									<image mode="widthFix" :src="goodsitem.image_url"></image>
								</view>
								<view class="wrap">
									<view class="inner d-bf2  c-gray3 font_size14 ">
										<view class="name ell">{{goodsitem.title}} </view>
										<view class="option ">规格: {{goodsitem.sku_title}}</view>
									</view>
									<view class="price d-bf2">
										<text>¥{{goodsitem.price}}</text>
										<text>x{{goodsitem.qty}}</text>
									</view>
								</view>
							</view>
						</block>
						<view class="note d-cf">
							<text class="c-note font_size12">买家留言:</text>
							<u--input placeholder="50字以内（选填）" border="none" clearable v-model="item.remark"
								placeholderStyle="font-size:12px;color: #858585;"></u--input>
						</view>
						<!-- 商品的属性-->
						<view class="tbs">
							<view class="num list d-bf2">
								<view>商品金额</view>
								<view class="c-f1">¥ {{item.item_amount || 0.00}}</view>
							</view>
							<view class="list d-bf2">
								<view>
									<text>运费 </text>
								</view>
								<view class="c-f1">¥{{item.freight || 0.00}}</view>
							</view>
						</view>
					</view>
				</block>

				<view class="mb20"></view>
				<!--发票类型 -->
				<view class="bg-white">
					<u-cell-group :border="false">
						<u-cell size="large" title="发票" :border="false" isLink @click="invoice"></u-cell>
					</u-cell-group>
				</view>

				<view class="mb20"></view>

				<view class="tbs">
					<block v-for="(item,amountIndex) in amount_detail.amount_items" :key="amountIndex">
						<view class="list d-bf2">
							<view>{{item.title}}</view>
							<view class="c-f1">¥{{item.amount || 0.00 }}</view>
						</view>
					</block>
				</view>

				<view class="mb106"></view>
			</view>
		</view>

		<!-- 提交订单 -->
		<view class="detail_pay d-ef font_size14">
			<view class="total">合计：<text class="f-regular c-f1" >¥{{amount_detail.amount ? amount_detail.amount : 0.00}}</text></view>
			<view class="order_delete" :class="disabledBtn ? 'disabled' : ''" @click="orderBtn">提交订单</view>
		</view>

		<!-- 组件做判断，不然多少数据就加载多少次组件 -->
		
		<invoice-popup
				v-if="invoiceShow"
				:billShow="invoiceShow"
				:order_bill="orderBill"
				:setting="settingBill"
				@billClose="billClose"
				@BillConfirm="orderBillBtn"
				
		>
		</invoice-popup>
		<receiving-popup v-if="destroyShow"></receiving-popup>
		<address-from-popup v-if="destroyShow"></address-from-popup>
		<u-overlay :show="overBillShow" :z-index ="zIndex"></u-overlay>
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
	import invoicePopup from '@/components/invoicePopup.vue';
	import receivingPopup from '@/components/receivingPopup/receivingPopup.vue';
	import addressFromPopup from '@/components/addressFromPopup/addressFromPopup.vue';
	import eventBus from '@/utils/eventBus';
	export default {
		components:{
			invoicePopup,
			receivingPopup,
			addressFromPopup,
		},
		provide() {
			return {
				bill_type: () => this.billType, //孙组件数据
			}
		},
		data() {
			return {
				disabledBtn: true,
				zIndex:10070,
				invoiceShow:false,//发票显示隐藏
				overBillShow:false,// 遮罩层的显示隐藏
				goodsAddList:[], //地址列表
				billType:[], //发票类型，判断是否开发票
				settingBill:{}, //发票所有的设置
				billTypeShow:[], //是否显示发票功能
				index: 0,
				destroyShow:false, //销毁组件标识
				addressShow: false,
				addFormShow: false,
				addressName: '',
				orderBill: {}, //发票内容
				billAddressName: '',
				billAddressId: 0,
				formData: {
					buy_id: 0,
				},
				amount_detail: {}, // //商品价格信息
				shipping_methods: [], //tab导航栏数据
				ordersList: [], //商品所有信息
				commodity: 0, //商品金额
				default_address: {}, //地址信息
				pickForm: {
					name: '',
					mobile: '',
				},
				chooseName: '',
			}
		},
		created() {
			eventBus.$on('overlay', (val) => { //使用bus兄弟传值
				this.zIndex = val;
			})
			eventBus.$on('destroyShow', (val) => { //使用bus兄弟传值
				this.destroyShow = val;
			})
			eventBus.$on('overlayBill', (val) => { //默认地址调用和发票调用不同
				this.overBillShow = val;
				this.destroyShow = val;
			})
			eventBus.$on('orderAddress', (item) => { //默认地址调用和发票调用不同
				this.default_address = item;
				uni.setStorageSync('default_address', item);
				this.addressShow = false;
				if(this.default_address.id) {
					this.ordersList.forEach(item => {
						item.address = this.default_address;
						item.address_id = this.default_address.id;
					})
				}
				this.formData.orders = this.ordersList;
				this.orders(this.formData);
			})
		},
		onLoad(options) {
			console.log('1112');
			if ((options.buy_id ?? '') !== '') {
				this.formData.buy_id = parseInt(options.buy_id);
			}
		},
		onShow() {
			this.orders(this.formData);
			this.findTradeSetting();
		},
		onHide() {
			console.log('1112');
		},
		destroyed() {
			console.log("orderAddress被销毁了");
			eventBus.$off("orderAddress");
		},
		methods: {
			//子组件传过来的数据
			tabsTap(item) {
				this.ordersList[0].shipping_method_id = item.id;
				this.index = item.index;
			},
			billClose(show,overlay) {  //关闭发票
				this.invoiceShow = !show;
				this.overBillShow = !show;
			},
			invoice() {
				if (!this.$isEmpty.isEmpty(this.billTypeShow)) {
					this.invoiceShow = true;
					this.overBillShow = true;
					eventBus.$emit('overlay', 10070); //设置发票详情的遮罩层
				} else {
					this.toast('发票功能已关闭');
				}

			},
			orderBillBtn(bill,show) { //提交发票
				this.orderBill = bill;
				this.invoiceShow = false;
				this.overBillShow = false;
			},
			addressOrderOpen(name) {
				this.overBillShow = true;
				this.destroyShow =true;
				this.$nextTick(function(){
					eventBus.$emit('upaddressList', true);
					eventBus.$emit('chooseName', name);
				})
				
			},
			findTradeSetting() { //获取发票设置
				this.get('/api/bill/findTradeSetting', {}, true).then((res) => {
					if (res.code === 0) {
						let data = res.data;
						this.settingBill = data.setting?.value;
						this.billTypeShow = data.setting?.value.bill_type;
						return this.billType = data.setting?.value.bill_type;

					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			orders(formData) { //提交订单
				this.post('/api/trade/checkout', this.formData, true, true, false).then((res) => {
					if (res.code === 0) {
						let data = res.data;
						this.amount_detail = data.amount_detail;
						this.amount_detail.amount = this.toYuan(this.amount_detail.amount);
						let amount_items = this.amount_detail.amount_items;
						for (let i in amount_items) {
							amount_items[i].amount = this.toYuan(amount_items[i].amount);
						}
						this.shipping_methods = data.orders[0].shipping_methods; //快递还是门店自提
						this.ordersList = data.orders;
						let ordersList = this.ordersList;
						for (let i in ordersList) {  //处理订单的价格
							ordersList[i].item_amount = this.toYuan(ordersList[i].item_amount);
							ordersList[i].freight = this.toYuan(ordersList[i].freight);
							this.orderBill = ordersList[i].order_bill;
							for (let j in ordersList[i].order_items) { 
								ordersList[i].order_items[j].price = this.toYuan(ordersList[i].order_items[j].price);
								
							}
						}
						let default_address = uni.getStorageSync('default_address');
						if (default_address && Object.keys(this.default_address).length > 0) { //防止刷新默认地址丢失
							this.default_address = default_address;
							console.log(this.default_address);
						} else {
							this.default_address = data.default_address;
						}
						this.disabledBtn = false
					} else {
							// type:"error",
						this.$refs.uToast.show({
							message: res.msg,
							duration: 4000
						})
						/* uni.showToast({
							title: res.msg,
							duration: 4000
						}); */
						setTimeout(() => {
							uni.navigateBack({
								delta: 1
							})
						}, 4000)
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			orderBtn() { //提交订单
				if(this.disabledBtn){
					return;
				}
				if (this.default_address.is_default === false || this.default_address.is_default === null) {
					for (let i in this.ordersList) {
						this.ordersList[i].address = this.default_address;
					}
				}
				if (this.default_address.id) {
					this.post('/api/trade/confirm', {
						buy_id: this.formData.buy_id,
						orders: this.ordersList
					}, true).then((res) => {
						if (res.code === 0) {
							let data = res.data;
							uni.redirectTo({
								url: '/packageA/goodsorder/orderpay/orderpay'
							});
							let orderIds = data.order_ids;
							uni.setStorageSync('orderIds', orderIds);
						} else {
							this.toast(res.msg);
						}
					}).catch((Error) => {
						console.log(Error);
					})
				} else {
					this.toast('请选择收货地址');
				}

			}
		}
	}
</script>
<style scoped>
	::v-deep .u-tabs__wrapper__nav__item {
		padding: 0rpx;
	}

	/*修复：tabs组件添加css样式*/
	::v-deep .u-tabs__wrapper__nav {
		position: relative;
	}

	.invoice ::v-deep .u-form-item__body {
		padding: 0;
	}

	.invoice_name ::v-deep .u-cell__body {
		padding: 0;
	}

	.from-content ::v-deep .u-form-item__body__right .u-form-item__body__right__content__slot {
		justify-content: flex-end;
	}

	.from-content .area ::v-deep .u-form-item__body__right .u-form-item__body__right__content__slot {
		justify-content: flex-start;
	}
</style>
<style lang="scss" scoped>
	//重复样式
	.tbs {
		background-color: #fff;
		padding: 20rpx 28rpx 20rpx 28rpx;

		.list {
			width: 100%;
			padding-bottom: 20rpx;
		}
	}

	//公共样式
	.type_icon {
		font-size: 28rpx;
		color: #f15353;
	}

	.express {
		.ex_message {
			padding: 28rpx;
			background-color: #fff;

			.ex_name {
				padding: 0 20rpx;
			}
		}

		.express_msg {
			background-color: #fff;
		}

		.shop {
			.shop_title {
				padding: 0 0 0 28rpx;
				height: 72rpx;
				line-height: 72rpx;
				border-bottom: 1px solid #E8E8E8;
				background-color: #fff;
			}

			.shop_detail {
				background-color: #fff;

				.goods {
					padding: 32rpx 28rpx 0 28rpx;

					.img {
						width: 30%;
						display: inline-block;

						image {
							width: 100%;
							height: auto;
						}
					}

					.wrap {
						width: 70%;
						margin-left: 20rpx;
						position: relative;

						.inner {
							.name {
								width: 60%;
								margin-right: 35rpx;
							}

							// height: 80rpx;

						}
					}

				}

				.note {
					margin: 32rpx 16rpx 16rpx;

					.c-note {
						width: 128rpx;
					}
				}
			}
		}
	}

	.detail_pay {
		width: 100%;
		height: 96rpx;
		z-index: 98;
		font-size: 32rpx;
		text-align: left;
		background: #fff;
		margin-top: 60rpx;
		border-top: 1px solid #eaeaea;
		position: fixed;
		bottom: 0;

		.total {
			flex: 3;
			text-align: right;
			padding-right: 20rpx;
		}

		.order_delete {
			flex: 1;
			width: auto;
			background: #f15353;
			text-align: center;
			color: #fff;
			line-height: 96rpx;
			position: relative;
			&.disabled{
				background: #d4d4d4;
				color: #000000;
			}
		}
	}
</style>
