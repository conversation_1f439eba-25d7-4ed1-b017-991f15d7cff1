import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex)
const store = new Vuex.Store({
    state: {
		product_ids:[],
		userLevelPriceTitle:"",
		discount_type: null,
		agentShow:true,
		homePage:{  //首页筛选参数
			page:1,
			pageSize:10,
			category1_id:1,
			sort_by:'',
		},
		searchGoods:{ //商品搜索页面筛选参数
			page:1,
			pageSize:10,
			sort_by:'',
			title:'',
			supplier_id:'',
			category1_id:'',
			category2_id:'',
			category3_id:'',
			price_form:'',
			price_to:'',
			profit_form:'',
			profit_to:'',
		},
		collectPage:{ //个人收藏页面商品数据筛选参数
			page:1,
			pageSize:10,
			title:'',
			lose_efficacy:0,
		},
		newUserData:{
			discountRate:'', //最新的折扣
			latestUserLevel:'',//最新的用户权重
		},
		collectionShows:false, //是否显示收藏
		cancellationArray:[], //商品取收藏状态数组
		storeToCancel:[],//店铺取收藏状态数组
		homeNoMore:false ,//判断是否显示查看更多
		shadeShow:false, //分享遮罩层是否显示
	},
    mutations: {
		updateUserLevelPriceTitle(state,title){
            state.userLevelPriceTitle = title
        },
		updateProductIds(state,ids){
			state.product_ids = ids
		},
		updateAgentShow(state,status){
			state.agentShow = status
		},
		upHomePage(state,num){
			for (let key in num) {
			　	state.homePage[key] = num[key]
			}
		},
		upSearchGoods(state,num){
			for (let key in num) {
			　	state.searchGoods[key] = num[key]
			}
		},
		upCollectPage(state,num){
			for (let key in num) {
			　	state.collectPage[key] = num[key]
			}
		},
		upNewUserData(state,num){
			for (let key in num) {
			　	state.newUserData[key] = num[key]
			}
		},
		upHomeNoMore(state,num){
			state.homeNoMore = num
		},
		upShadeShow(state,num){
			state.shadeShow = num
		},
		upCollectionShows(state,num){
			state.collectionShows = num
		},
		upCancellationArray(state,num){
			state.cancellationArray = num
		},
		upStoreToCancel(state,num){
			state.storeToCancel = num
		},
		categoryId(state) {
			this.searchGoods.category2_id = state;
		},
		upDiscountType(state,num){
			state.discount_type = num
		}
	},
	actions: {  //修改数据经过这一步操作，避免数据丢失
	},
	modules:{
	},
	getters: {
		userLevelPriceTitle(state){
			return state.userLevelPriceTitle
		}
	}
})
export default store