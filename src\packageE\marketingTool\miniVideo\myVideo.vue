<template>
    <view>
        <view class="top">
            <view style="height: 130rpx;"></view>
            <view class="img-user">
                <u-avatar :src='user.avatar' size="128rpx"
                    shape="circle"></u-avatar>
            </view>
            <view class="top-user">
                <span class="user-name">{{ user.nickname ? user.nickname : '用户' + user.username.slice(-4) }}</span>
            </view>
        </view>
        <view class="divider"></view>
        <view class="f fac fjsb tabs">
            <template v-for="item in tabsList">
                <view class="f fac tabs-box" @click="changeTabs(item)" :key="item.status">
                    <view class="tabname" :style="{ 'color': item.status === status ? '#00001C' : '#3B3B4A' }">{{
                        item.name
                    }}</view>
                    <view v-if="item.status === status" class="line"></view>
                    <view v-else class="line-false"></view>
                </view>
            </template>
        </view>
        <view class="f fac fjsb fw video-card">
            <template v-for="item in videoList">
                <view class="video-box" @click="goVideo(item)" :key="item.id">
                    <view style="position: relative;">
                        <image :src="item.cover_url" mode="widthFix" class="video-img" />
                        <view class="f fac fjc imgbut">
                            <span class="iconfont icon-you icon-but"></span>
                        </view>
                    </view>
                    <view class="title">
                        {{ item.title }}
                    </view>
                    <view class="video-ures f fac">
                        <u-avatar :src='item.user.avatar' size="56rpx" shape="circle"></u-avatar>
                        <view style="margin-left: 8rpx;">
                            <view class="video-user-name">{{ item.user.nickname ? item.user.nickname : '用户' + item.user.username.slice(-4) }}</view>
                            <view class="video-user-time">{{ formatDateTime(item.created_at) }}</view>
                        </view>
                    </view>

                    <view class="video-modal" v-if="item.status !== 2">
                        <view class="f fac d-f-c " style="margin-top: 180rpx;">
                            <view v-if="item.status === 1" class="iconfont icon-fontclass-daiqueren icon-but-shenhe">
                            </view>
                            <view v-else class="iconfont icon-shenhebutongguo icon-but-shehebutongg"></view>
                            <view class="statuc-name">{{ item.status === 1 ? '审核中' : '审核不通过' }}</view>
                        </view>
                        <view style="width: 170rpx;margin: 30rpx auto 0;" v-if="item.status === 3" @click.stop="">
                            <u-button type="primary" color="#F0F0F1" shape="circle" :plain="true" text="查看原因"
                                @click="findReason(item)"></u-button>
                        </view>
                    </view>
                </view>
            </template>
        </view>

        <u-modal :show="show" @confirm="confirm" width="500rpx">
            <view class="slot-content">
                <view class="content" v-html="reason">
                </view>
            </view>
        </u-modal>

        <view class="addVideo f fac d-f-c" @click="goAddVideo">
            <view class="iconfont icon-fontclass-ship ship-video"></view>
            <view class="addVideo-text">发布</view>
        </view>
    </view>
</template>
<script>
export default {
    data() {
        return {
            user: {},
            tabsList: [
                {
                    name: '全部',
                    status: 0
                },
                {
                    name: '审核中',
                    status: 1
                },
                {
                    name: '已发布',
                    status: 2
                },
                {
                    name: '审核不通过',
                    status: 3
                },
            ],
            videoList: [],
            page: 1,
            pageSize: 10,
            total: 0,
            status: 0,

            show: false,
            reason: '',
        }
    },
    onShow() {
        this.getVideoList();
        this.user = uni.getStorageSync('user')
    },
    // 加载更多数据
    async onReachBottom() {
        if (this.videoList.length < this.total) {
            this.page = ++this.page
            let params = {
                page: this.page,
                pageSize: this.pageSize,
                user_id: parseInt(uni.getStorageSync('user').id)
            }
            if (this.status) {
                params.status = this.status
            }
            let res = await this.post('/api/video/self/list', params)
            if (res.code === 0) {
                this.total = res.data.total;
                this.videoList = this.videoList.concat(res.data.list)
            }
        }
    },
    methods: {
        // 发布视频
        goAddVideo() {
            this.navTo('/packageE/marketingTool/miniVideo/addVideo')
        },
        // 查看视频
        goVideo(item) {
            this.navTo('/packageE/marketingTool/miniVideo/findVideo?id=' + item.id + '&pause=true')
        },
        confirm() {
            this.show = false
        },
        // 查看原因
        findReason(item) {
            this.reason = item.rejected_reason;
            this.show = true
        },
        // 获取我的视频
        async getVideoList() {
            let params = {
                page: this.page,
                pageSize: this.pageSize,
                user_id: parseInt(uni.getStorageSync('user').id)
            }
            if (this.status) {
                params.status = this.status
            }
            let res = await this.post('/api/video/self/list', params)
            if (res.code === 0) {
                this.total = res.data.total;
                this.videoList = res.data.list;
                this.tabsList[0].name = `全部(${res.data.total})`;
                this.tabsList[1].name = `审核中(${res.data.auditTotal})`;
                this.tabsList[2].name = `已发布(${res.data.adoptTotal})`;
                this.tabsList[3].name = `审核不通过(${res.data.rejectTotal})`;
            }
        },
        changeTabs(e) {
            this.status = e.status
            this.page = 1;
            this.getVideoList()
        }
    }
}
</script>
<style lang="scss" scoped>
.top {
    height: 248rpx;
    width: 100%;
    background-color: #F15353;
    position: relative;
    box-sizing: border-box;

    .img-user {
        width: 128rpx;
        height: 128rpx;
        border-radius: 50%;
        position: absolute;
        // border: 6rpx solid #fff;
        background-color: #FFFFFF;
        padding: 6rpx;
        top: 60rpx;
        left: 30rpx;
    }

    .top-user {
        height: 122rpx;
        background: #F5F5F5;
        border-radius: 30rpx 30rpx 0rpx 0rpx;
        padding-top: 22rpx;
        box-sizing: border-box;

        .user-name {
            margin-left: 177rpx;
            // padding-top: 24rpx;
            color: #00001C;
            font-size: 40rpx;
            font-weight: bold;
        }
    }
}

.tabs {
    width: 680rpx;
    height: 32rpx;
    margin: 37rpx auto;

    .tabs-box {
        font-size: 28rpx;
        margin: 0 auto;
        flex-direction: column;

        .tabname {
            margin-bottom: 12rpx;
        }

        .line {
            width: 34rpx;
            height: 6rpx;
            background-color: #F15353;
            border-radius: 3rpx;
        }

        .line-false {
            width: 34rpx;
            height: 6rpx;
            border-radius: 3rpx;
        }
    }
}

.divider {
    width: 702rpx;
    height: 1px;
    background: #E9E9EC;
    margin: 2px auto 0;
}

.video-card {
    width: 702rpx;
    margin: 17rpx auto;

    .video-box {
        width: 345rpx;
        // height: 470rpx;
        box-sizing: border-box;
        background: #FFFFFF;
        border-radius: 16rpx;
        position: relative;
        margin-top: 10px;

        .video-img {
            min-width: 345rpx;
            max-width: 345rpx;
            min-height: 278rpx;
            max-height: 278rpx;
            border-radius: 16rpx 16rpx 0rpx 0rpx;
        }

        .imgbut {
            width: 72rpx;
            height: 72rpx;
            position: absolute;
            background: rgba($color: #000000, $alpha: 0.3);
            border-radius: 50%;
            top: 103rpx;
            left: 136rpx;

            .icon-but {
                font-size: 55rpx;
                color: #FFFFFF;
            }
        }

        .title {
            width: 305rpx;
            height: 75rpx;
            margin: 24rpx auto 0;
            font-weight: 500;
            font-size: 28rpx;
            color: #00001C;
            line-height: 38rpx;
            word-break: break-all;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .video-ures {
            width: 305rpx;
            height: 56rpx;
            margin: 14rpx auto 22rpx;
        }

        .video-user-name {
            font-weight: 400;
            font-size: 24rpx;
            color: #232426;
            word-break: break-all;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
        }

        .video-user-time {
            font-weight: 400;
            margin-top: 10rpx;
            font-size: 20rpx;
            color: #808080;
        }

        .video-modal {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            background: rgba($color: #000000, $alpha: 0.5);
            border-radius: 16rpx;
        }
    }
}

.icon-but-shenhe {
    font-size: 52rpx;
    color: #D6D6DC;
    height: 52rpx;
}

.icon-but-shehebutongg {
    font-size: 45rpx;
    color: #D6D6DC;
    height: 52rpx;
}

.statuc-name {
    color: #F0F0F1;
    font-size: 28rpx;
    font-weight: 400;
}


::v-deep .u-button--plain {
    background-color: rgba(0, 0, 0, 0) !important;
}

.content {
    width: 415rpx;
    margin: 0 auto;
    white-space: normal;
    word-wrap: break-word; /* 自动换行长单词 */
}

.addVideo {
    width: 96rpx;
    height: 96rpx;
    border-radius: 50%;
    background-color: #F15353;
    position: fixed;
    right: 16rpx;
    bottom: 215rpx;

    .ship-video {
        font-size: 38rpx;
        color: #FFFFFF;
        margin-top: 16rpx;
        // margin-bottom: 5rpx;
    }

    .addVideo-text {
        font-size: 22rpx;
        color: #FFFFFF;
    }
}
</style>