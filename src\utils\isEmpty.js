
// 检查字符串是否为空
const checkStringEmpty = (str) => {
  return !str
}
// 检查数值是否为空
const checkNumberEmpty = (num) => {
  return !num.toString()
}
// 检查对象是否为空
const checkObjectEmpty = (obj) => {
  return Object.keys(obj).length === 0
}
// 检查数组是否为空
const checkArrayEmpty = (arr) => {
  return arr.length === 0
}
// 各数据类型检查方法字典
const checkDataEmptyDict = {
  string: checkStringEmpty,
  number: checkNumberEmpty,
  Object: checkObjectEmpty,
  Array: checkArrayEmpty,
  function: () => false,
  undefined: () => true,
  boolean: (data) => !data,
  Null: () => true
}
/**
 * @description 检查数据是否为空，为空返回true，不为空返回false
 * @param {any} data
 * @return {Boolean}
 * @example isEmpty({}) ⇒ true
*/
const isEmpty = (data) => {
  let type = typeof(data)
  // 基础数据类型
  if (type !== 'object') return checkDataEmptyDict[type](data)
  // 引用数据类型
  type = Object.prototype.toString.call(data).replace(/^\[object (\S+)\]$/,'$1')
  return checkDataEmptyDict[type](data)
}

export default {
	isEmpty
}