<!-- 申请供应商 -->
<template>
  <view>
    <apply-head :applyStatus="status" :isApply="isApply"></apply-head>
    <view class="audit d-c" v-if="status === 0 && isApply">
      <image
        src="https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/uni-supply-platform/audit.png"
      ></image>
    </view>
    <view class="audit d-c" v-if="status === 1 && isApply">
      <image
        src="https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/uni-supply-platform/pass.png"
      ></image>
    </view>
    <view class="audit d-c" v-if="status === -1 && isApply">
      <image
        src="https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/uni-supply-platform/auditFailure.png"
      ></image>
    </view>
    <view
      class="steps_form"
      :class="status === 0 && isApply === 0 ? '' : 'stepsDiv'"
      v-show="status === 0 && isApply === 0"
    >
      <u--form :model="form" ref="supplierForm" :rules="rules">
        <view class="headImg">
          <u-form-item
            label="上传头像"
            :required="true"
            prop="avatar"
            labelWidth="180rpx"
            style="margin: 0 26rpx 30rpx 26rpx; flex-direction: column"
            borderBottom
          >
            <u-upload
              style="width: 140rpx; height: 140rpx"
              :fileList="supplierImg"
              @afterRead="afterRead"
              @delete="deletePic"
              name="1"
              :maxCount="1"
            ></u-upload>
          </u-form-item>
        </view>
        <view class="headImg">
          <u-form-item
            label="营业执照"
            :required="true"
            prop="license_link"
            labelWidth="180rpx"
            style="margin: 0 26rpx 30rpx 26rpx; flex-direction: column"
            borderBottom
          >
            <u-upload
              width="340rpx"
  	          height="200rpx"
              @afterRead="licenseLink"
              name="1"
              :maxCount="1"
            >
            <view style="width: 340rpx;height: 200rpx;background-color: rgb(244, 245, 247);display: flex;align-items: center;justify-content: center;">
              <u-image v-if="form.license_link" mode="scaleToFill" :src="form.license_link" width="340rpx" height="200rpx"></u-image>
              <u-icon v-else name="plus"></u-icon>
            </view>
            
          </u-upload>
          </u-form-item>
        </view>
        <view class="headImg">
          <u-form-item
            label="身份证正面"
            :required="true"
            prop="license_link"
            labelWidth="180rpx"
            style="margin: 0 26rpx 30rpx 26rpx; flex-direction: column"
            borderBottom
          >
            <u-upload
              width="340rpx"
  	          height="200rpx"
              @afterRead="idCardLink"
              name="1"
              :maxCount="1"
            >
            <view style="width: 340rpx;height: 200rpx;background-color: rgb(244, 245, 247);display: flex;align-items: center;justify-content: center;">
              <u-image v-if="form.id_card_link" mode="scaleToFill" :src="form.id_card_link" width="340rpx" height="200rpx"></u-image>
              <u-icon v-else name="plus"></u-icon>
            </view>
            
          </u-upload>
          </u-form-item>
        </view>
        <view class="headImg">
          <u-form-item
            label="身份证反面"
            :required="true"
            prop="license_link"
            labelWidth="180rpx"
            style="margin: 0 26rpx 30rpx 26rpx; flex-direction: column"
            borderBottom
          >
            <u-upload
              width="340rpx"
  	          height="200rpx"
              @afterRead="idCardBackLink"
              name="1"
              :maxCount="1"
            >
            <view style="width: 340rpx;height: 200rpx;background-color: rgb(244, 245, 247);display: flex;align-items: center;justify-content: center;">
              <u-image v-if="form.id_card_back_link" mode="scaleToFill" :src="form.id_card_back_link" width="340rpx" height="200rpx"></u-image>
              <u-icon v-else name="plus"></u-icon>
            </view>
          </u-upload>
          </u-form-item>
        </view>
        <u-form-item
          borderBottom
          label="供应商名称"
          labelWidth="180rpx"
          prop="name"
          :required="true"
          style="margin: 0 26rpx 30rpx 26rpx"
        >
          <u--input
            border="none"
            placeholderStyle="text-align:right"
            placeholder="请输入供应商名称"
            v-model="form.name"
          ></u--input>
        </u-form-item>
        <u-form-item
          label="姓名"
          labelWidth="180rpx"
          prop="realname"
          borderBottom
          :required="true"
          style="margin: 0 26rpx 30rpx 26rpx"
        >
          <u--input
            placeholderStyle="text-align:right"
            placeholder="请输入您的姓名"
            border="none"
            v-model="form.realname"
          ></u--input>
        </u-form-item>
        <u-form-item
          label="联系电话"
          labelWidth="180rpx"
          prop="mobile"
          borderBottom
          :required="true"
          style="margin: 0 26rpx 30rpx 26rpx"
        >
          <u--input
            placeholderStyle="text-align:right"
            placeholder="请输入您的联系电话"
            border="none"
            style="text-align: right"
            v-model="form.mobile"
          ></u--input>
        </u-form-item>
        <view class="fromClassify">
          <u-form-item
            label="分类"
            labelWidth="180rpx"
            prop="category_id"
            borderBottom
            :required="true"
            style="margin: 0 26rpx 30rpx 26rpx"
          >
            <view class="d-cf">
              <text class="c-71 font_size14" @click="classify">
                {{ classifyName || '请选择' }}
              </text>
              <text class="iconfont icon-member_right"></text>
            </view>
          </u-form-item>
        </view>
        <u-form-item
          label="账号"
          labelWidth="180rpx"
          prop="username"
          borderBottom
          :required="true"
          style="margin: 0 26rpx 30rpx 26rpx"
        >
          <u--input
            placeholderStyle="text-align:right"
            placeholder="请输入您的账号"
            border="none"
            v-model="form.username"
          ></u--input>
        </u-form-item>
        <u-form-item
          label="密码"
          labelWidth="180rpx"
          prop="password"
          :required="true"
          style="margin: 0 26rpx 30rpx 26rpx"
        >
          <u--input
            placeholderStyle="text-align:right"
            placeholder="请输入您的密码"
            border="none"
            v-model="form.password"
          ></u--input>
        </u-form-item>
      </u--form>
    </view>

    <view
      class="supplierBtn d-cc"
      v-if="status === 0 && isApply === 0"
      @click="$clicks(supplierApplay)"
    >
      提交
    </view>
    <view class="mb30"></view>

    <u-popup
      :show="agreementShow"
      @close="close('agreement')"
      mode="center"
      :round="20"
    >
      <view class="main">
        <view class="header d-c">
          <view class="title">申请协议</view>
        </view>
        <view class="content" v-html="agreement"></view>
        <view class="agreement-btn" @click="close('agreement')">确定</view>
      </view>
    </u-popup>

    <u-popup :show="show" :round="10" mode="bottom" @close="close('supplier')">
      <view class="classify">
        <view class="classify_title d-bf">
          <view class="c-b5 font_size13" @click="close('supplier')">取消</view>
          <view class="c-20 font_size17">选择分类</view>
          <view
            class="iconfont icon-close11 title_close"
            @click="close('supplier')"
          ></view>
        </view>
        <view class="classify_content">
          <u-radio-group
            placement="column"
            iconPlacement="right"
            v-model="form.category_id"
          >
            <u-radio
              size="30"
              activeColor="#f15353"
              labelSize="28rpx"
              labelColor="#202020"
              v-for="(item, index) in supplierList"
              :key="index"
              :label="item.name"
              :name="item.id"
              @change="checkboxChange"
            ></u-radio>
          </u-radio-group>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import applyHead from '@/packageB/components/applyHead.vue'
export default {
  components: {
    applyHead,
  },
  data() {
    return {
      show: false,
      agreementShow: false,
      status: 0, // 申请状态
      isApply: 0, // 是否冻结
      freeze: 0, // 是否有申请记录
      agreement: '',
      src: '@/static/image/auditCheck.png',
      classifyName: '', // 分类选择名称
      form: {
        avatar: '',
        realname: '',
        name: '',
        status: 2,
        category_id: null,
        mobile: '',
        username: '',
        password: '',
        license_link:"", //营业执照
        id_card_link:"", // 身份证正面
        id_card_back_link:"", // 身份证反面
      },
      supplierList: [],
      supplierImg: [], // 上传图片数组
      rules: {
        avatar: [
          {
            required: true,
            message: '请上传图片',
            trigger: ['blur', 'change'],
          },
        ],
        license_link: [
          {
            required: true,
            message: '请上传营业执照图片',
            trigger: ['blur', 'change'],
          },
        ],
        id_card_link: [
          {
            required: true,
            message: '请上传身份证正面图片',
            trigger: ['blur', 'change'],
          },
        ],
        id_card_back_link: [
          {
            required: true,
            message: '请上传身份证反面图片',
            trigger: ['blur', 'change'],
          },
        ],
        name: [
          {
            required: true,
            message: '请输入供应商名称',
            trigger: ['blur'],
          },
          {
            required: true,
            min: 2,
            max: 20,
            message: '长度在2-20个字符之间',
            trigger: ['blur'],
          },
        ],
        realname: [
          {
            required: true,
            message: '请输入您的姓名',
            trigger: ['blur'],
          },
          {
            required: true,
            min: 2,
            max: 20,
            message: '长度在2-20个字符之间',
            trigger: ['blur'],
          },
        ],
        mobile: [
          {
            required: true,
            message: '请输入您的联系电话',
            trigger: ['blur'],
          },
          {
            // 自定义验证函数，见上说明
            validator: (rule, value, callback) => {
              // 上面有说，返回true表示校验通过，返回false表示不通过
              // uni.$u.test.mobile()就是返回true或者false的
              this.userShow = uni.$u.test.mobile(value)
              return uni.$u.test.mobile(value)
            },
            message: '手机号码不正确',
            // 触发器可以同时用blur和change
            trigger: ['blur'],
          },
        ],
        category_id: [
          {
            type: 'number',
            required: true,
            message: '请选择你的分类',
            trigger: ['blur', 'change'],
          },
        ],
        username: [
          {
            required: true,
            message: '请输入您的账号',
            trigger: ['blur'],
          },
          {
            required: true,
            min: 6,
            max: 20,
            message: '长度在6-20个字符之间',
            trigger: ['blur'],
          },
        ],
        password: [
          {
            required: true,
            message: '请输入您的密码',
            trigger: ['blur'],
          },
          {
            required: true,
            min: 6,
            max: 20,
            message: '长度在6-20个字符之间',
            trigger: ['blur'],
          },
        ],
      },
    }
  },
  onReady() {
    this.$refs.supplierForm.setRules(this.rules)
    this.supplierStatus()
  },
  onShow() {
    this.findSetting()
    this.supplierCategoryList()
  },
  methods: {
    // 新增图片
    async afterRead(event) {
      // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
      const lists = [].concat(event.file)
      let fileListLen = this.supplierImg.length
      lists.map(item => {
        this.supplierImg.push({
          ...item,
          status: 'uploading',
          message: '上传中',
        })
      })
      for (let i = 0; i < lists.length; i++) {
        const result = await this.uploadFilePromise(lists[i].url)
        const item = this.supplierImg[fileListLen]
        this.supplierImg.splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: 'success',
            message: '',
            url: result,
          }),
        )
        fileListLen++
      }
    },
    async licenseLink(e){
      const res = await this.newUploadFilePromise(e.file.url)
      if(res.file && res.file.url){
        this.form.license_link = res.file.url
        this.$refs.supplierForm.validateField('license_link')
      }
    },
    async idCardLink(e){
      const res = await this.newUploadFilePromise(e.file.url)
      if(res.file && res.file.url){
        this.form.id_card_link = res.file.url
        this.$refs.supplierForm.validateField('id_card_link')
      }
    },
    async idCardBackLink(e){
      const res = await this.newUploadFilePromise(e.file.url)
      if(res.file && res.file.url){
        this.form.id_card_back_link = res.file.url
        this.$refs.supplierForm.validateField('id_card_back_link')
      }
    },
    // 上传图片
    newUploadFilePromise(url) { //上传图片
      return new Promise((resolve, reject) => {
        let a = uni.uploadFile({
          url: this.api.host + '/api/common/upload',
          filePath: url,
          name:'file',
          formData: {
            file: url
          },
          success: (res) => {
            let json = JSON.parse(res.data)
            resolve(json.data);
          }
        });
      })
    },
    deletePic(event) {
      this.supplierImg.splice(event.index, 1)
      this.form.avatar = ''
      this.$refs.supplierForm.validateField('avatar') // 重新校验一次
    },
    uploadFilePromise(url) {
      return new Promise((resolve, reject) => {
        const a = uni.uploadFile({
          url: this.api.host + '/api/common/upload',
          filePath: url,
          name: 'file',
          success: res => {
            setTimeout(() => {
              resolve(res.data.data)
              const data = JSON.parse(res.data)

              // for(let i in this.supplierImg) {
              // 	this.supplierImg[i].url = data.data.file.url;
              // }
              this.form.avatar = data.data.file.url
              this.$refs.supplierForm.validateField('avatar') // 重新校验一次
            }, 1000)
          },
        })
      })
    },
    checkboxChange(id) {
      this.form.category_id = id
      this.$refs.supplierForm.validateField('category_id') // 重新校验一次
      for (const i in this.supplierList) {
        if (this.supplierList[i].id === id) {
          this.classifyName = this.supplierList[i].name
        }
      }
      this.show = false
    },
    classify() {
      this.show = true
    },
    close(type) {
      if (type === 'agreement') {
        this.agreementShow = false
      }
      if (type === 'supplier') {
        this.show = false
      }
    },
    agencyOpen() {
      this.agencyShow = true
    },
    supplierStatus() {
      this.get('/api/supplier/getSupplierApplyStatus', {}, true, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            this.status = data.restatus
            this.isApply = data.isApply
            if (this.status == 0 && this.isApply == 0) {
              this.agreementShow = true
            }
            if (data.restatus === -1) {
              this.toast('您申请的代理被驳回，请重新申请')
            }
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    supplierCategoryList() {
      this.get('/api/supplier/getSupplierCategoryList', {}, true, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            this.supplierList = data.list
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    findSetting() {
      this.get('/api/supplier/findSetting', {}, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            this.agreement = data.resupplierSetting.value.agreement
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    supplierApplay() {
      this.$refs.supplierForm
        .validate()
        .then(res => {
          this.post('/api/supplier/createSupplierApply', this.form, true, true)
            .then(res => {
              if (res.code === 0) {
                const data = res.data
                this.toast(res.msg)
                this.$refs.supplierForm.resetFields()
                this.supplierStatus()
              } else {
                this.toast(res.msg)
                // this.$refs.supplierForm.resetFields();
              }
            })
            .catch(Error => {
              console.log(Error)
            })
        })
        .catch(errors => {
          uni.$u.toast('填写错误')
        })
    },
  },
}
</script>
<style scoped>
[v-cloak] {
  display: none !important;
}
.headImg ::v-deep .u-form-item__body {
  flex-direction: column !important;
}
/*#ifdef MP-WEIXIN */
.steps_form ::v-deep .u-form-item__body {
  /* margin:0 26rpx 20rpx 26rpx; */
  padding: 20rpx 26rpx 30rpx 26rpx;
}
/*#endif*/
.headImg ::v-deep .u-form-item__body__right__content {
  margin-top: 30rpx;
}
.steps_form .fromClassify ::v-deep .u-form-item__body__right__content__slot {
  justify-content: flex-end;
  text-align: right;
  flex-direction: row;
}
.steps_form ::v-deep .u-upload__wrap__preview__image {
  width: 140rpx !important;
  height: 140rpx !important;
}
.steps_form ::v-deep .u-upload__button {
  width: 140rpx !important;
  height: 140rpx !important;
}
.steps_form ::v-deep .u-form-item__body__right__message {
  text-align: right;
}
.steps_form
  ::v-deep
  .u-input__content__field-wrapper
  .u-input__content__field-wrapper__field {
  text-align: right !important;
}
.classify_content ::v-deep .u-radio {
  margin-bottom: 53rpx;
}
.content ::v-deep p img {
  width: 100%;
  height: 100%;
  display: block;
}
</style>
<style lang="scss" scoped>
.audit {
  box-sizing: border-box;
  margin: 20rpx 20rpx 0 20rpx;
  background-color: #fff;
}
.stepsDiv {
  display: none;
}
.steps_form {
  margin: 20rpx;
  padding-bottom: 1rpx;
  background-color: #fff;
  border-radius: 10rpx;
  /*#ifdef MP-WEIXIN*/
  .headImg {
    margin: 0 26rpx 0rpx 26rpx;
  }
  /*#endif*/
  .supplier {
    width: 140rpx;
    height: 140rpx;
    border-radius: 10rpx;
    border: solid 2rpx #a5a5a5;
  }
}
.supplierBtn {
  width: 650rpx;
  height: 70rpx;
  position: relative;
  background-color: #f14e4e;
  border-radius: 6rpx;
  margin: 70rpx auto 0rpx auto;
  color: #fff;
}

.main {
  width: 640rpx;
  // height: 1024rpx;
  border-radius: 20rpx;
  margin: 0 auto;
  background: #fff;
  .header {
    width: 100%;
    height: 80rpx;
    line-height: 80rpx;
    position: relative;
    .title_close {
      position: absolute;
      right: 20rpx;
    }
  }
  .content {
    margin: 0 34rpx 0 24rpx;
    height: 768rpx;
    overflow-y: scroll;
  }
  .agreement-btn {
    width: 408rpx;
    height: 74rpx;
    background-color: rgb(240, 77, 77);
    border-radius: 10rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgb(255, 255, 255);
    font-size: 40rpx;
    margin: 48rpx auto 34rpx;
  }
}

/*弹出框样式*/
.classify {
  overflow: scroll;
  padding: 40rpx 30rpx 30rpx 30rpx;
  height: 50vh;
  .classify_content {
    margin-top: 50rpx;
  }
}
</style>
