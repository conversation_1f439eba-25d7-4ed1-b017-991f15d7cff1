<template>
  <view>
    <!-- 搜索 -->
    <view class="f d-bf search_box pl-25">
      <view class="head_select">
        <uni-data-select v-model="value" :localdata="liveOptions" :clear="false"
          @change="changeSelect"></uni-data-select>
      </view>
      <view class="search">
        <u-search clearabled searchIconSize="40" placeholder="搜索核销员" :showAction="false" height="64"
          v-model="search"></u-search>
      </view>
      <view class="search_btn pr-45">
        <u-button shape="circle" type="error" size="small" @click="searchVerificationer">
          搜索
        </u-button>
      </view>
    </view>
    
    <scroll-view class="scroll-Y" scroll-y="true" @scrolltolower="scrolltolower">
      <template v-for="item in verificationerList">
        <view class="verificationer-card pl-20 pr-20 pb-30" :key="item.id">
          <view class="f fac fjsb" style="padding-bottom: 25rpx;border-bottom: 1px solid #F5F5F5;">
            <view class="f fac">
              <view class="verificationer-img">
                <u-image :src="item.user.avatar" radius="28rpx" width="56rpx" height="56rpx"></u-image>
              </view>
              <view class="verificationer-name">{{ item.user.nickname }}</view>
              <view class="f fac fjsb verificationer-id">ID:{{ item.user.id }}</view>
            </view>
            <view class="f fac">
              <view class="text">启动</view>
              <u-switch size='37' style="height: 40rpx; width: 72rpx;" v-model="item.status" activeColor="#f56c6c"
                :activeValue="1" :inactiveValue="2" @change="statusVerification(item)"></u-switch>
            </view>
          </view>
          <view style="margin-top: 25rpx;">
            <view class="f fac message">
              <view class="message-name">姓名</view>
              <view class="message-num">{{ item.name }}</view>
            </view>
            <view class="f fac message">
              <view class="message-name">电话</view>
              <view class="message-num">{{ item.tel }}</view>
            </view>
            <view class="f fac">
              <view class="message-name">累计核销</view>
              <view class="message-num">{{ item.verification_number }}次</view>
            </view>
          </view>
          <view class="f fac fjsb pt-20">
            <view></view>
            <view class="f fac">
              <view class="button" style="margin-right: 16rpx;" @click="upDataPopup(item)">编辑</view>
              <view class="button" @click="deleteVerification(item)">删除</view>
            </view>
          </view>
        </view>
      </template>
      <view style="height:170rpx"></view>
    </scroll-view>
    <view class="but-red">
      <u-button text="+ 添加核销员" @click="submit" type="error" shape="circle"></u-button>
    </view>

    <u-popup :round="30" :show="newsShow" @close="close">
      <view class="verifyNews-box">
        <view class="f fjsb fac verifyNews-title">
          <view class="title">{{ popupStater }}核销员</view>
          <view class="iconfont icon-close11" @click="close"></view>
        </view>
        <view class="con-verifyNews">
          <u--form :model="fromData" :rules="rules" ref="fromData">
            <view :class="[fromData.user !== ''?'con-verifyNews-from3':'con-verifyNews-from']">
              <view class="f fac pt_10">
                <u-form-item label="核销员" labelWidth="210rpx" :required="true" prop="name" ref="name">
                    <u--input v-model="fromData.user" @change="userChange" placeholder="请输入核销员会员手机号"
                      border="none"></u--input>
                </u-form-item>
              </view>
              <view v-if="user_name !== '' && fromData.user !== ''" style="font-size: 22rpx;color: #F15353;">会员: {{
                user_name }}</view>
              <view v-else-if="user_name === '' && fromData.user !== ''" style="font-size: 22rpx;color: #F15353;">未找到会员
              </view>
              <!-- <view v-else></view> -->
            </view>
            <view class="xian"></view>
            <view class="f fac con-verifyNews-from">
              <u-form-item label="姓名" labelWidth="210rpx" prop="name" ref="name">
                <u--input v-model="fromData.name" placeholder="请输入姓名" border="none"></u--input>
              </u-form-item>
            </view>
            <view class="xian"></view>
            <view class="f fac con-verifyNews-from">
              <u-form-item label="手机号" labelWidth="210rpx" prop="tel" ref="tel">
                <u--input v-model="fromData.tel" placeholder="请输入手机号" border="none"></u--input>
              </u-form-item>
            </view>
            <view class="xian"></view>
            <view class="f fac con-verifyNews-from1">
              <u-form-item label="状态" labelWidth="210rpx" prop="name" ref="name">
                <view class="f fac right-switch">
                  <u-switch size='37' style="height: 40rpx; width: 72rpx;" v-model="fromData.status" inactiveColor="#d6d6dc" activeColor="#f56c6c"
                    :activeValue="1" :inactiveValue="2"></u-switch>
                </view>
              </u-form-item>
            </view>
          </u--form>
        </view>
        <view class="popup-button">
          <u-button customStyle="height: 80rpx;font-size:30rpx" type="error" size="small" shape="circle" text="确认" @click="popupfun"></u-button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  name: 'verificationerManage',

  data() {
    return {
      store_id: null, // 门店id
      page: 1,
      pageSize: 10,
      total: null,
      user_name: '', // 会员名字
      user_id: null, // 会员id
      id: null, // 编辑确认时的id

      self: 0,
      newsShow: false,

      search: "",
      verificationerList: [], // 核销员表

      value: 'user_id',
      // 选择框
      liveOptions: [
        { value: 'user_id', text: '会员ID' },
        { value: 'tel', text: '手机号' },
        { value: 'nickname', text: '会员昵称' },
        { value: 'name', text: '姓名' },
      ],

      popupStater: '',
      fromData: {
        status: 2,
        name: '',
        tel: '',
        user: ''
      },
      rules: {}
    }
  },

  mounted() { },
  onLoad(e) {
    console.log(e);
    if (e.store_id) {
      this.store_id = parseInt(e.store_id)
    }
  },
  onShow() {
    this.verificationList()
  },
  async onReachBottom() {

  },
  methods: {
    async scrolltolower(e) {
      if (this.verificationerList.length < this.total) {
        this.page = ++this.page
        let params = {
          store_id: this.store_id,
          page: this.page,
          pageSize: this.pageSize,
        }
        params[this.value] = this.search
        let res = await this.get('/api/localLife/front/verification/list', params)
        if (res.code === 0) {
          this.verificationerList = [...this.verificationerList,...res.data.list];
          this.total = res.data.total;
        }
      }
    },
    // 获取核销员列表
    async verificationList() {
      let params = {
        store_id: this.store_id,
        page: 1,
        pageSize: 10
      }
      let res = await this.get('/api/localLife/front/verification/list', params)
      if (res.code === 0) {
        this.verificationerList = res.data.list;
        this.total = res.data.total;
      }

    },
    // 搜索
    async searchVerificationer() {
      this.page = 1;
      this.pageSize = 10;
      let params = {
        store_id: this.store_id,
        page: 1,
        pageSize: 10,
      }
      params[this.value] = this.search
      let res = await this.get('/api/localLife/front/verification/list', params);
      if (res.code === 0) {
        this.verificationerList = res.data.list;
        this.total = res.data.total;
      }
    },
    // 选择框方法
    changeSelect(e) {
      console.log('选择选项：', e)
    },
    // 打开增加弹窗
    submit() {
      this.newsShow = true;
      this.popupStater = '新增';
    },
    // 关闭增加弹窗
    close() {
      this.newsShow = false;
      this.fromData = {
        status: 2,
        name: '',
        tel: '',
        user: ''
      }
    },
    // 确认弹窗按钮
    popupfun() {

      if (this.popupStater === '新增') {
        this.add()
      } else {
        this.upData()
      }
    },
    // 确认更新
    async upData() {
      let params = {
        id: this.id,
        store_id: parseInt(this.store_id),
        user_id: this.user_id ? parseInt(this.user_id) : '',
        tel: this.fromData.tel,
        status: parseInt(this.fromData.status),
        name: this.fromData.name
      }
      let res = await this.put('/api/localLife/front/verification/update', params, false, true, false);
      if (res.code === 0) {
        this.close()
        this.verificationList()
      }
    },
    // 增加核销员
    async add() {
      if (this.user_id === null) {
        this.toast('请填写核销员会员手机号')
        return
      } 
      let params = {
        store_id: parseInt(this.store_id),
        user_id: parseInt(this.user_id),
        tel: this.fromData.tel,
        status: parseInt(this.fromData.status),
        name: this.fromData.name
      }
      let res = await this.post('/api/localLife/front/verification/create', params)
      if (res.code === 0) {
        this.close()
        this.verificationList()
      }
    },
    // 删除核销员
    async deleteVerification(item) {
      let params = {
        id: item.id
      }
      let res = await this.delete('/api/localLife/front/verification/delete', params, true)
      if (res.code === 0) {
        this.toast(res.msg)
        this.verificationList()
      }
    },
    // 更新启用状态
    async statusVerification(item) {
      let params = {
        ids: [item.id],
        value: item.status
      }
      let res = await this.post('/api/localLife/front/verification/statusVerification', params);
      if (res.code === 0) {
        this.toast(res.msg)
      }
    },
    // 打开编辑popup
    upDataPopup(item) {
      this.popupStater = '编辑'
      this.newsShow = true;
      this.fromData = {
        status: item.status,
        name: item.name,
        tel: item.tel,
        user: item.user.username
      }
      this.id = item.id
      this.user_name = item.user.nickname
      this.user_id = item.user.id
    },
    // 搜索会员
    async userChange() {
      let params = {
        username: this.fromData.user,
      }
      this.user_name = '';
      this.user_id = '';
      let res = await this.get('/api/localLife/front/getUserByUserName', params, false, false, false)
      if (res.code === 0) {
        this.user_name = res.data.nickname;
        this.user_id = res.data.id
      }
    }
  },
}
</script>
<style scoped lang="scss">
::v-deep .uni-stat__select {
  padding: 0;
}

::v-deep .uni-select {
  border: none;
}

::v-deep .uni-select__input-box {
  padding: 0;
  width: 162rpx;
  height: 64rpx;
}

::v-deep .uni-select__selector {
  width: 230rpx;
}

::v-deep .u-col {
  margin-right: 26rpx;
  align-self: flex-end;
}

.search_box {
  width: 750rpx;
  height: 96rpx;
  background-color: #ffffff;

  .search {
    width: 400rpx;
  }

  .search_btn {
    width: 110rpx;
    height: 64rpx;
  }
}

.verificationer-card {
  width: 702rpx;
  margin: 20rpx auto;
  padding-top: 20rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  box-sizing: border-box;

  .verificationer-img {
    margin-right: 16rpx;
  }

  .verificationer-name {
    margin-right: 11rpx;
    font-weight: bold;
    line-height: 38rpx;
    font-size: 30rpx;
  }

  .verificationer-id {
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    border: 1px solid #AAAAB3;
    color: #6E6E79;
    height: 32rpx;
    padding: 0 9rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.text {
  margin-right: 8rpx;
  font-size: 24rpx;
  color: #6E6E79;
}

.message {
  margin-bottom: 40rpx;
  font-size: 26rpx;
}

.message-name {
  width: 130rpx;
  color: #6E6E79;
  font-weight: 400;
}

.message-num {
  color: #00001C;
  font-weight: 400;
}

.button {
  width: 140rpx;
  height: 64rpx;
  border-radius: 32rpx 32rpx 32rpx 32rpx;
  border: 1rpx solid #D6D6DC;
  font-size: 26rpx;
  font-weight: 400;
  color: #00001C;
  display: flex;
  justify-content: center;
  align-items: center;
}

.but-red {
  width: 100%;
  height: 80rpx;
  padding: 24rpx;
  box-sizing: border-box;
  position: fixed;
  bottom: 52rpx;
}

.con-verifyNews {
  width: 702rpx;
  height: 441rpx;
  margin: 0 auto;
  background-color: #ffffff;
  border-radius: 16rpx 16rpx 16rpx 16rpx;

  .xian {
    width: 654rpx;
    height: 1px;
    margin: 0 auto;
    background-color: #F0F0F1;
  }

  .con-verifyNews-from {
    height: 104rpx;
    padding: 0 25rpx;
  }

  .con-verifyNews-from3 {
    height: 130rpx;
    padding: 0 25rpx 10rpx 25rpx;
  }

  .con-verifyNews-from1 {
    height: 105rpx;
    padding: 0 25rpx;

    .right-switch {
      width: 460rpx;
      padding-right: 24rpx;
      box-sizing: border-box;
      justify-content: end;
    }
  }
}

.verifyNews-title {
  height: 116rpx;
  width: 702rpx;
  margin: 0 auto;

  .title {
    font-size: 32rpx;
    color: #00001C;
    font-weight: bold;
  }
}

.popup-button {
  width: 702rpx;
  margin: 50rpx auto 60rpx;
}

.scroll-Y {
  height: calc(100vh - 96rpx)
}
.verification-but{
  width: 100%;
  text-align: right;
}

.verifyNews-box {
  height: 740rpx;
  background-color:  #F5F5F5;
}
// .verification-but {
//   width: 702rpx;
//   justify-content: end;
// }
</style>
