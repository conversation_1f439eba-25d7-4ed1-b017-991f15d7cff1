<!-- 搜索 -->
<template>
  <view>
    <!-- 搜索框 -->
    <view class="d-bf pl-25 pr_20 pt_14 pb_14">
      <view class="d-cc">
        <view class="iconfont icon-member-left fs-0" @tap="back"></view>
        <view class="ml-15">
          <view>
            <view style="width: 680rpx">
              <u-search
                height="60"
                bgColor="#eff0f1"
                color="#666666"
                @custom="searchBt(boxValue, 'box')"
                @search="searchBt(boxValue, 'box')"
                v-model="boxValue"
                searchIconSize="40"
                :placeholder="defaultValue"
              ></u-search>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!--  -->
    <!-- 搜索记录 -->
    <view class="mr-20">
      <view>
        <view class="d-bf">
          <view class="fs-1-5 mt_28 ml-20">最近搜索</view>
          <view
            class="iconfont icon-fontclass-shanchu"
            style="font-size: 25rpx"
            @tap="cleanrecentList"
          ></view>
        </view>
        <view class="d-f flow">
          <view
            class="pr_20 pt_15 pb_15 mt_38 pl-20 ml-20 searchcs"
            v-for="(item, index) in recentList"
            :key="index"
            @tap="searchBt(item, 'lately')"
          >
            {{ item }}
          </view>
        </view>
      </view>
      <view class="mt_40">
        <view class="d-bf">
          <view class="fs-1-5 mt_28 ml-20">热门搜索</view>
        </view>
        <view class="d-f flow">
          <view
            class="pr_20 pt_15 pb_15 mt_38 pl-20 ml-20 searchcs"
            v-for="(item, index) in hotList"
            :key="index"
            @tap="searchBt(item, 'hot')"
          >
            {{ item }}
          </view>
        </view>
      </view>
    </view>
    <!--  -->
  </view>
</template>

<script>
export default {
  data() {
    return {
      supplierId: '',
      defaultValue: '请输入关键字', // searchBox的默认搜索值(由后端返回)
      boxValue: '', // searchBox搜索值
      latelyValue: '', // 最近搜索值
      hotValue: '', // 热门搜索值
      recentList: [],
      hotList: [],
    }
  },
  onLoad(options) {
    if ((options.supplier_id ?? '') !== '') {
      this.supplierId = options.supplier_id
    }
  },
  onShow() {
    this.recentList = [...uni.getStorageSync('recentList')]
    this.popularSearchHistory()
    this.getDefaultSearch()
  },
  methods: {
    searchBt(value, condition) {
      this.boxValue = ''
      this.latelyValue = ''
      this.hotValue = ''
      if (this.defaultValue && this.defaultValue !== '请输入关键字') {
        switch (condition) {
          case 'lately':
            this.latelyValue = value
            this.navToList(this.latelyValue)
            break
          case 'hot':
            this.hotValue = value
            this.navToList(this.hotValue)
            break
          case 'box':
            // 如果用户没有输入, 默认搜索this.defaultValue
            this.boxValue =
              this.boxValue === '' && !value ? this.defaultValue : value
            this.navToList(this.boxValue)
            break
          default:
            break
        }
      }
      if(value){
        this.navToList(value)
      }
      this.recentList.unshift(value)
      this.recentList = Array.from(new Set(this.recentList))
      uni.setStorageSync('recentList', this.recentList)
      // if (value === '' || value === null) {
      //   // 如果用户没有输入就默认搜索热门搜索第一条
      //   if (this.checkNull(this.hotList[0])) {
      //     value = this.hotList[0]
      //   }
      // }
    },
    navToList(val) {
      uni.navigateTo({
        url: `./searchResult/searchResult?value=${val}&supplierId=${this.supplierId}`,
      })
    },
    back() {
      uni.navigateBack({
        delta: 1, // 返回层数，2则上上页
      })
    },
    cleanrecentList() {
      // 清理最近搜索
      uni.setStorageSync('recentList', [])
      this.recentList = uni.getStorageSync('recentList')
    },
    popularSearchHistory() {
      // 获取热门搜索记录
      this.get('/api/home/<USER>', {}, true).then(data => {
        this.hotList = data.data.header.search.hot_words
      })
    },
    getDefaultSearch() {
      // 获取默认搜索词
      this.get('/api/home/<USER>', {}, true).then(data => {
        this.defaultValue =
          data.data.header.search.place_holder || '请输入关键字'
      })
    },
  },
}
</script>

<style scoped>
page {
  background-color: #ffffff;
}

.u-search__action[data-v-1a326067] {
  color: #3a3a3a;
}

.searchcs {
  background-color: #f2f2f2;
  font-size: 24rpx;
  color: #656565;
  border-radius: 30rpx;
}
</style>
