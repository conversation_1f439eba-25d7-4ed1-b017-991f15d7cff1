<template>
  <view :style="{ position: 'relative', backgroundColor: '#010101' }">
    <!-- 头部 -->
    <view
      class="f fjc top"
      :style="{ height: menuButtonInfo.bottom + 8 + 'px' }"
    >
      <view
        class="top-view"
        :style="{ top: menuButtonInfo.top - 8 + 'px', left: '0' }"
      >
        <view
          class="f fac"
          style="position: absolute; left: 20px; top: 8px; margin-top: 10rpx"
        >
          <text
            class="iconfont icon-member-left member"
            @click="toMember"
          ></text>
        </view>
        <view class="f fjc">
          <u-tabs
            :list="tabsList"
            @change="tabsChange"
            keyName="name"
            :current="tabCurrent"
            :inactiveStyle="{ color: '#FFFFFF' }"
            :activeStyle="{ color: '#FFFFFF', fontWeight: 'blod' }"
            lineColor="#FFFFFF"
          >
            <view slot="right" style="padding-left: 20rpx">
              <view
                class="iconfont icon-home-page"
                @tap="navTo('/pages/index/index')"
              ></view>
            </view>
          </u-tabs>
        </view>
      </view>
    </view>

    <u-empty
      :style="{
        width: '100%',
        height: windowHeight - tabbarHeight + 'px',
      }"
      v-if="list.length === 0 || !list"
      text="暂无小视频"
      mode="list"
      marginTop="100rpx"
      textSize="28rpx"
      iconSize="150"
    ></u-empty>
    <!-- 视频 -->
    <swiper
      v-else
      :style="{
        width: '100%',
        height: windowHeight - tabbarHeight + 'px',
        backgroundColor: '#FFFFFF',
      }"
      :vertical="true"
      circular
      @change="handleSwiperChange"
    >
      <template>
        <swiper-item v-for="(item, index) in videoList" :key="index">
          <view v-if="item.id">
            <video
              :id="index + ''"
              :style="{
                width: '100vw',
                height: windowHeight - tabbarHeight + 'px',
                backgroundColor: '#010101',
              }"
              :http-cache="true"
              :controls="false"
              :page-gesture="false"
              :show-fullscreen-btn="false"
              :show-loading="false"
              :show-center-play-btn="false"
              :enable-progress-gesture="false"
              :custom-cache="false"
              :src="item.video_url"
              :poster="item.cover_url"
              webkit-playsinline="true"
              playsinline
              x5-playsinline
              x5-video-player-type="h5-page"
              preload="auto"
              x5-video-player-fullscreen="false"
              x-webkit-airplay="true"
              :muted="muted"
              @click="pauseFn"
            ></video>
            <!-- 继续播放 -->
            <view class="play-view" v-if="status === 'pause'" @click="playFn(false)">
              <view class="iconfont icon-play"></view>
            </view>
            <!-- 左侧标题与商品部分 -->
            <view class="f1 left-view">
              <view class="c-white font_size18 font_w700">
                {{
                  item.user.nickname
                    ? item.user.nickname
                    : '用户' + item.user.username.slice(-4)
                }}
              </view>
              <view class="mt_21 c-white limit-text-2">{{ item.title }}</view>
              <view
                class="mt_21 goods-view main-space"
                @click.stop="
                  navTo(
                    '/packageA/commodity/commodity_details/commodity_details?id=' +
                      item.product.id,
                  )
                "
              >
                <u--image
                  :src="item.product.image_url"
                  radius="16rpx"
                  width="96rpx"
                  height="96rpx"
                ></u--image>
                <view class="c-white ml_15 goods-tp">
                  <view class="limit-text-1 title">
                    {{ item.product.title }}
                  </view>
                  <view class="f fac">
                    <view class="c-f14d4d font_w700">
                      <text class="font_size12">￥</text>
                      {{ toYuan(item.product.price) }}
                    </view>
                    <view class="c-c1 ml_15">
                      <text>原价:</text>
                      <span style="text-decoration: line-through" class="ml_10">
                        <text class="font_size12">￥</text>
                        <span>{{ toYuan(item.product.origin_price) }}</span>
                      </span>
                    </view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 右侧logo/点赞/转发 -->
            <view class="right-view ml_100">
              <u-avatar
                :src="item.user.avatar"
                size="80rpx"
                shape="circle"
                @click="goUserVideo(item.user.id)"
              ></u-avatar>
              <view class="item mt_42" @click="goAddVideo">
                <view
                  class="f fac fjc iconfont icon-fontclass-ship icon-fabu"
                ></view>
                <view>发布</view>
              </view>
              <view class="item mt_42" @click="likeClick(item, index)">
                <view
                  :style="isLike === 1 ? 'color: red;' : 'color:#ffffff'"
                  class="iconfont icon-bc_like"
                ></view>
                <view>{{ item.like_num }}</view>
              </view>
              <view class="item mt_30">
                <!-- #ifdef MP-WEIXIN -->
                <button
                  open-type="share"
                  class="clear-btn c-white font_size14"
                  style="line-height: 45rpx"
                >
                  <view class="iconfont icon-zb_all_share"></view>
                  <view>{{ item.forwarding_num }}</view>
                </button>
                <!-- #endif -->
                <!-- #ifdef APP-PLUS || H5 -->
                <button
                  class="clear-btn c-white font_size14"
                  style="line-height: 45rpx"
                  @click="sharefun()"
                >
                  <view class="iconfont icon-zb_all_share"></view>
                  <view>{{ item.forwarding_num }}</view>
                </button>
                <!-- #endif -->
              </view>
            </view>

            <!-- 遮罩层 -->
            <view class="shade-view" @click="shadefun"></view>
          </view>
        </swiper-item>
      </template>
    </swiper>
    <myTabBar ref="myTabBar"></myTabBar>
    <!-- 遮罩层 -->
    <shade></shade>

    <!-- 右上侧/我的 -->
    <view
      class="topRight-view"
      :style="{ top: menuButtonInfo.bottom + 10 + 'px' }"
    >
      <view class="item mt_42" @click="goMyVideo">
        <view
          class="f fac fjc iconfont icon-fontclass-rengezhongxin icon-fabu"
        ></view>
      </view>
    </view>
  </view>
</template>

<script>
import myTabBar from '../../../components/tabbar.vue'
import shade from '../../../common/shade/shade.vue'

export default {
  components: { myTabBar, shade },
  data() {
    return {
      menuButtonInfo: {
        top: 5,
        bottom: 45,
      },
      tabsList: [{ name: '推荐' }, { name: '最新' }],
      tabCurrent: 1,
      windowHeight: 0,
      tabbarHeight: 51,
      muted: true,
      status: 'pause', // 视频 play播放 pause暂停
      isLike: 0, // 点赞
      id: null,
      list: [], // 视频数组
      k: 0, // list下标
      videoList: [], // 展示数组
      displayIndex: 0, // 记录videoList下标
      total: 0,
      s: null, // 向上滑动还是向下滑动 1，向下 0，向上
      linkStatur: '', // 记录是否是链接进入

      isPaly: false // ios 进入不直接播放,需要手动点一下才能播放
    }
  },
  async onLoad(e) {
    this.windowHeight = uni.getSystemInfoSync().windowHeight
    if (e.id) {
      this.id = parseInt(e.id)
      let idRes = await this.post('/api/video/center/list', {
        page: 1,
        pageSize: 1,
        id: this.id,
      })
      if (idRes.code === 0) {
        this.list.push(idRes.data.list[0])
      }
    }
    let res = await this.post('/api/video/center/list', {
      page: 1,
      pageSize: 3,
    })
    if (res.code === 0) {
      this.total = res.data.total
      for (let i = 0; i < this.total; i++) {
        this.list.push(null)
      }
      res.data.list.forEach((item, index) => {
        if (this.id) {
          this.list[index + 1] = item
        } else {
          this.list[index] = item
        }
      })
      if (this.list.length > 3) {
        let res1 = await this.post('/api/video/center/list', {
          page: this.total,
          pageSize: 1,
        })
        if (res1.code === 0) {
          this.list[this.list.length - 1] = res1.data.list[0]
        }
        this.getVideoList()
      } else {
        this.videoList = this.list;
      }
    }
    // #ifdef MP-WEIXIN
    await this.playFirst() // 播放
    // #endif
    await this.gettabslist() // 获取是否显示推荐
    await this.getIsLike() // 获取是否打赞

    if (this.checkWenxin()) {
      // h5分享参数变动 微信浏览器分享
      let share = this.list[this.k]
      let arr = [
        { linkName: 'id', linkNum: share.id },
        { linkName: 'pause', linkNum: 'false' },
      ]
      this.setWeixinShare(share.title, '', share.cover_url, arr)
    }
    // #ifdef H5
    // pause == true 代表是从别的页面进入 没有则是直接点击链接进入此页面
    if (e.pause !== 'true' && e.id) {
      this.linkStatur = e.pause
      //  h5分享 分享次数加1 分享的视频会放在首位所以直接调用分享接口即可
      await this.linkfun()
    }
    // #endif
  },
  async onShow() {
    this.$nextTick(() => {
      this.$refs.myTabBar.tabBarActive = -1
    })
    let bottomHight = uni.getSystemInfoSync().safeAreaInsets.bottom
    if (
      uni.getSystemInfoSync().platform == 'ios' &&
      this.linkStatur !== 'true'
    ) {
      bottomHight = bottomHight ? bottomHight : 34
    }
    this.tabbarHeight = 51 + bottomHight
    // #ifdef MP-WEIXIN
    this.menuButtonInfo = uni.getMenuButtonBoundingClientRect()
    // #endif
  },
  async onReady() {
    // #ifdef H5
    this.muted = true
    await uni.createVideoContext(0 + '', this).pause()
    this.status = 'pause'
    setTimeout(() => {
      uni.createVideoContext(1 + '', this).pause()
      uni.createVideoContext(2 + '', this).pause()
    }, 500)
    // #endif

  },
  // 分享给朋友
  onShareAppMessage() {
    let that = this
    that.linkfun() // 分享成功时调用
    return {
      title: this.videoList[this.k].title,
      imageUrl: that.videoList[that.displayIndex].cover_url,
      path:
        '/packageE/marketingTool/miniVideo/miniVideo?id=' +
        this.videoList[that.displayIndex].id,
    }
  },
  // 分享朋友圈
  onShareTimeline() {
    let that = this
    that.linkfun()
    // 分享朋友圈
    return {
      title: this.videoList[this.k].title,
      imageUrl: that.videoList[that.displayIndex].cover_url,
      path:
        '/packageE/marketingTool/miniVideo/miniVideo?id=' +
        this.videoList[that.displayIndex].id,
    }
  },
  watch: {
    async displayIndex(k, old_k) {
      // 关闭视频
      uni.createVideoContext(old_k + '', this).pause()
      this.getIsLike()
    },
  },
  methods: {
    // <------------------跳转页面 ---------------------->
    // 去往我的
    toMember() {
      this.navBack()
      // this.tabTo('/pages/membercenter/membercenter')
    },
    // 去往作者视频
    goUserVideo(id) {
      this.navTo('/packageE/marketingTool/miniVideo/authorVideo?id=' + id)
    },
    // 去往我的视频
    goMyVideo() {
      this.navTo('/packageE/marketingTool/miniVideo/myVideo')
    },
    // 发布视频
    goAddVideo() {
      this.navTo('/packageE/marketingTool/miniVideo/addVideo')
    },

    // <------------------tabs处理 ---------------------->
    // 判断是否显示推荐
    async gettabslist() {
      let res = await this.post('/api/video/center/list', {
        page: 1,
        pageSize: 3,
        is_recommend: 1,
      })
      if (res.code === 0) {
        if (res.data.total === 0) {
          this.tabsList = [{ name: '最新' }]
          this.tabCurrent = 0
        }
      }
    },
    // 切换
    tabsChange(tab) {
      this.status = 'pause' // 视频 play播放 pause暂停
      this.isLike = 0 // 点赞
      this.id = null
      this.k = 0 // list下标
      this.videoList = [] // 展示数组
      this.displayIndex = 0 // 记录videoList下标
      this.total = 0
      this.s = null // 向上滑动还是向下滑动 1，向下 0，向上
      this.tabCurrent = tab.index
      this.tabsChangeVideoList()
    },
    // 切换tabs获取数据
    async tabsChangeVideoList() {
      let params = {
        page: 1,
        pageSize: 3,
      }
      if (this.tabsList[this.tabCurrent].name === '推荐') {
        params.is_recommend = 1
      }
      let res = await this.post('/api/video/center/list', params)
      if (res.code === 0) {
        this.total = res.data.total
        let list = []
        for (let i = 0; i < this.total; i++) {
          list.push(null)
        }
        res.data.list.forEach((item, index) => {
          list[index] = item
        })
        if (list.length > 3) {
          let params1 = {
            page: this.total,
            pageSize: 1,
          }
          if (this.tabsList[this.tabCurrent].name === '推荐') {
            params1.is_recommend = 1
          }
          let res1 = await this.post('/api/video/center/list', params1)
          if (res1.code === 0) {
            list[list.length - 1] = res1.data.list[0]
          }
          this.list = list
          this.getVideoList()
        } else {
          this.videoList = this.list
        }
      }
      await this.gettabslist() // 获取是否显示推荐
      await this.playFirst() // 播放
      await this.getIsLike() // 获取是否打赞
    },

    // <------------------视频上下滑动处理 ---------------------->
    // 获取视频中心视频
    async fetch(page) {
      let params = {
        page,
        pageSize: 1,
      }
      if (this.tabsList[this.tabCurrent].name === '推荐') {
        params.is_recommend = 1
      }
      let res = await this.post('/api/video/center/list', params)
      if (res.code === 0) {
        let list = res.data.list || []
        if (list.length === 0) {
          return
        }
        return list[0]
      }
    },
    // 处理展示数据用
    async getVideoList(k = this.k) {
      const videoListLength = this.list.length // 源数据长度
      const displayList = []
      // 向下滑动
      if (this.s === 1) {
        if (this.list[k + 1 == videoListLength ? 0 : k + 1] === null) {
          if (this.id) {
            this.list[k + 1 == videoListLength ? 0 : k + 1] = await this.fetch(
              k + 1,
            )
          } else {
            this.list[k + 1 == videoListLength ? 0 : k + 1] = await this.fetch(
              k + 2,
            )
          }
        }
      }
      // 向上滑动
      else if (this.s === 0) {
        if (this.list[k - 1 == -1 ? videoListLength - 1 : k - 1] === null) {
          if (this.id) {
            this.list[k - 1 == 0 ? videoListLength - 1 : k - 1] =
              await this.fetch(k - 1)
          } else {
            this.list[k - 1 == 0 ? videoListLength - 1 : k - 1] =
              await this.fetch(k)
          }
        }
      }

      displayList[this.displayIndex] = this.list[k]
      displayList[this.displayIndex - 1 == -1 ? 2 : this.displayIndex - 1] =
        this.list[k - 1 == -1 ? videoListLength - 1 : k - 1]
      displayList[this.displayIndex + 1 == 3 ? 0 : this.displayIndex + 1] =
        this.list[k + 1 == videoListLength ? 0 : k + 1]

      this.videoList = displayList

    },
    // swiper切换
    async handleSwiperChange(event) {
      // 视频长度为3或者小于3时，不更新数组
      const { current } = event.detail
      const videoListLength = this.list.length // 源数据长度
      // =============向下==========
      if (
        this.displayIndex - current == 2 ||
        this.displayIndex - current == -1
      ) {
        this.s = 1
        this.k = this.k + 1 == videoListLength ? 0 : this.k + 1
        this.displayIndex =
          this.displayIndex + 1 == 3 ? 0 : this.displayIndex + 1
      }
      // ======如果两者的差为-2或者1则是向上滑动============
      else if (
        this.displayIndex - current == -2 ||
        this.displayIndex - current == 1
      ) {
        this.s = 0
        this.k = this.k - 1 == -1 ? videoListLength - 1 : this.k - 1
        this.displayIndex =
          this.displayIndex - 1 == -1 ? 2 : this.displayIndex - 1
      }
      // 处理展示数据用
      await this.getVideoList(this.k)
      // 获取数据后播放
      if (uni.getSystemInfoSync().platform == 'ios' && this.isPaly == true) {
        await this.playFn(true)
      } else if (uni.getSystemInfoSync().platform != 'ios') {
        await this.playFn(true)
      } else {
        await this.pauseFn()
      }
      if (this.checkWenxin()) {
        // h5分享参数变动 微信浏览器分享
        let share = this.list[this.k]
        let arr = [
          { linkName: 'id', linkNum: share.id },
          { linkName: 'pause', linkNum: 'false' },
        ]
        this.setWeixinShare(share.title, '', share.cover_url, arr)
      }
    },

    // <------------------视频操作相关 ---------------------->
    // h5分享
    sharefun() {
      this.$store.commit('upShadeShow', true)
    },
    // 获取是否点赞
    async getIsLike() {
      let res = await this.post('/api/video/user/getLikeById', {
        id: parseInt(this.videoList[this.displayIndex].id),
      })
      if (res.code === 0) {
        this.isLike = res.data.is_like
      }
    },
    // 点赞或取消点赞
    async likeClick(item, index) {
      // 判断登录
      let user = uni.getStorageSync('user')
      if (!this.checkNull(user)) {
        this.$toast('未登录,3秒后跳至登录页', 3000)
        setTimeout(() => {
          this.$fn.navTo('/pages/login/login')
        }, 3000)
        return
      }
      let res
      // 已登录处理 新增/减少点赞数量
      switch (this.isLike) {
        case 1: // 已点赞
          // 取消点赞
          res = await this.post('/api/video/user/reduceLikeNum', {
            id: item.id,
          })
          this.isLike = res.code === 0 ? 0 : 1
          this.$set(this.videoList[index], 'like_num', item.like_num - 1)
          break
        case 0: // 未点赞
          // 点赞
          res = await this.post('/api/video/user/addLikeNum', { id: item.id })
          this.isLike = res.code === 0 ? 1 : 0
          this.$set(this.videoList[index], 'like_num', item.like_num + 1)
          break
      }
    },
    // 首次加载播放第一个视频
    playFirst() {
      uni.createVideoContext(this.displayIndex + '', this).play()
      this.status = 'play'
    },
    // 播放
    async playFn(clearTime = false) {
      if (clearTime) {
        uni.createVideoContext(this.displayIndex + '', this).seek(0)
      } else {
        this.isPaly = true
      }
      this.status = 'play'
      this.muted = false
      setTimeout(() => {
        uni.createVideoContext(this.displayIndex + '', this).play()
      }, 500)
    },
    // 暂停
    pauseFn() {
      this.muted = true
      this.status = 'pause'
      setTimeout(() => {
        uni.createVideoContext(this.displayIndex + '', this).pause()
      }, 500)
    },
    shadefun() {
      if (this.status === 'play') {
        this.pauseFn()
      } else {
        this.playFn(false)
      }
    },

    // <------------------ 分享相关 ---------------------->
    linkfun() {
      this.post('/api/video/addForwardingNum', {
        id: this.videoList[this.displayIndex].id,
      }).then(res => {
        if (res.code === 0) {
          console.log(this.videoList[this.displayIndex].id)
          this.$set(
            this.list[this.k],
            'forwarding_num',
            this.list[this.k].forwarding_num + 1,
          )
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.top {
  width: 100%;
  z-index: 10000;
  position: fixed;
  background-image: url('https://yxgyl.obs.cn-south-1.myhuaweicloud.com/2025225/b4158855a711d8e43cd219c9a3911f1d');
  background-repeat: no-repeat;
  /* 禁止图片重复 */
  background-size: 100% 100%;
  background-position: center center;
  /* 让图片居中显示 */

  .top-view {
    position: fixed;
    width: 100%;
  }
}

.icon-home-page {
  font-weight: bold;
  color: #ffffff;
  font-size: 44rpx;
}

.iconfont {
  font-size: 58rpx;
}

.member {
  bottom: 32rpx;
  left: 32rpx;
  font-size: 35rpx;
  color: #ffffff;
  padding-right: 30rpx;
}

.play-view {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;

  .icon-play {
    font-size: 92rpx;
  }
}

.left-view {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  position: absolute;
  padding-bottom: 20rpx;
  bottom: 0;
  left: 0;
  padding-left: 23rpx;
  z-index: 1000;
  width: calc(100% - 260rpx);

  .goods-view {
    display: flex;
    padding-top: 20rpx;
    padding-bottom: 20rpx;
    padding-left: 20rpx;
    border-radius: 20rpx;
    background-color: rgba($color: #000000, $alpha: 0.5);

    // 白色边框阴影 区分黑背景
    // box-shadow: 0px 0px 5rpx rgba($color: #FFFFFF, $alpha: 0.5);
    .goods-tp {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
    }
  }
}

.right-view {
  width: 80rpx;
  padding-right: 24rpx;
  position: absolute;
  padding-bottom: 20rpx;
  bottom: 0;
  right: 0;
  z-index: 1000;

  .item {
    color: #ffffff;
    text-align: center;

    .iconfont {
      font-size: 58rpx;
    }
  }
}

.shade-view {
  width: 100%;
  height: 370rpx;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-image: url('https://yxgyl.obs.cn-south-1.myhuaweicloud.com/2025225/94497541c987415435678370e3408f45');
  background-repeat: no-repeat;
  /* 禁止图片重复 */
  background-size: 100% 100%;
  background-position: center center;
  /* 让图片居中显示 */
}

.topRight-view {
  width: 80rpx;
  padding-right: 24rpx;
  position: absolute;
  padding-bottom: 20rpx;
  // top: 170rpx;
  right: 0;

  .item {
    color: #ffffff;
    text-align: center;

    .iconfont {
      font-size: 58rpx;
    }

    .icon-fabu {
      // background: #F15353;
      background: rgba($color: #000000, $alpha: 0.3);
      height: 80rpx;
      width: 80rpx;
      border-radius: 50%;
      color: #ffffff;
      font-size: 34rpx;
    }
  }
}

.clear-btn {
  border-radius: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
  padding: unset;
  margin: unset;

  &::after {
    border: unset;
  }
}

.icon-fabu {
  background: #f15353;
  height: 80rpx;
  width: 80rpx;
  border-radius: 50%;
  color: #ffffff;
  font-size: 32rpx;
}

.title {
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.limit-text-2 {
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
