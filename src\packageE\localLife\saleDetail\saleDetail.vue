<template>
  <view>
    <!-- 搜索框 -->
    <view class="search-box">
      <view>
        <view class="f fac pl-25 pr-25 pt-25">
          <view>
            <u-input
              clearable
              placeholder="手机号"
              v-model="user_mobile"
              customStyle="height: 40rpx;"
            ></u-input>
          </view>
          <view class="ml-20">
            <u-input
              clearable
              placeholder="订单编号"
              v-model="order_sn"
              customStyle="height: 40rpx;"
            ></u-input>
          </view>
        </view>
        <view class="mt-30 pl-25 pr-25">
          <u-button
            type="error"
            shape="circle"
            customStyle="height: 70rpx;"
            @click="search"
          >
            搜索
          </u-button>
        </view>
      </view>
    </view>
    <!-- tabs切换 -->
    <view class="mt-10">
      <view style="width: 750rpx">
        <u-tabs
          :scrollable="false"
          :list="tabsList"
          lineColor="#F15353"
          lineHeight="5"
          :itemStyle="{
            height: '70rpx',
            width: '210rpx',
          }"
          :activeStyle="{
            color: '#F15353',
            fontWeight: '500',
          }"
          :inactiveStyle="{
            color: '#00001C',
            fontWeight: '400',
          }"
          @click="changeTabs"
        ></u-tabs>
      </view>
    </view>
    <!-- 商品 -->
    <view class="product-box" v-for="item in productList" :key="item.id">
      <view class="f fac d-bc pl-25 pr-25 pt-30">
        <view class="title">订单编号: {{ item.order_sn }}</view>
        <view>
          <view v-if="item.refund_status === 2" class="red fs-2">退款中</view>
          <view v-if="item.refund_status === 3" class="red fs-2">退款完成</view>
          <view v-if="item.refund_status !== 3" class="red fs-2">
            {{ item.status_name }}
          </view>
        </view>
      </view>
      <view class="f pl-25 pr-25 mt-35">
        <view>
          <u-image
            :src="
              item.order_items.length !== 0 ? item.order_items[0].image_url : ''
            "
            radius="16rpx"
            width="172rpx"
            height="172rpx"
          ></u-image>
        </view>
        <view class="ml-25 mt-10">
          <view class="title">
            {{ item.title }}
          </view>
          <view class="mt-40 fs-2 red">￥ {{ toYuan(item.amount) }}</view>
        </view>
      </view>
      <view class="pl-25 mt-35 azure">手机号：{{ item.user_mobile }}</view>
      <view class="product-count">
        <u-grid :border="false" col="3">
          <u-grid-item>
            <view class="fs-2-5 mt-30 title">{{ item.usage }}</view>
            <text class="fs-1-5 mt_10 azure">总次数</text>
          </u-grid-item>
          <u-grid-item>
            <view class="fs-2-5 mt-30 title">{{ item.used }}</view>
            <text class="fs-1-5 mt_10 azure">已使用</text>
          </u-grid-item>
          <u-grid-item>
            <view class="fs-2-5 mt-30 title">{{ item.usage - item.used }}</view>
            <text class="fs-1-5 mt_10 azure">未使用</text>
          </u-grid-item>
        </u-grid>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'saleDetail',

  data() {
    return {
      tabsList: [
        {
          name: '全部',
        },
        {
          name: '待核销',
        },
        {
          name: '已完成',
        },
        {
          name: '退款订单',
        },
      ],
      status: 0, // 已完成状态
      productList: [], // 商品列表
      front_order_type: 0, // 0-全部 1-待核销 2-退款订单
      user_mobile: '', // 手机号
      order_sn: '', // 订单编号
      store_id: 0, // 核销门店ID
      page: 1, // 当前页数
      pageSize: 10, //每页条数
      total: null, // 总数
    }
  },

  onLoad(options) {
    this.store_id = parseInt(options.storeId)
    this.getFrontOrderList()
  },
  // 加载更多数据
  async onReachBottom() {
    if (this.productList.length < this.total) {
      this.page = ++this.page
      const params = {
        order_sn: this.order_sn,
        user_mobile: this.user_mobile,
        store_id: this.store_id,
        front_order_type: this.front_order_type,
        page: this.page,
        pageSize: this.pageSize,
      }
      const res = await this.get(
        '/api/localLife/front/getFrontOrderList',
        params,
      )
      if (res.code === 0) {
        this.productList = this.productList.concat(res.data.list)
      }
    }
  },

  methods: {
    async getFrontOrderList() {
      let params = {
        order_sn: this.order_sn,
        user_mobile: this.user_mobile,
        store_id: this.store_id,
        front_order_type: this.front_order_type,
        page: this.page,
        pageSize: this.pageSize,
      }
      if (this.status === 3) {
        params.status = this.status
      }
      const res = await this.get(
        '/api/localLife/front/getFrontOrderList',
        params,
      )
      if (res.code === 0) {
        this.productList = res.data.list
        this.total = res.data.total
      }
    },
    // 搜索
    search() {
      this.page = 1
      this.getFrontOrderList()
    },
    // 切换tabs
    changeTabs(value) {
      switch (value.name) {
        case '全部':
          this.front_order_type = 0
          this.status = 0
          break
        case '待核销':
          this.front_order_type = 1
          this.status = 0
          break
        case '已完成':
          this.front_order_type = 0
          this.status = 3
          break
        case '退款订单':
          this.front_order_type = 2
          this.status = 0
          break
        default:
          break
      }
      this.search()
    },
  },
}
</script>
<style lang="scss" scoped>
.search-box {
  width: 702rpx;
  height: 222rpx;
  background-color: #ffffff;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  margin: auto;
  margin-top: 22rpx;
}
.product-box {
  width: 702rpx;
  // height: 550rpx;
  background-color: #ffffff;
  margin: 0 auto;
  margin-top: 20rpx;
  padding-bottom: 26rpx;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  .product-count {
    width: 654rpx;
    height: 130rpx;
    background: #f5f5f5;
    border-radius: 24rpx 24rpx 24rpx 24rpx;
    margin: 0 auto;
    margin-top: 20rpx;
  }
}
.red {
  color: #f15353;
}
.title {
  color: #00001c;
  font-size: 28rpx;
  font-weight: bold;
}
.azure {
  color: #6e6e79;
}
</style>
