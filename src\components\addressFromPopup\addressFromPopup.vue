<template>
	<view>
		<u-popup 
			:show="formShow" 
			@close="closeAddress('form')"  
			:round="20" 
			:zIndex="10090"
			:overlay="false"
			:closeable="true">
			<view class="addForm-main">
				<view class="addForm-title d-cc">
					<view class="title c-20 ">{{this.addForm.id?'编辑地址':'添加地址'}}</view>
				</view>
				<view class="from-content">
					
					<u--form
							labelPosition="left"
							:model="addForm"
							:rules="rules"
							ref="addForm"
					>
						<u-form-item
								label="姓名:"
								prop="realname"
								borderBottom
								ref="realname"
								labelWidth="176rpx"
								style="padding:0rpx 32rpx;"
						>
							<u--input
									v-model="addForm.realname"
									placeholder="请输入收件人"
									border="none"
							></u--input>
						</u-form-item>
						<u-form-item
								label="联系电话:"
								prop="mobile"
								borderBottom
								ref="mobile"
								labelWidth="176rpx"
								style="padding:0rpx 32rpx;"
						>
							<u--input
									v-model="addForm.mobile"
									placeholder="请输入联系电话"
									border="none"
							></u--input>
						</u-form-item>
						<u-form-item
								label="设置默认地址"
								prop="is_default"
								borderBottom
								ref="item1"
								labelWidth="240rpx"
								style="padding:0rpx 32rpx;"
						>
						<u-switch v-model="addForm.is_default" @change="defaultChange" size="50"  activeColor="#f56c6c" ></u-switch>
						</u-form-item>
						<u-form-item
								label="所在地区:"
								prop="province"
								borderBottom
								ref="province"
								labelWidth="200rpx"
								style="padding:0rpx 32rpx;"
								
						>
							<u-cell-group :border="false">
								<u-cell @click="openDateLwtbtn" :border="false" :value="addressName?addressName:'请选择地区'" :isLink="true"></u-cell>
							</u-cell-group>
						</u-form-item>
						<u-form-item
								label="街道:"
								prop="town"
								borderBottom
								ref="item1"
								labelWidth="240rpx"
								style="padding:0rpx 32rpx;"
								v-show="streetItemShow"
						>
							<u-cell-group :border="false">
								<u-cell @click="streetBtn" :border="false" :value="addForm.town?addForm.town:'请选择街道'" :isLink="true"></u-cell>
							</u-cell-group>
						</u-form-item>
						<u-form-item
								prop="detail"
								style="padding:0rpx 32rpx;"
								class="area">
							<u--input
								placeholder="请输入详细地址"
								placeholderStyle="color: #ccc;"
								border="none"
								v-model.trim="addForm.detail"
							  ></u--input>
						</u-form-item>
					</u--form>
					
					<view class="content03 d-cc" @click="addFormBtn" >
						<view class="iconfont icon-customform_add addColor"></view>
						<view class="new-add" >{{this.addForm.id?'编辑':'保存'}}</view>
					</view>
				</view>
			</view>
		</u-popup>
		<!-- 选择地址的UI组件 -->
		<address-popup
			v-if="addressShow"
			:addressShow="addressShow"
			@_closeDateLw="closeDateLw"
			@AddressSetOn="addressSetOn"
		>
		</address-popup>
		<!-- 选择街道组件 -->
		
		<street-popup
			v-if="streetObj.show"
			:streetObj="streetObj"
			@closeStreet="closeStreet"
			@streetConfirm="streetConfirm"></street-popup>
	</view>
</template>

<script>
	import eventBus from '@/utils/eventBus'
	import addressPopup from '../addressPopup.vue';
	import streetPopup from '../streetPopup.vue';
	export default {
		name:"addressFromPopup",
		components:{
			addressPopup,
			streetPopup
		},
		data() {
			return {
				newAddNameShow:false, //获取新的值去判断
				formShow:false,
				addressShow:false,
				streetItemShow:false, // 显示街道的item
				streetObj:{ //传递给街道组件
					show:false,
					countyId:0
				},
				streetData:[],
				addressName:'',
				addForm:{
					realname:'',
					mobile:'',
					is_default: false,
					province:'',
					province_id:0,
					county:'',
					country_id:0,
					city:'',
					city_id:0,
					town:'',
					town_id:0,
					detail:'',
				},
				rules: {
					'realname':[
						{
							type: 'string',
							required: true,
							message: '请输入收件人姓名',
							trigger: ['blur']
						},
						{
							min: 2,
							max: 15,
							message: '长度在2-15个字符之间',
							trigger: ['blur']
						},
					],
					'mobile':[
						{
							type: 'string',
							required: true,
							message: '请输入联系电话',
							trigger: ['blur']
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// uni.$u.test.mobile()就是返回true或者false的
								this.userShow = uni.$u.test.mobile(value);
								return uni.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['blur'],
						}
					],
					'province': {
						type: 'string',
						required: true,
						message: '请选择地区',
						trigger: ['blur']
					},
					'town': {
						type: 'string',
						required: true,
						message: '请选择街道',
						trigger: ['blur']
					},
					'detail': {
						type: 'string',
						required: true,
						message: '请输入详细地址',
						trigger: ['blur']
					},
				},
				
			};
		},
		watch:{
				addressName: {
					handler(newName, oldName) {
						if(newName) {
							this.newAddNameShow = true;
						}
					},
					immediate: true,
					deep:true
				},
		},
		created() {
			eventBus.$on('addFormShow', (val,name) => { //使用bus兄弟传值
				this.formShow = val;
				if(name === 'append') {
					let query = {
						realname:'',
						mobile:'',
						is_default: false,
						province:'',
						province_id:0,
						county:'',
						country_id:0,
						city:'',
						city_id:0,
						town:'',
						town_id:0,
						detail:'',
					}
					this.addForm = query;
					this.addressName = '';
				}
			})
			eventBus.$on('editForm', (form,name,show) => { //使用bus兄弟传值
				({
					id: this.addForm.id,
					realname: this.addForm.realname,
					mobile: this.addForm.mobile,
					is_default: this.addForm.is_default,
					province: this.addForm.province,
					province_id: this.addForm.province_id,
					county: this.addForm.county,
					county_id: this.addForm.county_id,
					city: this.addForm.city,
					city_id: this.addForm.city_id,
					town: this.addForm.town,
					town_id: this.addForm.town_id,
					detail: this.addForm.detail,
				} = form);
				this.addForm = form;
				this.addressName = name;
				this.formShow = show;
			})
		},
		onShow() {
		},
		mounted() {
		},
		destroyed() {
			eventBus.$off("editForm");
			eventBus.$off("addFormShow");
		},
		methods:{
			defaultChange(e) {
				this.addForm.is_default = e;
			},
			openDateLwtbtn() {
				// eventBus.$emit('region', true);
				this.addressShow = true;
				this.newAddNameShow =false;
				this.addForm.town = '';
			},
			streetBtn() {
				this.streetObj.show = true;
			},
			closeStreet(show) {
				this.streetObj.show = false;
			},
			closeDateLw(cancel) { // 关闭地址
				this.newAddNameShow = true;
				this.addressShow = !cancel;
			},
			addressSetOn(address,addressName) {
				this.addressName = addressName;
				this.addressShow = false;
				({  //解构赋值
					city: this.addForm.city,
					city_id: this.addForm.city_id,
					county: this.addForm.county,
					county_id: this.addForm.county_id,
					province: this.addForm.province,
					province_id: this.addForm.province_id,
				} = address);
				if(this.addForm.county_id) {
					this.streetObj.countyId = this.addForm.county_id;
					this.streetItemShow = true;
				}
				this.newAddNameShow = true;
				this.$refs.addForm.validateField('province'); //重新校验一次
			},
			streetConfirm(street) {  //获取街道地址
				({town:this.addForm.town,town_id:this.addForm.town_id} = street);
			},
			closeAddress(type) {
				if(type === 'msg') {
					this.formShow = false;
					eventBus.$emit('overlay', 10075);
				} else if (type === 'form') {
					this.formShow = false;
					eventBus.$emit('overlay', 10075);
					let query = {
						realname:'',
						mobile:'',
						is_default: false,
						province:'',
						province_id:0,
						county:'',
						country_id:0,
						city:'',
						city_id:0,
						town:'',
						town_id:0,
						detail:'',
					}
					this.addForm = query;
					// this.openDateLw = false;
				} else {
					console.log('剩下的添加')
				}
			},
			addFormBtn() {
				this.addForm.detail = this.addForm.detail.replace(/\n/g,'')
				if (this.addForm.id) {
					this.$refs.addForm.validate().then(res => {
						this.post('/api/address/update', this.addForm).then((res) => {
							if (res.code === 0) {
								let data = res.data;
								this.$nextTick(() => {
									this.$refs.addForm.resetFields();
								});
								setTimeout(() => {
									this.toast('修改地址成功');
								}, 500);
								this.formShow = false;
			
							} else {
								this.toast(res.msg);
							}
						}).catch((Error) => {
							console.log(Error);
						})
					}).catch(errors => {
						uni.$u.toast('填写错误')
					})
				} else {
					this.$refs.addForm.validate().then(res => {
						this.post('/api/address/add', this.addForm).then((res) => {
							if (res.code === 0) {
								let data = res.data;
								this.$nextTick(() => {
									this.$refs.addForm.resetFields();
								});
								this.formShow = false;
								setTimeout(() => {
									this.toast('新增地址成功');
								}, 500);
			
							} else {
								this.toast(res.msg);
							}
						}).catch((Error) => {
							console.log(Error);
						})
					}).catch(errors => {
						uni.$u.toast('填写错误')
					})
				}
				eventBus.$emit('overlay', 10075);
				eventBus.$emit('refresh',true);
			},
		}
	}
</script>

<style lang="scss" scoped>
	.addForm-main {
		.addForm-title {
			width: 100%;
			height: 80rpx;
			font-weight: 700;
			font-size: 32rpx;
		}
		.from-content {
			.content03 {
				width: 600rpx;
				height: 90rpx;
				margin: 40rpx auto;
				background-color: #f15353;
				border-radius: 10rpx;
				z-index: 99;
				.new-add {
					font-size: 28rpx;
					color: #fff;
					margin-left: 20rpx;
				}
				.addColor {
					color: #fff;
				}
			}
		}
	}
</style>
