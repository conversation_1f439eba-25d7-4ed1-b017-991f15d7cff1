import TencentCloudChat from '@tencentcloud/chat'
import TIMUploadPlugin from 'tim-upload-plugin'
import TIMProfanityFilterPlugin from 'tim-profanity-filter-plugin'

export const IMmixin = {
  data() {
    return {
      tim: {},
      timData: {
        groupID: '',
        SDKAppID: null,
        secretKey: '',
      },
      /**
       * 格式:
       * {
          type: null, // 1 = 普通消息 2 = 进入直播间
          user_name:""
          message: "" // 消息
        }
       */
      messageList: [],
      clientTime: null,
    }
  },
  methods: {
    // 发送消息
    sendMeaageFn() {
      let message = this.tim.createTextMessage({
        to: this.timData.groupID,
        conversationType: TencentCloudChat.TYPES.CONV_GROUP,
        payload: { text: this.textValue },
      })
      let messageRes = this.tim.sendMessage(message)
      messageRes
        .then(res => {
          let messageData = {}
          messageData.type = 1
          messageData.user_name = res.data.message.nick || res.data.message.from
          messageData.message = res.data.message.payload.text
          this.messageHandle(messageData)
          this.textValue = ''
          console.log('发送成功:', res)
        })
        .catch(err => {
          this.textValue = ''
          console.warn('发送失败', err)
        })
    },
    // 退出登录
    imLogout() {
      let promise = this.tim.quitGroup(this.groupId)
      promise
        .then(imResponse => {
          console.log(imResponse.data.groupID) // 退出成功的群 ID
          let logOutPromise = this.tim.logout()
          logOutPromise
            .then(logOutRes => {
              console.log('登出成功:', logOutRes.data)
              this.tim.destroy()
            })
            .catch(logOutError => {
              console.warn('logout error:', logOutError)
            })
        })
        .catch(imError => {
          console.warn('quitGroup error:', imError) // 退出群组失败的相关信息
        })
    },
    // 登录IM
    imLogin(userID = '', userSig = '') {
      let _that = this
      let promise = this.tim.login({ userID, userSig })
      promise
        .then(function (imResponse) {
          console.log('登录成功:', imResponse.data) // 登录成功
          _that.timListener()
          if (imResponse.data.repeatLogin === true) {
            // 标识账号已登录，本次登录操作为重复登录。
            console.log(
              '标识账号已登录，本次登录操作为重复登录',
              imResponse.data.errorInfo,
            )
          }
        })
        .catch(function (imError) {
          console.warn('login error:', imError) // 登录失败的相关信息
        })
    },
    // 监听
    timListener() {
      // 登录成功后会触发 SDK_READY 事件，该事件触发后，可正常使用 SDK 接口
      // 登录成功后触发 onReadyStateUpdate，可在此事件中加入群聊中
      this.tim.on(TencentCloudChat.EVENT.SDK_READY, this.onReadyStateUpdate)
      // 收到新消息后触发 onReceiveMessage，可在此事件处理聊天信息回显逻辑
      this.tim.on(
        TencentCloudChat.EVENT.MESSAGE_RECEIVED,
        this.onReceiveMessage,
      )
      // 群组列表更新后触发 onGroupListUpdated，可在此事件处理群成员登入/出状态
      this.tim.on(
        TencentCloudChat.EVENT.GROUP_LIST_UPDATED,
        this.onGroupListUpdated,
      )
    },
    // 聊天处理
    messageHandle(data = {}) {
      if (this.messageList.length >= 200) {
        this.messageList.splice(0, 100)
      }
      if (data.type && data.message) {
        this.messageList.push(data)
        this.$nextTick(() => {
          uni
            .createSelectorQuery()
            .in(this)
            .select('#messageBox')
            .boundingClientRect(res => {
              this.scrollTop = res.height
            })
            .exec()
        })
      }
    },
    // 登录成功
    onReadyStateUpdate() {
      // 加入群聊
      let promise = this.tim.joinGroup({
        groupID: this.timData.groupID,
      })
      promise
        .then(function (imResponse) {
          switch (imResponse.data.status) {
            case TencentCloudChat.TYPES.JOIN_STATUS_WAIT_APPROVAL: // 等待管理员同意
              console.warn('等待管理员同意')
              break
            case TencentCloudChat.TYPES.JOIN_STATUS_SUCCESS: // 加群成功
              console.log('加入群聊成功') // 加入的群组资料
              break
            case TencentCloudChat.TYPES.JOIN_STATUS_ALREADY_IN_GROUP: // 已经在群中
              console.log('已加入群聊')
              break
            default:
              break
          }
        })
        .catch(function (imError) {
          console.warn('加入群失败:', imError) // 申请加群失败的相关信息
        })
    },
    // 收到新消息
    onReceiveMessage(dataRes) {
      let data = dataRes.data
      if (this.clientTime === data[0].clientTime) {
        return
      }
      this.clientTime = data[0].clientTime
      console.log('消息接收', dataRes)

      let newData = {}
      newData.type = data[0].isSystemMessage ? 2 : 1
      newData.user_name = data[0].nick || data[0].payload.operatorID
      switch (newData.type) {
        case 1: // 发送接收消息
          newData.message = data[0].payload.text
          break
        case 2: // 系统通知用户进入直播间
          newData.message = newData.user_name + '进入直播间'
          break
      }
      this.messageHandle(newData)
    },
    // 群组列表更新
    onGroupListUpdated(data) {
      /**
       * 昵称
       * data[0].lastMessage.nick ? data[0].lastMessage.nick : data[0].lastMessage.payload.operatorID
       */
      console.log('成员数量变化', data)
    },
    // 初始化实例
    imCreate() {
      this.tim = TencentCloudChat.create({ SDKAppID: this.timData.SDKAppID })
      this.tim.setLogLevel(0)
      // 注册腾讯云即时通信 IM 上传插件
      this.tim.registerPlugin({ 'tim-upload-plugin': TIMUploadPlugin })

      // 注册腾讯云即时通信 IM 本地审核插件
      this.tim.registerPlugin({
        'tim-profanity-filter-plugin': TIMProfanityFilterPlugin,
      })
    },
  },
}
