<!-- 充值 -->
<template>
	<view>
		<view class="amount">
			<view class="title mb_20 c-20">充值金额</view>
			<view class="d-f ">
				<text class="mr-10">￥</text>
				<u--input placeholder="请输入充值金额" border="none" type="number" v-model="amount" clearable></u--input>
			</view>
		</view>

		<view class="payment">
			<view class="title c-333">支付方式</view>
			<view class="payment-list" v-if="pay_type === '1'">
				<block v-for="(item,index) in paymentList2" :key="index">
					<view class="item d-bf " @click="payBtn(index,item.pay_type)">
						<view class="d-cf">
							<view class="icon-balance_i iconfont pay-img"></view>
							<view>{{item}}</view>
						</view>
						<view class="d-c">
							<text class="iconfont icon-member_right"></text>
						</view>
					</view>
				</block>
			</view>
			<view class="payment-list" v-if="pay_type === '2'">
				<block v-for="(item,index) in paymentList" :key="index">
					<view class="item d-bf " @click="payBtn(index,item.pay_type, item.pay_code)">
						<view class="d-cf">
							<view class="icon-balance_i iconfont pay-img"></view>
							<view>{{item.name}}</view>
						</view>
						<view class="d-c">
							<text class="iconfont icon-member_right"></text>
						</view>
					</view>
				</block>
			</view>
		</view>
		<payment-popup :paymentShow="paymentShow" :payImg="payImg" @payClose="payClose"></payment-popup>
		<!--#ifdef H5-->
		<u-overlay v-if="!checkWenxin()" :show="hrefShow" @click="hrefShow = false">
			<view class="con-btn">
				<u-button type="success" text="点击进入支付页面" @click="toLink"></u-button>
			</view>
		</u-overlay>
		<!--#endif-->
		<web-view v-if="ju_he_href" :src="ju_he_href"></web-view>
	</view>
</template>

<script>
	import paymentPopup from '@/components/paymentPopup.vue'
	export default {
		components: {
			paymentPopup
		},
		data() {
			return {
				amount: '0.00',
				hrefShow : false,
				paymentShow: false,
				rc_Result: null,
				h5_url:null,
				paymentList: ['汇聚微信支付','微信支付'],
				paymentList2:['微信支付'],
				pay_type: 1,
				payImg: '',
				pollingState:'',//轮询
				app_id: '',
				ju_he_href: '', // 聚合拉卡拉支付地址
			}
		},
		computed: {},
		onLoad(opation){
			if(opation.pay_type) {
				this.pay_type = opation.pay_type
			} 
		},
		mounted() {
			this.getUserBalance()
			this.getWechatAppid()
		},
		methods: {
			// 获取H5调用微信JS配置
			async getWechatAppid() {
			const res = await this.get('/api/wechatofficial/wechatAppid')
			if (res.code === 0) {
				if (res.data.is_open === 1) {
				this.app_id = res.data.app_Id
				}
			} else {
				this.toast('请先登录')
			}
			},
			// 获取充值列表
			getUserBalance() {
				let params = {}
				// #ifdef  MP-WEIXIN
				params.platform = 'miniProgram'
				// #endif
				// #ifdef H5
				params.platform = 'h5'
				// #endif
				this.post('/api/payment/getPayment', params, true).then(res => {
					this.paymentList = res.data.StationBalanceRecharge
				})
			},
			toLink() {
				if(this.rc_Result) {
					window.location.href = this.rc_Result
				}
				if(this.h5_url) {
				}			
			},
			payBtn(index,pay_type, pay_code) {
				
				if (this.amount > 0) {
					// this.paymentShow = true;
					if(pay_type === 7000) {
						console.log('拉卡拉支付');
						this.geRecharge()
					} else if (pay_code === 'ALIPAY') {
						console.log('聚合支付宝支付');
						this.aLiRecharge(pay_type)
					} else if (pay_code === 'WECHAT') {
						console.log('聚合微信支付');
						this.weChatRecharge(pay_type)
					} else if (pay_code === 'MINIAPP_PAY') {
						console.log('聚合小程序终端支付');
						this.miniRecharge(pay_type)
					} else if (pay_code === 'AGGR_CASHIER') {
						console.log('聚合收银台支付');
						this.aggrRecharge(pay_type)
					} else if (pay_code === 'CASH') {
						console.log('聚合拉卡拉支付');
						this.cashRecharge(pay_type)
					} else if (pay_code === 'QUICKPAY') {
						console.log('聚合快捷支付');
						this.quickRecharge(pay_type)
					} else {
						this.joinRecharge(this.pay_type,pay_type)
					}
				} 
				else {
					this.toast('请输入充值金额');
				}
			},
			payClose(data) {
				/* this.paymentShow = data;
				clearInterval(this.pollingState) */
			},
			joinRecharge(argType,payType) {
				let amount = this.toFen(parseFloat(this.amount));
				let _payType = null;
				// #ifdef  H5
				_payType = 11
				if(this.checkWenxin()){
					_payType = 4
				}
				// #endif
				// #ifdef  MP-WEIXIN
				_payType = 4
				// #endif
				// let api
				console.log('argType', argType)
				console.log('payType', payType)
				let api
				let params = {
					amount,
					// pay_type: _payType
				}
				if(argType === '1') { //汇聚余额充值（cp）
					api = '/api/finance/userJoinRecharge'
					params.pay_type = 4 // 小程序
					// #ifdef  H5
					params.pay_type = 11 // h5
					// #endif
				} 
				else { //站内余额充值
					switch (payType) {						
						case 1:
							api = '/api/finance/wechatRechargePay' //汇聚微信支付（cp）
							params.pay_type = 4 // 小程序
							// #ifdef  H5
							params.pay_type = 11 // h5
							// #endif
							break;
						case 10:
							api = '/api/finance/weChatRecharge' //微信支付（zlw）
							params.pay_type = 10 // 小程序
							// #ifdef  H5
							params.pay_type = 12 // h5
							if(this.checkWenxin()){
								params.pay_type = 5
							}
							// #endif
							break;	
						case 12:
							api = '/api/finance/weChatRecharge' //微信支付（zlw）
							params.pay_type = 10 // 小程序
							// #ifdef  H5
							params.pay_type = 12 // h5
							if(this.checkWenxin()){
								params.pay_type = 5
							}
							// #endif
							break;		
						default:
							break;
					}
				}
				if(_payType){ //原api：'/api/finance/userJoinRecharge'
					this.post(api, params, true).then((res) => {
						if (res.code === 0) {							
							this.rc_Result = res.data?.rc_Result
							this.h5_url = res.data?.h5_url
							this.hrefShow = true
							let data = res.data;
							// if(_payType === 11){ //微信H5支付
							// 	window.location.href = data.rc_Result+ '&redirect_url=' + document.location.protocol +
							// 	"//" + window.location.host + '/h5/?menu%23/packageB/wallet/wallet'
							// 	return
							// }
							if(_payType === 4){ //微信小程序支付
								let _data = {}								
								switch(params.pay_type){
									case 4:
										_data = JSON.parse(data.rc_Result)
										break;
									case 6:
										_data = data
										break;
									case 5:
										_data = data.pay
										break;
									case 10:
										_data = data
										break;
								}
								if(params.pay_type === 5){
									WeixinJSBridge.invoke('getBrandWCPayRequest',{
										"appId": _data.appId,     //公众号ID，由商户传入     
										"timeStamp": _data.timeStamp,     //时间戳，自1970年以来的秒数     
										"nonceStr": _data.nonceStr,      //随机串     
										"package": _data.package,
										"signType": _data.signType,     //微信签名方式：     
										"paySign": _data.paySign //微信签名 
									},function(res){
											if (res.err_msg == "get_brand_wcpay_request:ok") {
												this.toast("支付成功")
											}else{
												this.toast("支付失败")
											}
											setTimeout(() => {
												uni.redirectTo({
													url: '/packageB/wallet/wallet'
												})
											}, 1000)
										}
									)
								}else if(params.pay_type === 4 || params.pay_type === 6 || params.pay_type === 10){									
									uni.requestPayment({
										provider: 'wxpay',
										timeStamp: _data.timeStamp,
										nonceStr: _data.nonceStr,
										package: _data.package,
										signType: _data.signType,
										paySign: _data.paySign,
										success: function(res) {
											uni.showToast({
												title: '支付成功',
												icon: 'none',
											})
											setTimeout(() => {
												uni.redirectTo({
													url: '/packageB/wallet/wallet'
												})
											}, 1000)
										},
										fail: function(err) {
											uni.showToast({
												title: '支付失败',
												icon: 'none',
											})
											setTimeout(() => {
												uni.redirectTo({
													url: '/packageB/wallet/wallet'
												})
											}, 1000)
										}
									});
								}
								
								
							}
							/* console.log(data);
							this.payImg = data.rd_Pic;
							let url = data.rc_Result;
							console.log(url); */
							//判断是否在微信
							// let ua = navigator.userAgent.toLowerCase();
							// let isWeixin = ua.indexOf('micromessenger') != -1;
							// // #ifdef H5
							// if (isWeixin) {
								
							// 	window.location.href = url;
								
							// } else {
							// 	this.paymentShow = true;
							// }
							// // #endif
							/* this.paymentShow = true;
							this.pollingState = setInterval(this.getPayStatus,2000,res.data.r2_OrderNo); //轮询获取充值状态 */
						} else {
							this.toast(res.msg);
						}
					}).catch((Error) => {
						console.log(Error);
					})
				}else{
					this.toast("系统错误");
				}
			},
			// 拉卡拉充值
			geRecharge() {
				const params= {
					amount: this.toFen(parseFloat(this.amount)),
					pay_type: 7000
				}
				this.post('/api/payment/getRecharge', params, true).then(res => {
					// #ifdef  H5
					window.open(res.data.url, '_blank')
					window.location.href = window.location.protocol + '//' +  window.location.host + '/h5/?menu#/packageB/wallet/wallet'
					// #endif
					// #ifdef  MP-WEIXIN
					wx.navigateToMiniProgram({
							appId: 'wx889424d565967811',
							path: `payment-cashier/pages/checkout/index?source=WECHATMINI&counterUrl=${
									encodeURIComponent(res.data.url)
							}`,
							envVersion: 'trial',
							// release: 正式版  trial: 体验版
							success(res) {
									// 打开成功
									console.log('打开成功')
									setTimeout(() => {
										uni.redirectTo({
											url: '/packageB/wallet/wallet'
										})
									}, 1000)
							},
							fail(err) {
								console.log('打开失败', err)
								setTimeout(() => {
									uni.redirectTo({
										url: '/packageB/wallet/wallet'
									})
								}, 1000)
							}
					})
					// #endif
				})
			},
			// 聚合支付宝支付
			async aLiRecharge(pay_type) {
				const data = {
					amount: this.toFen(this.amount),
					// pay_info_id: this.pay_info_id,
					pay_type: pay_type,
					// M - 公众号支付  M-H5 - H5网页支付 W - 微信小程序支付
					// #ifdef H5
					type: 'M',
					// #endif
					// #ifdef MP-WEIXIN
					type: 'W'
					// #endif
				}
				const res = await this.post('/api/finance/aggregatePaymentRecharge', data)
				if (res.code === 0) {
					window.location.href = res.data.fields.counter_url
				}
			},
			// 聚合微信支付
			async weChatRecharge(pay_type) {
				const data = {
					amount: this.toFen(this.amount),
					// pay_info_id: this.pay_info_id,
					pay_type: pay_type,
					// M - 公众号支付  M-H5 - H5网页支付 W - 微信小程序支付
					// #ifdef MP-WEIXIN
					type: 'W',
					// #endif
					// #ifdef H5
					type: 'M',
					// #endif
				}
				const res = await this.post('/api/finance/aggregatePaymentRecharge', data)
				if (res.code === 0) {
					// #ifdef H5
					let json = {}
					if (this.app_id) {
						const config = {
						appId: res.data.fields.app_id,
						nonceStr: res.data.fields.nonce_str,
						signature: res.data.fields.pay_sign,
						timestamp: res.data.fields.time_stamp,
						jsApiList: ["chooseWXPay"] ,
						beta: false,
						debug: false,
						// url: herf,
						}
						console.log('wqewqeqwewqeqweqweqweq', config);
						jweixin.config(config)
						json = {
						appId: res.data.fields.app_id,
						nonceStr: res.data.fields.nonce_str,
						package:  res.data.fields.package,
						timestamp: res.data.fields.time_stamp,
						paySign: res.data.fields.pay_sign,
						signType: res.data.fields.sign_type,
						}
						jweixin.chooseWXPay({
						appId: json.appId,
						timestamp: json.timestamp, // 支付签名台生成签
						nonceStr: json.nonceStr, // 支付签名随机串，不长于 32 位
						package: json.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=***）
						signType: json.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
						paySign: json.paySign, // 支付签名
						success: res => {
							// 支付成功后的回调函数
							if (res.errMsg == 'chooseWXPay:ok') {
							this.toast('支付成功')
							setTimeout(() => {
								uni.redirectTo({
								url: '/packageB/wallet/wallet',
								})
							}, 1000)
							}
						},
						cancel: res => {
							// 支付取消
							this.toast('支付取消')
						},
						fail: res => {
							this.toast('支付失败')
							setTimeout(() => {
							uni.redirectTo({
								url: '/packageB/wallet/wallet',
							})
							}, 1000)
						},
						})
						return
					} else {
						window.location.href = res.data.fields.counter_url
					}
					// #endif
					// #ifdef MP-WEIXIN
					if(res.data.fields.miniapp_app_id && res.data.fields.jump_type === 2) {
						//微信小程序支付
						wx.navigateToMiniProgram({
						appId: res.data.fields.miniapp_app_id,
						path: res.data.fields.counter_url,
						envVersion: 'trial',
						// release: 正式版  trial: 体验版
						success(res) {
						// 打开成功
						console.log('打开成功')
						uni.redirectTo({
							url: '/packageB/wallet/wallet',
						})
						},
						fail(err) {
							console.log('打开失败', err)
						},
						})
					} else {
						uni.requestPayment({
						provider: 'wxpay',
						timeStamp: res.data.fields.time_stamp,
						nonceStr: res.data.fields.nonce_str,
						package: res.data.fields.package,
						signType: res.data.fields.sign_type,
						paySign: res.data.fields.pay_sign,
						success: function (res) {
							uni.showToast({
								title: '支付成功',
								icon: 'none',
							})
							setTimeout(() => {
							uni.redirectTo({
								url: '/packageB/wallet/wallet',
							})
							}, 1000)
						},
						fail: function (err) {
							uni.showToast({
								title: '支付失败',
								icon: 'none',
							})
							setTimeout(() => {
							uni.redirectTo({
								url: '/packageB/wallet/wallet',
							})
							}, 1000)
						},
						})
					}
					// #endif
					} else {
					this.toast(res.msg)
					}
			},
			// 聚合快捷支付
			async quickRecharge(pay_type) {
				const data = {
					amount: this.toFen(this.amount),
					// pay_info_id: this.pay_info_id,
					pay_type: pay_type,
					// M - 公众号支付  M-H5 - H5网页支付 W - 微信小程序支付
					type: 'M',
				}
				const res = await this.post('/api/finance/aggregatePaymentRecharge', data)
				if (res.code === 0) {
					window.location.href = res.data.fields.counter_url
				}
			},
			// 聚合小程序终端支付
			async miniRecharge(pay_type) {
				const data = {
					amount: this.toFen(this.amount),
					pay_type: pay_type,
					// M - 公众号支付  M-H5 - H5网页支付 W - 微信小程序支付
					// #ifdef H5
					type: 'M',
					// #endif
					// #ifdef MP-WEIXIN
					type: 'W'
					// #endif
				}
				const res = await this.post('/api/finance/aggregatePaymentRecharge', data)
				if (res.code === 0) {
					wx.navigateToMiniProgram({
					appId: res.data.fields.miniapp_app_id, // 交易小程序的 APPID（申请为商户配置）
					path: res.data.fields.counter_url,
					envVersion: 'release',
					success(res) {
						// 打开成功
						console.log('跳转小程序成功！', res)
						uni.redirectTo({
						url: '/packageB/wallet/wallet',
						})
					},
					fail(err) {
						console.log('打开失败', err)
					},
					})
				}
			},
			// 聚合收银台支付
			async aggrRecharge(pay_type) {
				const data = {
					amount: this.toFen(this.amount),
					pay_type: pay_type,
					// M - 公众号支付  M-H5 - H5网页支付 W - 微信小程序支付
					// #ifdef H5
					type: 'M',
					// #endif

					// #ifdef MP-WEIXIN
					type: 'W'
					// #endif
				}
				const res = await this.post('/api/finance/aggregatePaymentRecharge', data)
				if (res.code === 0) {
					// #ifdef H5
					window.location.href = res.data.fields.counter_url
					// #endif
					// #ifdef MP-WEIXIN
					wx.navigateToMiniProgram({
					appId: res.data.fields.miniapp_app_id, // 交易小程序的 APPID（申请为商户配置）
					path: res.data.fields.counter_url,
					envVersion: 'release',
					success(res) {
						// 打开成功
						console.log('跳转小程序成功！', res)
						uni.redirectTo({
						url: '/packageB/wallet/wallet',
						})
					},
					fail(err) {
						console.log('打开失败', err)
					},
					})
					// #endif
				}
			},
			// 聚合拉卡拉支付
			async cashRecharge(pay_type) {
				const data = {
					amount: this.toFen(this.amount),
					pay_type: pay_type,
					// M - 公众号支付  M-H5 - H5网页支付 W - 微信小程序支付
					// #ifdef H5
					type: 'M',
					// #endif
					// #ifdef MP-WEIXIN
					type: 'W'
					// #endif
				}
				const res = await this.post('/api/finance/aggregatePaymentRecharge', data)
				if (res.code === 0) {
					// #ifdef H5
					window.location.href = res.data.fields.counter_url
					// #endif
					// #ifdef MP-WEIXIN
					this.ju_he_href = res.data.fields.counter_url
					// #endif
				}
			},
			getPayStatus(orderNo) { //获取充值状态
				let json = {
					pay_sn:parseInt(orderNo)  
				}
				this.post('api/finance/getRechargeStatus', json).then(data => {
						if(data.data.pay_status == 1){
							clearInterval(this.pollingState) //停止轮询
							this.paymentShow = false
							this.showText("充值成功")
						}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.con-btn {
		margin: 0 auto;
		padding-top: 40vh;
		width: 50vw;
	}
	.amount {
		width: 711rpx;
		height: 160rpx;
		box-sizing: border-box;
		margin: 23rpx 19rpx 20rpx 20rpx;
		padding: 30rpx 24rpx 0 24rpx;
		background-color: #fff;

		.title {
			font-size: 28rpx;
		}
	}

	.payment {
		margin: 0 20rpx;
		padding: 22rpx 26rpx 0 22rpx;
		background-color: #fff;

		.title {
			font-size: 32rpx;
			margin-bottom: 36rpx;
		}

		.payment-list {
			.item {
				padding: 0 0 23rpx 0;

				.pay-img {
					font-size: 55rpx;
					color: #64b42e;
					// background-color: #64b42e;
					border-radius: 6rpx;
					margin-right: 20rpx;
				}
			}
		}
	}
</style>
