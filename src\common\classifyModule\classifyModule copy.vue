<!-- 分类组件 -->
<template>
	<view>
		<!--商品分类-->
		<view class="d-f" v-if="classification != null && classification != ''">
			<view style="width:20%;color: #666666;height: 1230rpx;overflow-y: scroll;overflow-x:hidden" class="pl_25 pt_37">
				<view v-for="(item,index) in classification" :key="index" :class="index == 0 ?'':'mt_73'" class="d-f"
					@click="oneOptions(item,index)">
					<view v-show="item.checked" class="classifyCss">
					</view>
					<view :class="item.checked == true?'seleCss':''" class="fs-2">
						{{item.name}}
					</view>
				</view>
			</view>
			<scroll-view 
				class="bg-white pr-20 pl-20 pb-20 ml-10 right-content scroll-Y"
				:scroll-top="scrollTop"
				scroll-y="true"
				scroll-with-animation="500">
					<view v-if="checkNull(classification2)" class="children-tab d-cf">
						<block v-for="(itemChildren,indexChildren) in classification2" :key="indexChildren">
							<view class="children-btn" :class="indexBtn === indexChildren?'on':''" @click="scrollBar(indexChildren)">
								{{itemChildren.name}}
							</view>
						</block>
					</view>
					<view class="fs-2" v-if="checkNull(classification2)">
						<view v-for="(item,index) in classification2" :key="index" :class="index == 0?'mt_37':'mt_70'">
							<view class="fw-b " @click="navTo('/packageA/search/searchResult/searchResult?category2_id='+item.id)">{{item.name}}</view>
							<view class="d-f flow mt-25 " :class="'foxbase-title' + index">
								<view v-for="(item1,index1) in item.children" :key="index1" class="ml-15 mt-30"
									@click="navTo('/packageA/search/searchResult/searchResult?category3_id='+item1.id)">
									<u--image  :src="item1.icon ? item1.icon : logoSquareSrc" width="150" height="150"></u--image>
									<text class="fs-1-5 d-cc mt-15 text-wd" style="color:#8A8A8A;">{{item1.name}}</text>
								</view>
							</view>
						</view>
					</view>
					<view class="d-cc fs-1 c-7e7e7e mt-40" v-else>暂无数据~</view>
			</scroll-view>
			
		</view>
		<!--  -->
	</view>
</template>

<script>
	export default {
		props: {
			url: { //type screen为显示筛选
				type: String,
				default: '/api/category/tree'
			},
			type: {
				type:String,
				default:''
			}
		},
		data() {
			return {
				logoSquareSrc: "",
				classification: [],
				classification2: [],
				scrollTop:0,
				indexBtn:0
			}
		},
		mounted() {
			this.classifiedData()
			this.getFramework()
		},
		methods: {
			// 获取默认分类图片
			getFramework(){
				this.get('/api/home/<USER>', {}, true).then((res) => {
					if (res.code === 0) {
						let data = res.data;
						this.logoSquareSrc = data.header?.logo_square_src;
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			oneOptions(item, index) { //一级选项
				this.scrollTop = 0;
				this.indexBtn = 0
				this.$set(this.classification[index], 'checked', true)
				for (var i = 0; i < this.classification.length; i++) {
					if (i != index) {
						this.$set(this.classification[i], 'checked', false)
					}
				}
				this.classification2 = item.children
			},
			classifiedData() { //分类数据
				this.get(this.url, {}, true).then(data => {
					if(this.type) {
						this.classification = data.data;
						this.classification2 = this.classification[0].children
					} else {
						this.classification = data.data.categories;
						this.classification2 = this.classification[0].children
					}
					
					/* 一级选项 */
					for (var i = 0; i < this.classification.length; i++) {
						if (i == 0) {
							this.$set(this.classification[i], 'checked', true)
						} else {
							this.$set(this.classification[i], 'checked', false)
						}
					}
				})
			},
			scrollBar(index) { //滚动到标题栏的位置
				this.indexBtn = index;
				uni.createSelectorQuery().in(this).select(".foxbase-title" + index).boundingClientRect(data=>{//目标节点
					uni.createSelectorQuery().in(this).select(".right-content").boundingClientRect((res)=>{//最外层盒子节点
							const tops = data.top - res.top - 57; //需要减去搜索的高度再去计算
							this.scrollTop = tops;
					　　}).exec();
				}).exec();
			},
			scroll(e) {
				this.scrollTop = e.detail.scrollTop
			}
		}
	}
</script>

<style scoped>
	.seleCss {
		font-size: 30rpx;
		font-weight: bold !important;
		color: #f15353;
		margin-left: 20rpx;
	}

	.seleCssTwo {
		border: 1rpx solid #f15353;
		color: #f15353;
		border-radius: 10rpx;
	}

	.twoOption {
		color: #666666;
		border: 3rpx solid #ebebeb;
		border-radius: 10rpx;
		height: 40rpx;
	}

	.classifyCss {
		background-color: #f15353;
		width:6rpx;
		height: 36rpx;
		margin-left: -16%;
		margin-top: 5rpx;
	}
</style>
<style scoped lang="scss">
	.scroll-Y {
		height: 100vh;
	}
	.text-wd {
		width: 150rpx;
	}
	.children-tab {
		flex-wrap: wrap;
		.children-btn {
			border-radius: 6rpx;
			border: solid 2rpx #ebebeb;
			padding: 10rpx 20rpx;
			margin: 0 15rpx 15rpx 0;
			font-size: 24rpx;
		}
		.on {
			color: #f15353;
			border: solid 2rpx #f15353;
		}
	}
</style>
