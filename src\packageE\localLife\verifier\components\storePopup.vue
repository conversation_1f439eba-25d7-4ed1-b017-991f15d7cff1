<template>
    <view>
      <u-popup
        :show="storeShow"
        :round="20"
        :closeOnClickOverlay="true"
        mode="bottom"
      >
        <view>
          <view class="store_popup">
            <view class="f fac fjsb">
              <view class="mt_20 ml_20 fs-3 fwb">切换商户</view>
              <view
                class="iconfont icon-close11 mt_20 mr_30"
                style="color: #6e6E79"
                @click="closePopup"
              ></view>
            </view>
            <scroll-view class="store_scroll" scroll-y>
              <view
                class="f d-bf store"
                v-for="(item, index) in storeList"
                :key="index"
              >
                <view class="f fac ml_20">
                  <view>
                    <u-image
                      width="80"
                      height="80"
                      shape="circle"
                      :src="item.user.avatar"
                    ></u-image>
                  </view>
                  <view class="ml_20 fwb">{{ item.store.name }}</view>
                </view>
                <view class="mr_20">
                  <u-button
                    type="error"
                    size="small"
                    shape="circle"
                    @click="changeStoreOther(item.store.id)"
                  >
                    进入
                  </u-button>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
      </u-popup>
    </view>
  </template>
  
  <script>
  export default {
    name: 'storePopup',
  
    data() {
      return {
        storeShow: false,
        storeList: [], // 门店列表
        store_id: null,
      }
    },
  
    methods: {
      closePopup() {
        this.storeShow = false
      },
      // 获取登录用户的所有核销门店 --核销员
      async getLocalLifeUseStores(store_id) {
        const res = await this.get('/api/localLife/front/getVerificationsByUserId')
        if (res.code === 0) {
            this.storeList = res.data
        }
      },
      // 切换其他的门店
      changeStoreOther(storeId) {
        this.storeShow = false
        this.$emit('change-store-other', storeId)
      },
    },
  }
  </script>
  <style lang="scss" scoped>
  .store_popup {
    height: 720rpx;
    background-color: #f5f5f5;
    border-radius: 30rpx 30rpx 0rpx 0rpx;
    .store_scroll {
      height: 600rpx;
      .store {
        width: 702rpx;
        height: 128rpx;
        background-color: #ffffff;
        border-radius: 16rpx;
        margin-left: 24rpx;
        margin-top: 20rpx;
      }
    }
  }
  .fwb {
    font-weight: bold;
  }
  </style>
  