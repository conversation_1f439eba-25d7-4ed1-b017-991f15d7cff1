<!-- 新增银行卡 -->
<template>
	<view id="bindBank">
		<u-alert :title="title" type="warning" fontSize="22rpx" class="warningAlert"></u-alert>
		<view class="bankForm">
			<!-- 注意，如果需要兼容微信小程序，最好通过setRules方法设置rules规则 -->
			<u--form labelPosition="left" :model="bankForm" :rules="rules" ref="bankForm">
				<u-form-item label="开户名" prop="account_name" borderBottom ref="account_name" :required="true"
					labelWidth="150">
					<u--input placeholder="请输入姓名" border="none" placeholder-style="text-align:right;font-size:28rpx"
						v-model="bankForm.account_name"></u--input>
				</u-form-item>
				<u-form-item label="证件类型" prop="certificateType" borderBottom ref="certificateType" labelWidth="150">
					<view>身份证</view>
				</u-form-item>
				<u-form-item label="证件号" prop="card_id" borderBottom ref="card_id" :required="true" labelWidth="150">
					<u--input placeholder="请输入在银行办理该卡时使用的证件号" border="none"
						placeholder-style="text-align:right;font-size:28rpx" style="text-align: right;"
						v-model="bankForm.card_id"></u--input>
				</u-form-item>
				<u-form-item label="身份证正面" prop="id_card_z" borderBottom ref="id_card_z" :required="true" labelWidth="160" v-if="bankForm.bank_type === 1">
					<view>
						<u-upload :fileList="cardZList" accept="image" :previewFullImage="true" :maxCount="1" uploadIcon="plus" width="140rpx" height="140rpx" maxSize="********" @afterRead="cardZAfterRead" @delete="deleteZ" mutiple></u-upload>
					</view>
				</u-form-item>
				<u-form-item label="身份证反面" prop="id_card_f" borderBottom ref="id_card_f" :required="true" labelWidth="160" v-if="bankForm.bank_type === 1">
					<view>
						<u-upload :fileList="cardFList" accept="image" :previewFullImage="true" :maxCount="1" uploadIcon="plus" width="140rpx" height="140rpx" maxSize="********" @afterRead="cardFAfterRead" @delete="deleteF" mutiple></u-upload>
					</view>
				</u-form-item>
        <u-form-item label="营业执照" prop="business_license" borderBottom ref="business_license" :required="true" labelWidth="160" v-if="bankForm.bank_type === 0">
					<view>
						<u-upload :fileList="businessLicenseList" accept="image" :previewFullImage="true" :maxCount="1" uploadIcon="plus" width="140rpx" height="140rpx" maxSize="********" @afterRead="businessLicenseAfterRead" @delete="deleteBusinessLicense" mutiple></u-upload>
					</view>
				</u-form-item>
				<u-form-item label="银行卡号" prop="bank_account" borderBottom ref="bank_account" :required="true"
					labelWidth="150">
					<u--input placeholder="请输入在银行卡号" border="none" placeholder-style="text-align:right;font-size:28rpx"
						style="text-align: right;" v-model="bankForm.bank_account"></u--input>
				</u-form-item>
				<view>

				</view>
				<u-form-item label="所属银行" prop="bank_name" borderBottom ref="bank_name" class="bank_name"
					:required="true" labelWidth="150">
					<u--input placeholder="请输入所属银行" border="none"
						placeholder-style="text-align:right;font-size:28rpx;color:#a5a5a5" style="text-align: right;"
						v-model="bankForm.bank_name" @input="searchBank"></u--input>
					<view class="search-bank" v-if="!bankForm.bank_name || bankNameSearch.length > 0">
						<block v-for="(item,index) in bankNameSearch" :key="index">
							<view class="bank-item" @click="bankNameBtn(item.text)">{{item.text}}</view>
						</block>
					</view>
					<view v-else></view>
					<view class="iconfont icon-member_right" @click="chooseBank"></view>
				</u-form-item>
				<u-form-item label="所在地区" prop="province" borderBottom ref="province" :required="true" labelWidth="150">
					<view @click="openDateLwtbtn">{{addressName?addressName:'请选择所在地区'}}</view>
					<view class="iconfont icon-member_right"></view>
				</u-form-item>
				<u-form-item label="银行分行" prop="branch" borderBottom ref="branch" :required="true" labelWidth="150" v-if="bankForm.bank_type === 0">
					<view class="d-cc">
						<u--input placeholder="请输入银行分行" border="none"
							placeholder-style="text-align:right;font-size:28rpx;color:#a5a5a5"
							style="text-align: right;" v-model="bankForm.branch"></u--input>

					</view>
				</u-form-item>
				<u-form-item label="账户类型" prop="bank_type" borderBottom ref="bank_type" :required="true"
					labelWidth="150">
					<view class="way-item d-cf" :class="bankForm.bank_type === 1?'on': ''"
						@click="selectOn('account',1)">
						<view class="way-circle">
							<view :class="bankForm.bank_type === 1?'circle-on': ''"></view>
						</view>
						<text>对私</text>
					</view>
					<view class="way-item d-cf" :class="bankForm.bank_type === 0?'on': ''"
						@click="selectOn('account', 0)">
						<view class="way-circle  ">
							<view :class="bankForm.bank_type === 0?'circle-on': ''"></view>
						</view>
						<text>对公</text>
					</view>
				</u-form-item>
				<u-form-item label="联行号" prop="bank_channel_no" borderBottom ref="bank_channel_no" :required="true"
					labelWidth="150" v-if="bankForm.bank_type === 0">
					<view class="d-cc">
						<u--input placeholder="请输入联行号" border="none"
							placeholder-style="text-align:right;font-size:28rpx;color:#a5a5a5"
							style="text-align: right;" v-model="bankForm.bank_channel_no"></u--input>

					</view>
				</u-form-item>
				<u-form-item label="默认设置" prop="is_default" borderBottom ref="is_default" labelWidth="150">
					<view class="way-item d-cf" :class="bankForm.is_default === 1?'on': ''"
						@click="selectOn('install', 1)">
						<view class="way-circle">
							<view :class="bankForm.is_default === 1?'circle-on': ''"></view>
						</view>
						<text>是</text>
					</view>
					<view class="way-item d-cf" :class="bankForm.is_default === 0?'on': ''"
						@click="selectOn('install', 0)">
						<view class="way-circle">
							<view :class="bankForm.is_default === 0?'circle-on': ''"></view>
						</view>
						<text>否</text>
					</view>
				</u-form-item>
			</u--form>
		</view>
		<view>
			<checkbox-group @change="handleAgreement">
				<label>
					<checkbox value="true" :checked="agreementChecked" color="#f14e4e" style="transform:scale(0.7);padding: 0 26rpx;margin-bottom: 30rpx;" />我已阅读<text class="c-f1" @click.stop="checkAgreement('用户服务协议')">《用户服务协议》</text>及<text class="c-f1" @click.stop="checkAgreement('隐私政策')">《隐私政策》</text>
				</label>
			</checkbox-group>
		</view>
		<view class="withdraw-btn d-cc" @click="bankFormBtn">{{bankId? '立即修改' : '立即绑定'}}</view>


		<!-- 选择银行 -->
		<u-popup :show="show" :round="10" mode="bottom" @close="close" @open="open">
			<view class="classify">
				<view class="classify_title d-bf">
					<view class="c-b5 font_size13" @click="close">取消</view>
					<view class="c-20 font_size17">选择分类</view>
					<view class="font_size13 c-orange" @click="bankNameconfirm">确认</view>
				</view>
				<view class="classify_content">
					<u-radio-group placement="column" iconPlacement="right" v-bind:bank_name="bankForm.bank_name">
						<u-radio size="30" activeColor="#f15353" labelSize="28rpx" labelColor="#202020"
							v-for="(item, index) in bankNameList" :key="index" :label="item.text" :name="item.text"
							@change="bankNameChange">
						</u-radio>
					</u-radio-group>
				</view>
			</view>
		</u-popup>

		<!-- 选择地区 -->
		<address-popup
			:addressShow="addressShow"
			@_closeDateLw="closeDateLw"
			@AddressSetOn="addressSetOn"
		>
		</address-popup>
		<!-- 查看协议 -->
		<u-modal :show="agreement.show"  :title="agreement.title" @confirm="handleCloseAgreement" confirmColor="#f14e4e">
			<view class="slot-content">
				<rich-text :nodes="agreement.content"></rich-text>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import addressPopup from '@/components/addressPopup.vue';
	export default {
		components:{
			addressPopup
		},
		data() {
			return {
				cardZList:[],
				cardFList:[],
        businessLicenseList:[],
				agreementChecked: false, // 勾选协议
				agreement:{ // 协议部分
					show: false,
					title: "",
					content: ""
				},
				selectShow: false, //是否为选择银行
				addressShow:false,
				bankId: 0,
				edit: '',
				create: '',
				title: '为保证您的资金安全，以下内容皆为必填，请认真填写下面的信息。',
				bankNameList: [],
				bankNameSearch: [],
				cardList: [],
				bankConfirm: '',
				addressName: '',
				addForm: {},
				bankComparison: '',
				bankForm: {
					account_name: '',
					bank_account: '',
					bank_channel_no: '',
					bank_name: '',
					bank_type: 0,
					branch: '',
					card_id: '',
					city: '',
					city_id: 0,
					county: '',
					county_id: 0,
					is_default: 0,
					province: '',
					province_id: 0,
					id_card_z:'',
					id_card_f:'',
          business_license:''
				},
				show: false,
				rules: {
					'account_name': {
						type: 'string',
						required: true,
						message: '请输入姓名',
						trigger: ['blur']
					},
					'card_id': [{
							type: 'string',
							required: true,
							message: '请输入在银行办理该卡时使用的证件号',
							trigger: ['blur']
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								return uni.$u.test.idCard(value);

							},
							message: '您输入的身份证号码不是有效格式',
							// 触发器可以同时用blur和change
							trigger: ['blur'],
						}
					],
					'id_card_z': {
						type: 'string',
						required: true,
						message: '请上传身份证正面',
						trigger: ['blur']
					},
					'id_card_f': {
						type: 'string',
						required: true,
						message: '请上传身份证反面',
						trigger: ['blur']
					},
					'business_license': {
						type:'string',
						required: true,
						message: '请上传营业执照',
						trigger: ['blur']
					},
					'bank_account': [{
							type: 'string',
							required: true,
							message: '请输入银行卡号',
							trigger: ['blur']
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// console.log(value);
								let RegEx = /^\d{5,30}$/;
								// let RegEx = /^([1-9]{1})(\d{14}|\d{18})$/;
								if (RegEx.test(value)) {
									return value;
								} else {
									return false
								}

							},
							message: '您输入的银行卡号格式错误',
							// 触发器可以同时用blur和change
							trigger: ['blur'],
						}
					],
					'bank_name': {
						type: 'string',
						required: true,
						message: '请输入所属银行',
						trigger: ['blur','change']
					},
					'branch': {
						type: 'string',
						required: true,
						message: '请选择银行分行',
						trigger: ['blur']
					},
					'province': {
						type: 'string',
						required: true,
						message: '请选择地址',
						trigger: ['blur','change']
					}
				},
			}
		},
		onReady() {
			this.$refs.bankForm.setRules(this.rules);
		},
		onLoad(options) {
			if ((options.id ?? '') !== '') {
				this.bankId = options.id;
			}
		},
		onShow() {
			let _keyName = 'bankNameList';
			uni.getStorage({
				key:_keyName,
				success:(res) => {
					this.bankNameList = res.data;
				},
				fail: (res) => {
					this.getBanks();
				}
			})
			if (this.bankId) { //查询是否是新增还是编辑
				this.getUserBankList();
			}
		},
		onHide() {
		},
		onUnload() {
			uni.removeStorage({
				key:'bankNameList'
			})
		},
		methods: {
			// 身份证正面
			async cardZAfterRead(event){
				let lists = [].concat(event.file)
				let fileListLen = this.cardZList.length
				lists.map((item) => {
					this.cardZList.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					this.bankForm.id_card_z = result.file.url
					let item = this.cardZList[fileListLen];
					this.cardZList.splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: result
					}))
					fileListLen++
				}
			},
			deleteZ(event){
				this.cardZList.splice(event.index, 1)
				this.bankForm.id_card_z = ''
			},
			// 身份证反面
			async cardFAfterRead(event){
				let lists = [].concat(event.file)
				let fileListLen = this.cardFList.length
				lists.map((item) => {
					this.cardFList.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					this.uploadFilePromise(lists[i].url).then(result=>{
						this.bankForm.id_card_f = result.file.url
						let item = this.cardFList[fileListLen];
						this.cardFList.splice(fileListLen, 1, Object.assign(item, {
							status: 'success',
							message: '',
							url: result
						}))
						fileListLen++
					})
				}
			},
			deleteF(event){
				this.cardFList.splice(event.index, 1)
				this.bankForm.id_card_f = ''
			},
			// 营业执照
			async businessLicenseAfterRead(event){
				let lists = [].concat(event.file)
				let fileListLen = this.businessLicenseList.length
				lists.map((item) => {
					this.businessLicenseList.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					this.uploadFilePromise(lists[i].url).then(result=>{
						this.bankForm.business_license = result.file.url
						let item = this.businessLicenseList[fileListLen];
						this.businessLicenseList.splice(fileListLen, 1, Object.assign(item, {
							status: 'success',
							message: '',
							url: result
						}))
						fileListLen++
					})
				}
			},
			deleteBusinessLicense(event){
				this.businessLicenseList.splice(event.index, 1)
				this.bankForm.business_license = ''
			},
			// 上传图片
			uploadFilePromise(url) { //上传图片
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: this.api.host + '/api/common/upload',
						filePath: url,
						name:'file',
						formData: {
							file: url
						},
						success: (res) => {
							console.log(res);
							let json = JSON.parse(res.data)
							resolve(json.data);
							// setTimeout(() => {
								// resolve(res.data.data);
								// let data = JSON.parse(res.data);
								// this.formData.detail_images.push(data.data.file.url);
							// }, 1000)
						}
					});
				})
			},
			// 关闭选择收货地址
			closeDateLw(cancel) {
				this.addressShow = !cancel;
			},
			openDateLwtbtn() {
				this.addressShow = true;
			},
			addressSetOn(address,addressName) {  //获取选择地区的地址
				this.addressName = addressName;
				this.addressShow = false;
				({  //解构赋值
					city: this.bankForm.city,
					city_id: this.bankForm.city_id,
					county: this.bankForm.county,
					county_id: this.bankForm.county_id,
					province: this.bankForm.province,
					province_id: this.bankForm.province_id,
				} = address);
				this.$refs.bankForm.validateField('province') //重新校验一次
			},
			close() {
				this.show = false;
			},
			chooseBank() {
				this.selectShow = true;
				this.show = true;
			},
			bankNameconfirm() { //确认银行卡分类
				this.show = false;
				this.bankForm.bank_name = this.bankConfirm;
				this.bankNameSearch = [];
			},
			bankNameBtn(name) {
				this.bankForm.bank_name = name;
				this.bankComparison = name;
				this.bankNameSearch = [];
			},
			getUserBankList() { //获取银行卡列表
				this.post('/api/finance/getUserBankList', {}, true).then((res) => {
					if (res.code === 0) {
						let data = res.data;
						this.cardList = data;
						let addItem = this.cardList.find((item) => {
							return this.bankId == item.id
						})
						let {
							id,
							account_name,
							bank_account,
							bank_channel_no,
							bank_name,
							bank_type,
							branch,
							card_id,
							city,
							city_id,
							county,
							county_id,
							is_default,
							province,
							province_id,
							user_id
						} = addItem;
						this.bankForm = {
							id,
							account_name,
							bank_account,
							bank_channel_no,
							bank_name,
							bank_type,
							branch,
							card_id,
							city,
							city_id,
							county,
							county_id,
							is_default,
							province,
							province_id,
							user_id
						};
						this.addressName = province + city + county;
						this.bankComparison = bank_name; //对比第一次填入的银行卡名字，避免出现提示列表
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					// console.log(Error);
				})
			},
			getBanks() {
				this.post('/api/common/getBanks', {}, true).then((res) => {
					if (res.code === 0) {
						let data = res.data;
						this.bankNameList = data;
						let bankNameListData = this.bankNameList;
						uni.setStorage({
							key: 'bankNameList',
							data: bankNameListData,
						});

					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					// console.log(Error);
				})
			},
			bankNameChange(name) { //选择银行卡列表
				this.bankConfirm = name;
			},
			searchBank(name) { //查询银行卡所有数据
				//&& name != this.bankComparison
				console.log(this.bankComparison);
				if ((name ?? '') !== '' && name != this.bankComparison) {
					let bankNameList = this.bankNameList.filter((item) => {
						return item.text.indexOf(name) >= 0
					})
					if(this.selectShow == true){ //选择银行就不弹出银行卡提示
						this.bankNameSearch = []
						this.selectShow = false
					}else{
						this.bankNameSearch = bankNameList;
					}

				} else {
					this.bankNameSearch = [];
				}
			},
			selectOn(name, value) {
				if (name === 'account') {
					value === 1 ? this.bankForm.bank_type = 1 : this.bankForm.bank_type = 0;
					console.log(this.bankForm.bank_type + 'account');
				}
				if (name === 'install') {
					value === 1 ? this.bankForm.is_default = 1 : this.bankForm.is_default = 0;
					console.log(this.bankForm.is_default + 'install');
				}
			},
			handleAgreement(val){
				this.agreementChecked = val.detail.value.length ? true : false
			},
			// 关闭协议
			handleCloseAgreement(){
				console.log(111111)
				this.agreement = {
					show: false,
					title: "",
					content: ""
				}
			},
			// 查看协议
			async checkAgreement(text){
				let res = await this.post("/api/user/findSetting")
				this.agreement.show = true
				this.agreement.title = text
				switch(text){
					case "用户服务协议":
						this.agreement.content = res.data.setting.value.service_agreement
						break;
					case "隐私政策":
						this.agreement.content = res.data.setting.value.privacy_policy
						break;
				}
			},
			bankFormBtn() {
				console.log(this.bankForm,'!!!')
				if(!this.agreementChecked){
					uni.$u.toast("请勾选下方协议")
					return false;
				}
				if (this.bankId) {
					this.edit = '/api/finance/saveUserBank';
				} else {
					this.create = '/api/user/createBank';
				}
				this.$refs.bankForm.validate().then(res => {
					this.post(this.edit || this.create, this.bankForm).then((res) => {
						if (res.code === 0) {
							let data = res.data;
							this.toast(res.msg);
							if (!this.bankId) {
								this.$refs.bankForm.resetFields();
								this.addressName = '';
							}
							setTimeout(() => {
								this.backRefresh();
							}, 1000);

						} else {
							this.toast(res.msg);
						}
					}).catch((Error) => {
						// console.log(Error);
					})
				}).catch(errors => {
					// console.log(errors);
					uni.$u.toast('填写错误')
				})
			}
		}
	}
</script>
<style scoped>

	#bindBank ::v-deep .u-alert--warning--light {
		background-color: #f14e4e;
		/* opacity: 0.1; */
		background-color: rgba(241, 78, 78, 0.1);
		padding: 16rpx 14rpx;
	}

	#bindBank ::v-deep .u-form-item__body {
		padding: 40rpx 0;
	}

	#bindBank ::v-deep .u-alert--warning--light .u-alert__text--warning--light {
		color: #f14e4e;
	}

	.bankForm::v-deep .u-form-item__body__right__message {
		text-align: right;
	}

	.bankForm::v-deep .u-form-item__body__right .u-form-item__body__right__content__slot {
		justify-content: flex-end;
		text-align: right !important;
		flex-direction: row; /*兼容小程序*/
	}

	.bankForm::v-deep .u-input__content__field-wrapper .u-input__content__field-wrapper__field {
		text-align: right !important;
	}

	.classify_content::v-deep .u-radio {
		margin-bottom: 53rpx;
	}
	.warningAlert ::v-deep .u-alert__content {
		transform: scale(0.9);
	}
</style>
<style lang="scss" scoped>

	#bindBank {
		.bankForm {
			background-color: #fff;
			color: #333;
			margin: 20rpx 20rpx 30rpx 20rpx;
			padding: 0 26rpx;
			border-radius: 10rpx;

			.icon-member_right {
				margin-left: 12rpx;
			}

			.branch {
				font-size: 28rpx;
				color: #a5a5a5;
			}

			.bank_name {
				position: relative;

				.search-bank {
					position: absolute;
					top: 100rpx;
					right: 50rpx;
					background-color: #ffffff;
					box-shadow: 0px 1px 9px 0px rgba(161, 161, 161, 0.27);
					z-index: 99;

					.bank-item {
						width: 373rpx;
						height: 67rpx;
						line-height: 67rpx;
						border: 2rpx solid rgba(161, 161, 161, 0.27);
						border-radius: 8rpx;
						font-size: 24rpx;
						text-align: right;
						padding-right: 30rpx;
					}
				}
			}

			.way-item {
				margin-left: 108rpx;
				color: #666666;
				font-size: 24rpx;

				.way-circle {
					width: 21rpx;
					height: 21rpx;
					position: relative;
					border: solid 1px #828282;
					border-radius: 50%;
					margin-right: 15rpx;

				}
			}

			.on {
				color: #F14E4E;

				.way-circle {
					border: solid 1px #f14e4e;
				}

				.circle-on {
					width: 14rpx;
					height: 14rpx;
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -55%);
					background-color: #f14e4e;
					border-radius: 50%;
				}

			}
		}

		.withdraw-btn {
			width: 650rpx;
			height: 70rpx;
			margin: 0 auto 54rpx auto;
			background-color: #f14e4e;
			border-radius: 6rpx;
			font-size: 28rpx;
			color: #fff;

		}

		.classify {
			padding: 40rpx 30rpx 30rpx 30rpx;

			.classify_content {
				margin-top: 50rpx;
				height: 600rpx;
				overflow-y: scroll;
			}
		}
	}

	/*选择地区*/
	/*地址选择器*/
	/*--------------------------------*/
	.btn-area {
		padding: 0rpx 20rpx;
	}

	.dateBe {
		position: fixed;
		bottom: 0rpx;
		left: -5rpx;
		width: 760rpx;
		// height:900rpx;
		padding: 0rpx 5rpx;
		box-sizing: border-box;
		z-index: 11000;
		font-size: 28rpx;
		border-top: 1rpx solid #d9d9d9;
		opacity: 0;
		transform: translate(-750rpx, 0rpx);
	}

	.dateBe.true {
		opacity: 1;
		transform: translate(0rpx, 0rpx);
	}

	.dateBe .head {
		display: flex;
		flex-flow: nowrap;
		padding: 0rpx 30rpx;
		line-height: 80rpx;
		border-bottom: 1rpx solid #d9d9d9;
		background: #f8f8f8;
	}

	.main {
		// height: 900rpx;
	}

	.dateBe .head .ll {
		flex: 1;
	}

	.dateBe .head .rr {
		text-align: right;
		flex: 1;
	}

	.dateBe .main {
		background: #f8f8f8;
	}

	.dateBe .main view {
		text-align: center;
	}
	.slot-content{
		max-height: 60vh;
		overflow: auto;
	}
</style>
