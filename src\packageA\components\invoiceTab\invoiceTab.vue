<!-- 发票选项按钮 -->
<template>
	<view class="d-cf d-wrap">
		<block v-for="(item,index) in tabList" :key="index">
			<view class="font_size12 c-gray3 tab_item" v-if="item.checked"
						:class="tabIndex ===  index? 'on': ''" 
						@click="invoiceItem(index,item.type)"
						>
						{{item.name}}
			</view>
		</block>
		
	</view>
</template>

<script>
	export default {
		name:'invoiceTab',
		props:{
			tabList:Array,
			invoiceName:Array,
			tabIndex:{
				type:Number,
				default:0
			}
		},
		data() {
			return {
				indexTab:0,
			}
		},
		methods: {
			invoiceItem(index,value) {
				this.indexTab = index;
				this.$emit('cateGoryVal' , value,index);
				this.$emit('invoiceNameVal' , value,index);
				this.$emit('invoiceTypeVal' , value,index);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.tab_item {
		// flex:1; 
		// width: 140rpx;
		// height: 50rpx;
		padding: 14rpx 22rpx;
		// line-height: 50rpx;
		text-align: center;
		margin-right: 74rpx;
	}
	.on {
		border-radius: 28rpx;
		border: solid 1px #f15353;
		color: #f15353;
	}
</style>
