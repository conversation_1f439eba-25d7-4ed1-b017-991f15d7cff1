<!-- 注册 -->
<template>
  <view class="login_box">
    <view class="header-box">
      <view class="welcome-box">
        <view class="h1-view">欢迎使用</view>
        <view class="h1-view">{{welcome}}</view>
      </view>
    </view>
    <view class="main-view">
      <view class="pl_50 pr_50">
        <u-tabs
          :list="tabsList"
          :current="current"
          lineColor="#F15353"
          :activeStyle="{ fontWeight: '700' }"
          lineWidth="32rpx"
          :lineHeight="8"
        ></u-tabs>
        <u--form
          labelPosition="left"
          :model="form"
          :rules="rules"
          ref="regosterForm"
          class="mt_30"
        >
          <u-form-item prop="userName">
            <u--input
              v-model="form.userName"
              placeholder="请输入您的手机号"
              border="surround"
              shape="circle"
              ref="phone"
              clearable
              :customStyle="inputStyle"
            ></u--input>
          </u-form-item>
          <u-form-item prop="captcha_code">
            <u-input
              v-model="form.captcha_code"
              placeholder="请输入验证码"
              border="surround"
              shape="circle"
              :customStyle="inputStyle"
              @input="handleCode"
              clearable
            >
              <template slot="suffix">
                <button
                  class="Usercode"
                  :class="isDisable ? 'sendCode' : ''"
                  size="mini"
                  @click="$clicks(sendCode)"
                  :disabled="isDisable"
                >
                  {{ codeText }}
                </button>
              </template>
            </u-input>
          </u-form-item>
          <template  v-if="need_password !== 1">
            <u-form-item prop="password">
              <u--input
                type="password"
                v-model="form.password"
                placeholder="请输入密码"
                border="surround"
                shape="circle"
                :customStyle="inputStyle"
                clearable
              ></u--input>
            </u-form-item>
          </template>
          <u-form-item prop="invite_code">
            <u--input
              v-model="form.invite_code"
              placeholder="请输入邀请码"
              border="surround"
              shape="circle"
              :customStyle="inputStyle"
              clearable
            ></u--input>
          </u-form-item>
          <u-form-item>
            <u-button text="注册" @click="$clicks(register)" type="error" shape="circle"></u-button>
          </u-form-item>
          <u-form-item>
            <u-button text="取消" type="warning" @click="navsTo('/pages/index/index')" shape="circle"></u-button>
          </u-form-item>
        </u--form>
        <!--登录 -->
        <view class="user_btn">
          <view
            class="d-cc"
            @click="miniClick"
            v-if="externalValue.show == 'true'"
          >
            已有账号，<text class="c-f15">点击绑定</text>
          </view>
          <!-- #ifdef H5 || APP-PLUS -->
          <view
            class="d-cc"
            @click="navTo('/pages/login/login')"
            v-if="!weixinBrowser"
          >
            已有账号，<text class="c-f15">点击登录</text>
          </view>
          <!-- #endif -->
        </view>
      </view>
    </view>
    <!-- 绑定账号模态框 -->
    <view>
      <u-modal
        :show="show"
        showCancelButton
        :scrollable="false"
        @confirm="miniRAndOfficialAccountsRegister"
        @cancel="show = false"
      >
        <view slot="default">
          <div style="width: 450rpx; margin: 0 auto 25rpx; ">
            <u-tabs
              v-if="minitabs"
              :list="tabsList2"
              @change="tabsCurrentFun"
              :current="current2"
              lineColor="#F15353"
              :activeStyle="{ fontWeight: '700' }"
              lineWidth="90rpx"
              :lineHeight="8"
              itemStyle="padding-left: 15px; padding-right: 15px;weight: 100px;height: 36px;"
            ></u-tabs>
          </div>
          <u--form
            labelPosition="left"
            :model="bindingAccount"
            :rules="miniRules"
            ref="miniRegosterForm"
          >
            <u-form-item prop="userName" borderBottom>
              <u--input
                v-model="bindingAccount.userName"
                placeholder="请输入您的手机号"
                border="none"
                clearable
              ></u--input>
            </u-form-item>
            <u-form-item prop="captcha_code" borderBottom v-if="current2 === 0">
              <view class="d-f mt-20" style="width: 550rpx">
                <u--input
                  v-model="bindingAccount.captcha_code"
                  placeholder="请输入验证码"
                  border="none"
                  @input="handleCode"
                ></u--input>
                <button
                  class="smallUsercode"
                  @click="$clicks(miniSendCode)"
                  :disabled="isDisable"
                >
                  {{ miniCodeText }}
                </button>
              </view>
            </u-form-item>
            <u-form-item prop="password" borderBottom v-else>
              <view class="mt-20" style="width: 550rpx">
                <u--input
                  v-model="bindingAccount.password"
                  placeholder="请输入密码"
                  border="none"
                  clearable
                ></u--input>
              </view>
            </u-form-item>
          </u--form>
        </view>
      </u-modal>
    </view>

    <!--用户协议 -->
    <view class="f fjc">
      <agreement @confirmed="confirmed" v-show="hideShow" class="agreement"></agreement>
    </view>
  </view>
</template>

<script>
import agreement from '@/common/agreement/agreement.vue'
let app = getApp()
export default {
  components: {
    agreement,
  },
  data() {
    return {
      hideShow: true,
      agreementBottom: '10px',
      inputStyle: { backgroundColor: '#F9F9F9' },
      current: 0,
      tabsList: [
        {
          name: '注册',
        },
      ],
      current2: 0,
      minitabs: true,
      tabsList2: [
        {
          name: '手机号验证',
        },
        {
          name: '账号密码验证'
        }
      ],
      pass: false,
      form: {
        userName: '',
        captcha_code: '',
        captcha_key: '',
        invite_code: '',
        password: '', //不需要输入密码，传一个默认密码注册
      },
      bindingAccount: {
        //绑定账号
        userName: '',
        captcha_code: '',
        password: '',
        captcha_key: '',
        wx_unionid: '',
        type: 1,
        wx_mini_openid: '',
        is_bind: '',
      },
      show: false,
      externalValue: {}, //其他页面传过来的值
      logoSrc: '',
      welcome:'',
      show_default_invite_code: null, //是否显示默认邀请码
      default_invite_code: null, //默认邀请码
      required_invite_code: null, //	邀请码是否必填
      need_password: null,
      type: 'register',
      codeText: '获取短信验证码',
      miniCodeText: '获取短信验证码',
      isDisable: false,
      miniIsDisable: false,
      userShow: false,
      miniUserShow: false,
      weixinBrowser: false, //是否为微信浏览器
      src: 'https://shop-yunzshop-com.oss-cn-hangzhou.aliyuncs.com/newimage/4fcbd209f8578cfb7f78ad2c96a0f789.jpeg',
      miniRules: {
        userName: [
          {
            required: true,
            message: '请输入手机号',
            trigger: ['blur', 'change'],
          },
          {
            // 自定义验证函数，见上说明
            validator: (rule, value, callback) => {
              // 上面有说，返回true表示校验通过，返回false表示不通过
              // uni.$u.test.mobile()就是返回true或者false的
              this.miniUserShow = uni.$u.test.mobile(value)
              return uni.$u.test.mobile(value)
            },
            message: '手机号码不正确',
            // 触发器可以同时用blur和change
            trigger: ['blur'],
          },
        ],
      },
      rules: {
        userName: [
          {
            required: true,
            message: '请输入手机号',
            trigger: ['blur', 'change'],
          },
          {
            // 自定义验证函数，见上说明
            validator: (rule, value, callback) => {
              // 上面有说，返回true表示校验通过，返回false表示不通过
              // uni.$u.test.mobile()就是返回true或者false的
              this.userShow = uni.$u.test.mobile(value)
              return uni.$u.test.mobile(value)
            },
            message: '手机号码不正确',
            // 触发器可以同时用blur和change
            trigger: ['blur'],
          },
        ],
        captcha_code: {
          type: 'number',
          max: 10,
          required: true,
          message: '请输入验证码',
          trigger: ['blur', 'change'],
        },
        password: {},
        invite_code: [
          {
            type: 'string',
            required: false,
            message: '请输入邀请码',
            trigger: ['change', 'blur'],
          },
        ],
      },
    }
  },
  onReady() {
    this.$refs.regosterForm.setRules(this.rules)
    if (this.show) {
      this.$refs.miniRegosterForm.setRules(this.miniRules)
    }
  },
  onLoad(opations) {
    console.log(opations);
    if (opations) {
      this.externalValue = {
        ...opations,
      }
    }
    if (parseInt(uni.getStorageSync("invite_code"))) {
      this.get('/api/user/getCode',{pid: parseInt(uni.getStorageSync("invite_code"))}).then(res => {
        uni.removeStorageSync('invite_code')
        if (res.code === 0) {
          this.form.invite_code = res.data.invite_code
        }
      })
    }
    //#ifdef H5
    this.checkThePlatform()
    //#endif
  },
  onShow() {
    this.findAgentSetting()
    this.framework() //头像设置
  },
  mounted() {
    // #ifdef H5
    const initWindowHeight = window.innerHeight
    window.addEventListener('resize', () => {
      const newWindowHeight = window.innerHeight
      if(initWindowHeight > newWindowHeight){
        this.hideShow = false
      }else{
        this.hideShow = true
      }
    })
    // #endif
  },
  methods: {
    // 点击绑定
    miniClick() {
      this.show = true;
      this.minitabs = false;
      this.current2 = 0;
      this.$nextTick(() => {
        this.minitabs = true
      })
      
      
      this.miniRules.captcha_code = {
        type: 'number',
        max: 10,
        required: true,
        message: '请输入验证码',
        trigger: ['blur', 'change'],
      }
    },
    // 切换账号密码验证或手机号验证
    tabsCurrentFun(res) {
      if (res.index === 0) {
        this.miniRules.captcha_code = {
          type: 'number',
          max: 10,
          required: true,
          message: '请输入验证码',
          trigger: ['blur', 'change'],
        }
        this.miniRules.password = {}
      } else {
        this.miniRules.password = {
          required: true,
          message: '请输入密码',
          trigger: ['blur', 'change'],
        }
        this.miniRules.captcha_code = {}
      }
      this.current2 = res.index
    },
    // 处理验证码
      handleCode(){
        if(this.bindingAccount.captcha_code.length > 4){
          this.bindingAccount.captcha_code.slice(0,4)
        }
        if(this.form.captcha_code.length > 4){
          this.form.captcha_code.slice(0,4)
        }
      },
    confirmed(status) {
      this.pass = status
    },
    sendCode() {
      if (uni.$u.test.mobile(this.form.userName)) {
        this.post('/api/user/sendCode', {
          mobile: this.form.userName,
          type: this.type,
        })
          .then(res => {
            if (res.code === 0) {
              let data = res.data
              this.form.captcha_key = data.captcha_key
              this.timeReduce()
            }
          })
          .catch(Error => {
            console.log(Error)
          })
      } else {
        this.toast('手机号格式不正确')
      }
    },
    timeReduce() {
      let _this = this
      let time = 60
      this.timer = setInterval(() => {
        this.codeText = `重新发送${time}s`
        time -= 1
        this.isDisable = true
        if (time <= 0) {
          this.isDisable = false
          this.codeText = '获取短信验证码'
          clearInterval(_this.timer)
        }
      }, 1000)
    },

    miniSendCode() {
      this.post('/api/user/sendCode', {
        mobile: this.bindingAccount.userName,
        type: 'login',
      })
        .then(res => {
          if (res.code === 0) {
            let data = res.data
            this.bindingAccount.captcha_key = data.captcha_key
            this.miniTimeReduce()
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    miniTimeReduce() {
      let _this = this
      let time = 60
      this.timer = setInterval(() => {
        this.miniCodeText = `重新发送${time}s`
        time -= 1
        this.miniIsDisable = true
        if (time <= 0) {
          this.miniIsDisable = false
          this.miniCodeText = '获取短信验证码'
          clearInterval(_this.timer)
        }
      }, 1000)
    },
    async register() {
      //注册账号
      if (!this.pass) {
        this.toast('请先阅读用户并勾选')
        return
      }
      if (this.form.invite_code) {
        let inviteCodeRes = await this.post('/api/user/checkInviteCode', {
          invite_code: this.form.invite_code,
        })
        if (inviteCodeRes.code !== 0) {
          this.toast(inviteCodeRes.msg)
          return
        }
      }

      if (this.externalValue.show == 'true') {
        if (this.checkNull(this.externalValue.nickname)) {
          this.form['nickname'] = this.externalValue.nickname
        }

        if (this.checkNull(this.externalValue.avatar)) {
          this.form['avatar'] = this.externalValue.avatar
        }

        if (this.checkNull(this.externalValue.type)) {
          this.form['type'] = parseInt(this.externalValue.type)
        }

        if (this.checkNull(this.externalValue.wx_mini_openid)) {
          this.form['wx_mini_openid'] = this.externalValue.wx_mini_openid
        }

        if (this.checkNull(this.externalValue.wx_unionid)) {
          this.form['wx_unionid'] = this.externalValue.wx_unionid
        }

        if (this.checkNull(this.externalValue.wx_openid)) {
          this.form['wx_openid'] = this.externalValue.wx_openid
        }
      }
      let url =
        this.externalValue.show == 'true'
          ? '/api/user/wechatRegister'
          : '/api/user/register'
      this.$refs.regosterForm
        .validate()
        .then(res => {
          // 当H5本地存储有pid时注册时携带上
          // #ifdef H5
          if(localStorage.getItem('pid')) {
            this.form.pid = parseInt(localStorage.getItem('pid'))
          }
          // #endif
          
          
          this.post(url, this.form, true, true)
            .then(res => {
              if (res.code === 0) {
                let data = res.data
                // H5注册成功清除pid
                // #ifdef H5
                localStorage.removeItem('pid')
                // #endif
                if (data.status === 0) {
                  this.toast(res.msg)
                  setTimeout(() => {
                    uni.redirectTo({
                      url: '/packageC/membershipApplication/membershipApplication',
                    })
                  }, 1500)
                  return
                }
                if (this.externalValue.show == 'true') {
                  //公众号或者小程序注册
                  uni.setStorageSync('token', data.token)
                  uni.setStorageSync('user', data.user)
                  uni.setStorageSync('type', 'refresh')
                  this.toast(res.msg)
                  setTimeout(() => {
                    uni.redirectTo({
                      url: '/pages/index/index',
                    })
                  }, 1500)
                  return
                } else {
                  //正常注册
                  this.toast(res.msg)
                  setTimeout(() => {
                    uni.redirectTo({
                      url: '/pages/login/login',
                    })
                  }, 1500)
                }
              } else {
                if (
                  this.externalValue.show == 'true' &&
                  res.msg == '注册成功，需要管理员审核才可以登录'
                ) {
                  //公众号或者小程序注册
                  this.toast(res.msg)
                  setTimeout(() => {
                    uni.redirectTo({
                      url: '/packageC/membershipApplication/membershipApplication',
                    })
                  }, 1500)
                }
              }
            })
            .catch(Error => {
              console.log(Error)
            })
        })
        .catch(errors => {
          this.toast('填写错误')
        })
    },
    miniRAndOfficialAccountsRegister() {
      //小程序以及公众号账号绑定
      this.$refs.miniRegosterForm
        .validate()
        .then(res => {
          console.log(this.externalValue.show);
          
          if (this.externalValue.show == 'true') {
            if (this.checkNull(this.externalValue.wx_unionid)) {
              this.bindingAccount['wx_unionid'] = this.externalValue.wx_unionid
            }
            if (this.checkNull(this.externalValue.wx_mini_openid)) {
              this.bindingAccount['wx_mini_openid'] =
                this.externalValue.wx_mini_openid
            }
            if (this.checkNull(this.externalValue.wx_openid)) {
              this.bindingAccount['wx_openid'] = this.externalValue.wx_openid
            }
            if (this.checkNull(this.externalValue.type)) {
              this.bindingAccount['type'] = parseInt(this.externalValue.type)
            }
          }
          this.bindingAccount['is_bind'] = 1

          let params = {
            is_bind: this.bindingAccount.is_bind,
            type: this.bindingAccount.type,
            userName: this.bindingAccount.userName,
            wx_mini_openid: this.bindingAccount.wx_mini_openid,
            wx_unionid: this.bindingAccount.wx_unionid,
            wx_openid: this.bindingAccount.wx_openid
          }

          // 当微信小程序本地存储有pid时绑定时携带上
          // #ifdef MP-WEIXIN
          if(uni.getStorageSync('pid')) {
              this.bindingAccount['pid'] = parseInt(uni.getStorageSync('pid'))
              params.pid =  parseInt(uni.getStorageSync('pid'))
          }
          // #endif
          if (this.current2 === 0) {
            params.captcha_key = this.bindingAccount.captcha_key
            params.captcha_code = this.bindingAccount.captcha_code
          } else {
            params.password = this.bindingAccount.password
          }
          this.post('/api/user/wechatRegister', params)
            .then(res => {
              if (res.code === 0) {
                let data = res.data
                // 微信小程序成功清除pid
                // #ifdef MP-WEIXIN
                uni.removeStorageSync('pid')
                // #endif
                if (data.status === 0) {
                  this.toast(res.msg)
                  setTimeout(() => {
                    uni.redirectTo({
                      url: '/packageC/membershipApplication/membershipApplication',
                    })
                  }, 1500)
                  return
                }
                uni.setStorageSync('token', data.token)
                uni.setStorageSync('user', data.user)
                uni.setStorageSync('type', 'refresh')
                this.toast('绑定成功,已自动为您登录')
                setTimeout(() => {
                  uni.redirectTo({
                    url: '/pages/index/index',
                  })
                }, 1500)
              }
            })
            .catch(Error => {
              console.log(Error)
            })
        })
        .catch(errors => {
          this.toast('填写错误')
        })
    },
    findAgentSetting() {
      //会员设置
      this.post('/api/user/findAgentSetting', {}, true)
        .then(res => {
          if (res.code === 0) {
            let data = res.data
            this.show_default_invite_code =
              data.setting.show_default_invite_code
            if (this.show_default_invite_code) {
              this.form.invite_code = data.setting.default_invite_code
            }
            // this.default_invite_code = data.setting.default_invite_code;
            this.required_invite_code = data.setting.required_invite_code
            this.need_password = data.setting.need_password
            if (this.need_password !== 1) {
              this.rules.password = {
                type: 'string',
                required: true,
                message: '请输入密码',
                trigger: ['blur', 'change'],
              }
            }
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    framework() {
      this.get('/api/home/<USER>', {}, true)
        .then(res => {
          if (res.code === 0) {
            let data = res.data
            this.logoSrc = data.header?.logo_src
            this.welcome = data.header?.welcome
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    checkThePlatform() {
      let ua = window.navigator.userAgent.toLowerCase()
      if (ua.match(/MicroMessenger/i) == 'micromessenger') {
        this.weixinBrowser = true
      } else {
        this.weixinBrowser = false
      }
    },
  },
}
</script>
<style lang="scss" scoped>
/* #ifdef MP-WEIXIN */
::v-deep .loginCode .u-form-item__body__right__content__slot {
  flex-direction: row;
}

/* #endif */
</style>
<style lang="scss" scoped>
// page {
//   background-color: #fff;
// }
.agreement {
  position: absolute;
  bottom: 50rpx;
}
.login_box {
  height: 100vh;
  background-color: #fff;
  .header-box {
    position: relative;
    width: 100%;
    height: 439rpx;
    background-image: url('https://yunxingongyinglian.oss-cn-guangzhou.aliyuncs.com/2023124/1701654061logo_img.png');
    background-repeat: no-repeat;
    background-size: 100%;
    .welcome-box {
      position: absolute;
      bottom: 110rpx;
      padding: 0 50rpx;
      .h1-view {
        font-size: 50rpx;
        font-weight: bold;
      }
    }
  }
  .main-view {
    position: absolute;
    top: 358rpx;
    left: 0;
    right: 0;
    background: #fff;
    border-top-left-radius: 50rpx;
    border-top-right-radius: 50rpx;
    .user_btn{
      .c-f15{
        color: #F15353;
      }
    }
  }
  .smallUsercode {
    width: 210rpx;
    font-size: 22rpx;
    color: #f15353;
  }
}
::v-deep .u-tabs__wrapper__nav__item {
  padding: 0rpx;
}
::v-deep .Usercode {
  color: #f15353;
  &.sendCode {
    color: #aaaab3;
  }
}
::v-deep .Usercode:after {
  border: none;
  border-left: 3px solid #ececec;
  border-radius: 0;
}
</style>
