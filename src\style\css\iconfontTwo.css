@font-face {
  font-family: "iconfont"; /* Project id 432132 */
  src: url('https://at.alicdn.com/t/font_432132_swm1dyfsl3.woff2?t=1648107279621') format('woff2'),
       url('https://at.alicdn.com/t/font_432132_swm1dyfsl3.woff?t=1648107279621') format('woff'),
       url('https://at.alicdn.com/t/font_432132_swm1dyfsl3.ttf?t=1648107279621') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-fontclass-xiazai:before {
  content: "\eb09";
}

.icon-fontclass-piliang:before {
  content: "\eb08";
}

.icon-fontclass-shezhi2:before {
  content: "\ea50";
}

.icon-fontclass-daishouhuo:before {
  content: "\eaaf";
}

.icon-fontclass-daishouhuo1:before {
  content: "\eaac";
}

.icon-fontclass-gantanhao:before {
  content: "\ea2f";
}

.icon-all_select_active:before {
  content: "\e90a";
}

.icon-zb_all_share:before {
  content: "\e894";
}

.icon-pay_default:before {
  content: "\e8d1";
}

.icon-advertise-next:before {
  content: "\e8c0";
}

.icon-member_right:before {
  content: "\e881";
}

.icon-erweima:before {
  content: "\e627";
}