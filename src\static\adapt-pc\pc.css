body[adapt='pc']>*{
	display: none !important;
}
 
body[adapt='pc']>uni-adapt-pc{
	display: block !important;
}
 
body[adapt='pc'] {
	margin: 0;
	background-color: #fff;
	width: 100vw;
	height: 100vh;
}

/* ==== App.vue 文件 ==== */
/* 为了避免电脑浏览器中的滚动条影响到布局，可在 style 标记中添加如下 CSS 代码*/
 
/* 条件编译，仅在H5平台生效 */





body[adapt='pc'] uni-adapt-pc .container{
	position: fixed;
	width: 375px;
	height: 100%;
	z-index: 1;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	margin: auto;
	max-height: 680px;
	box-sizing: border-box;
	border: 1px solid #ddd;
	box-shadow: 0 0 10px #ddd;
}

body[adapt='pc'] uni-adapt-pc .right-side{
	position: fixed;
	width: 185px;
	height: 217px;
	right:500px;
	top:200px;
	margin: auto;
	z-index: 1;
	box-sizing: border-box;
	/* border: 1px solid #ddd; */
	/* box-shadow: 0 0 10px #ddd; */
}
 
body[adapt='pc'] uni-adapt-pc iframe{
	width: 100%;
	height: 100%;
	border: none;
}