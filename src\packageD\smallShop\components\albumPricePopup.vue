<template>
  <view>
    <u-popup :closeable="true" :show="isShow" @close="handleClose">
      <view class="d-c mt_40 fs-3">导入</view>
      <view class="p20">
        <u-row gutter="20">
          <u-col span="2">
            <text>销售价</text>
          </u-col>
          <u-col span="4">
            <u-radio-group
              placement="column"
              v-model="takeGroup"
              iconSize="24rpx"
            >
              <u-radio
                size="40"
                activeColor="#f15353"
                labelSize="28rpx"
                labelColor="#202020"
                :customStyle="{ marginBottom: '34rpx' }"
                v-for="(item, index) in takeValues"
                :key="index"
                :label="item.name"
                :name="item.value"
                @change="takeChange(item.name, item.value)"
              ></u-radio>
            </u-radio-group>
          </u-col>
          <u-col span="6">
            <view class="d-bf">
              <u-input
                placeholder="协议价比例"
                border="surround"
                v-model="formData.agreement_price_ratio"
                fontSize="24rpx"
                type="number"
              ></u-input>
              <view class="ml_30">%</view>
            </view>
            <view class="d-bf">
              <u-input
                placeholder="建议零售价比例"
                border="surround"
                v-model="formData.origin_price_ratio"
                fontSize="24rpx"
                type="number"
              ></u-input>
              <view class="ml_30">%</view>
            </view>
            <view class="d-bf">
              <u-input
                placeholder="指导价比例"
                border="surround"
                v-model="formData.guide_price_ratio"
                fontSize="24rpx"
                type="number"
              ></u-input>
              <view class="ml_30">%</view>
            </view>
            <view class="d-bf">
              <u-input
                placeholder="营销价比例"
                border="surround"
                v-model="formData.activity_price_ratio"
                fontSize="24rpx"
                type="number"
              ></u-input>
              <view class="ml_30">%</view>
            </view>
          </u-col>
        </u-row>
        <view class="mt_50">
          <u-button type="error" text="确定" @click="confirm"></u-button>
        </view>
      </view>
    </u-popup>
  </view>
</template>
<script>
export default {
  name: 'albumPricePopup',
  data() {
    return {
      type: null,
      id: null,
      isShow: false,
      takeGroup: 'is_agreement_price',
      takeValues: [
        {
          name: '协议价',
          value: 'is_agreement_price',
        },
        {
          name: '建议零售价',
          value: 'is_origin_price',
        },
        {
          name: '指导价',
          value: 'is_guide_price',
        },
        {
          name: '营销价',
          value: 'is_activity_price',
        },
      ],
      formData: {
        is_agreement_price: 0, // 协议价是否勾选
        is_origin_price: 0, // 建议零售价是否勾选
        is_guide_price: 0, // 指导价是否勾选
        is_activity_price: 0, // 营销价是否勾选
        agreement_price_ratio: 100, // 协议价比例
        origin_price_ratio: 100, // 建议零售价比例
        guide_price_ratio: 100, // 指导价比例
        activity_price_ratio: 100, // 营销价比例
      },
    }
  },
  methods: {
    confirm() {
      if (!this.takeGroup) {
        this.toast('请选择定价策略')
        return
      }
      let params = {}
      switch (this.type) {
        case 1:
          params.album_ids = [this.id]
          break
        case 2:
          params.album_ids = this.id
          break
      }
      switch (this.takeGroup) {
        case 'is_agreement_price':
          params.price_proportion = this.formData.agreement_price_ratio * 100
          params.price_type = 2
          break
        case 'is_origin_price':
          params.price_proportion = this.formData.origin_price_ratio * 100
          params.price_type = 0
          break
        case 'is_guide_price':
          params.price_proportion = this.formData.guide_price_ratio * 100
          params.price_type = 1
          break
        case 'is_activity_price':
          params.price_proportion = this.formData.activity_price_ratio * 100
          params.price_type = 3
          break
        default:
          params.price_proportion = 10000
          params.price_type = 0
          break
      }
      if (params.price_proportion < 10000) {
        this.toast('比例不能小于100')
        return
      }
      this.$emit('pricePopupSave', params)
    },
    init(type, id) {
      this.isShow = true
      this.id = id
      this.type = type
    },
    handleClose() {
      this.isShow = false
      this.id = null
      this.formData = {
        is_agreement_price: 0, // 协议价是否勾选
        is_origin_price: 0, // 建议零售价是否勾选
        is_guide_price: 0, // 指导价是否勾选
        is_activity_price: 0, // 营销价是否勾选
        agreement_price_ratio: 100, // 协议价比例
        origin_price_ratio: 100, // 建议零售价比例
        guide_price_ratio: 100, // 指导价比例
        activity_price_ratio: 100, // 营销价比例
      }
    },
    takeChange(name, value) {
      this.formData.is_agreement_price = 0
      this.formData.is_origin_price = 0
      this.formData.is_guide_price = 0
      this.formData.is_activity_price = 0
      this.formData[value] = 1
    },
  },
}
</script>
<style scoped>
::v-deep .u-input {
  margin-bottom: 20rpx;
  height: 40rpx;
  padding: 6rpx 18rpx !important;
}
::v-deep .u-col {
  align-self: flex-start;
}
</style>
<style lang="scss" scoped>
.p20 {
  padding: 40rpx;
}
.w20 {
  width: 20vw;
}
.relatvie {
  position: relative;
  height: 100vh;
}
.con-fixed {
  position: fixed;
  bottom: 0;
  padding: 3vw;
  width: 94vw;
  background: white;
}
</style>
