<!-- 申请代理，申请供应商的状态栏 -->
<template>
	<view>
		<view class="article_steps d-cc">
			<u-steps :current="applyStatus + 1" inactiveColor="#e0e0e0" activeColor="#f14e4e">
				<u-steps-item :title="type === 'agency' ? '申请代理': '供应商申请'">
					<view slot="icon">
						<image class="slot-img"
							src="https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/uni-supply-platform/supplier.png">
						</image>
					</view>
				</u-steps-item>
				<u-steps-item title="审核">
					<view slot="icon">
						<image class="slot-img"
							:src="applyStatus === 0 && isApply === 1 || applyStatus === 1?'https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/uni-supply-platform/auditCheck.png':'https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/uni-supply-platform/auditUnCheck.png'">
						</image>
					</view>
				</u-steps-item>
				<u-steps-item title="通过">
					<view slot="icon">
						<image class="slot-img"
							:src="applyStatus === 1 && isApply === 1?'https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/uni-supply-platform/supplierPass.png':'https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/uni-supply-platform/supplierUnpass.png'">
						</image>
					</view>
				</u-steps-item>
			</u-steps>
		</view>
	</view>
</template>

<script>
	export default {
		name: "applyHead",
		props: {
			applyStatus: {
				type: [Number],
				default: -1
			},
			isApply: {
				type: [Number],
				default: 0
			},
			type: {
				type:String,
				default:''
			}
		},
		options: {
			styleIsolation: 'shared'
		},
		data() {
			return {

			};
		},

	}
</script>

<style scoped>
	::v-deep .article_steps .u-steps-item__content {
		margin-top: 22rpx !important;
		margin-left: 0 !important;
	}

	.article_steps>>>.u-text:nth-child(1) {
		color: #f14e4e;
	}

	::v-deep .article_steps .u-steps-item__wrapper--row {
		width: auto;
	}

	::v-deep .u-text__value {
		font-size: 23rpx !important;
	}

	.article_steps>>>.u-text:nth-child(2) {
		color: #f14e4e;
	}

	.article_steps>>>.u-steps-item__line {
		height: 4rpx !important;
	}
</style>
<style lang="scss" scoped>
	.article_steps {
		box-sizing: border-box;
		height: 142rpx;
		margin: 20rpx 20rpx 0 20rpx;
		background-color: #fff;
		border-radius: 10rpx;

		.slot-img {
			width: 53rpx;
			height: 52rpx;
			// margin-right: 70rpx;
			margin: 0 30rpx;
		}

		.icon-fontclass-shenhe,
		.icon-fontclass-tongguo {
			font-size: 53rpx;
		}
	}
</style>
