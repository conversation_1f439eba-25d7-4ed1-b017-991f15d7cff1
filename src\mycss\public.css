/*每个页面公共css */

/*每个页面公共css */
body {
  /* background-color: #F4F5F6;
	    height: 100%;
	    font-size: 28upx;
	    line-height: 1.8; */

  background: #f5f5f5;
  font-size: 30rpx;
  color: #353535;
  font-weight: normal;
  font-family: 'Arial', 'Microsoft YaHei', '黑体', '宋体', sans-serif;
}

.bg-theme {
  /* 主题背景色*/
  background: #f14e4e;
}

.b-mask {
  /*自定义阴影*/
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0rpx;
  left: 0rpx;
  z-index: 10099;
  display: none;
}
.b-mask.true {
  display: block;
}

.c-theme {
  /* 主题字体色 */
  color: #f14e4e;
}

/* 白色背景色 */
.bg-white {
  background: #fff;
}

/* 红 */
.c-f14d4d {
  color: #f14d4d;
}

.c-f14e4e {
  color: #f14e4e;
}

/* 灰 */
.c-959595 {
  color: #959595;
}
.c-aaaaaa {
  color: #aaaaaa;
}

.c-7e7e7e {
  color: #7e7e7e;
}
.c-777 {
  color: #777777;
}
.c-7b7b7b {
  color: #7b7b7b;
}

.c-262626 {
  color: #262626;
}

.c-2b2b2b {
  color: #2b2b2b;
}

.c-666666 {
  color: #666666;
}

.c-333333 {
  color: #333333;
}

.c-5e5e5e {
  color: #5e5e5e;
}

.radius10 {
  border-radius: 10rpx;
}

.radius15 {
  border-radius: 15rpx;
}

.radius20 {
  border-radius: 20rpx;
}

.radius25 {
  border-radius: 25rpx;
}

.radius30 {
  border-radius: 30rpx;
}

.radius35 {
  border-radius: 35rpx;
}

.radius40 {
  border-radius: 40rpx;
}

.radius45 {
  border-radius: 45rpx;
}

.radius28 {
  border-radius: 28rpx;
}

.radius24 {
  border-radius: 24rpx;
}

/* 超出自动换行 */
.flow {
  flex-flow: wrap;
}

/* 单行文本的溢出显示省略号 */
.pl {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ell {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  /* 定义显示的行数*/
  overflow: hidden;
}

/* 粗体 */
.fw-b {
  font-weight: bold !important;
}

.text-r {
  text-align: right;
}
.text-l {
  text-align: left;
}
/*7个级别的字体,默认fs-3,对比微信淘宝正常字体用16px fs-0无用*/
.fs-0 {
  font-size: 20rpx;
}

.fs-0-5 {
  font-size: 22rpx;
}

.fs-1 {
  font-size: 24rpx;
}

.fs-1-5 {
  font-size: 26rpx;
}

.fs-2 {
  font-size: 28rpx;
}

.fs-2-5 {
  font-size: 30rpx;
}

.fs-3 {
  font-size: 32rpx;
}

.fs-4 {
  font-size: 36rpx;
}

.fs-5 {
  font-size: 40rpx;
}

.fs-6 {
  font-size: 44rpx;
}

.fs-7 {
  font-size: 48rpx;
}

/* 同一行 */

.d-f {
  display: flex;
}

.d-wrap {
  flex-wrap: wrap;
}

/*换行*/
.d-f-c {
  display: flex;
  flex-direction: column;
}

/* 垂直居中 */

.d-cf {
  display: flex;
  align-items: center;
}

/* 水平居中 */

.d-c {
  display: flex;
  justify-content: center;
}

/* 垂直水平居中 */

.d-cc {
  display: flex;
  align-items: center;
  justify-content: center;
}

.d-cc-c {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

/* 垂直居中并两边对齐 */

.d-bf {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.d-bf2 {
  display: flex;
  justify-content: space-between;
}

.d-bc {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 垂直两端上下对齐 */
.d-be {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 垂直居中并右对齐 */

.d-ef {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.d-ec {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.d-ce-column {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  flex-direction: column;
}

.d-fc-sa {
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-wrap: wrap;
}

.d-fs-warp {
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
}

.d-fa-start {
  display: flex;
  align-items: flex-start;
}

/* 底部对齐 */
.d-fe {
  display: flex;
  align-items: flex-end;
}

/*以下是各种常用内外边距，缺了的补上*/

.m-5 {
  margin: 5rpx;
}

.m-10 {
  margin: 10rpx;
}

.m-15 {
  margin: 15rpx;
}

.m-20 {
  margin: 20rpx;
}

.m-25 {
  margin: 25rpx;
}

.m-30 {
  margin: 30rpx;
}

.m-35 {
  margin: 35rpx;
}

.m-40 {
  margin: 40rpx;
}

.m-45 {
  margin: 45rpx;
}

.m-50 {
  margin: 50rpx;
}

.m-55 {
  margin: 55rpx;
}

.m-60 {
  margin: 60rpx;
}

.m-65 {
  margin: 65rpx;
}

.m-70 {
  margin: 70rpx;
}

.m-75 {
  margin: 75rpx;
}

.m-80 {
  margin: 80rpx;
}

.m-85 {
  margin: 85rpx;
}

.m-90 {
  margin: 90rpx;
}

.m-95 {
  margin: 95rpx;
}

.m-100 {
  margin: 100rpx;
}

/*margin-top*/
.mt-5 {
  margin-top: 5rpx;
}

.mt-10 {
  margin-top: 10rpx;
}

.mt-15 {
  margin-top: 15rpx;
}

.mt-20 {
  margin-top: 20rpx;
}

.mt-25 {
  margin-top: 25rpx;
}

.mt-26 {
  margin-top: 25rpx;
}

.mt-30 {
  margin-top: 30rpx;
}

.mt-35 {
  margin-top: 35rpx;
}

.mt-40 {
  margin-top: 40rpx;
}

.mt-45 {
  margin-top: 45rpx;
}

.mt-50 {
  margin-top: 50rpx;
}

.mt-55 {
  margin-top: 55rpx;
}

.mt-60 {
  margin-top: 60rpx;
}

.mt-65 {
  margin-top: 65rpx;
}

.mt-70 {
  margin-top: 70rpx;
}

.mt-75 {
  margin-top: 75rpx;
}

.mt-80 {
  margin-top: 80rpx;
}

.mt-85 {
  margin-top: 85rpx;
}

.mt-90 {
  margin-top: 90rpx;
}

.mt-95 {
  margin-top: 95rpx;
}

.mt-100 {
  margin-top: 100rpx;
}

/* margin-bottom */
.mb-5 {
  margin-bottom: 5rpx;
}

.mb-10 {
  margin-bottom: 10rpx;
}

.mb-15 {
  margin-bottom: 15rpx;
}

.mb-20 {
  margin-bottom: 20rpx;
}

.mb-25 {
  margin-bottom: 25rpx;
}

.mb-30 {
  margin-bottom: 30rpx;
}

.mb-35 {
  margin-bottom: 35rpx;
}

.mb-40 {
  margin-bottom: 40rpx;
}

.mb-45 {
  margin-bottom: 45rpx;
}

.mb-50 {
  margin-bottom: 50rpx;
}

.mb-55 {
  margin-bottom: 55rpx;
}

.mb-60 {
  margin-bottom: 60rpx;
}

.mb-65 {
  margin-bottom: 65rpx;
}

.mb-70 {
  margin-bottom: 70rpx;
}

.mb-75 {
  margin-bottom: 75rpx;
}

.mb-80 {
  margin-bottom: 80rpx;
}

.mb-85 {
  margin-bottom: 85rpx;
}

.mb-90 {
  margin-bottom: 90rpx;
}

.mb-95 {
  margin-bottom: 95rpx;
}

.mb-100 {
  margin-bottom: 100rpx;
}

.ml-5 {
  margin-left: 5rpx;
}

.ml-5- {
  margin-left: -10rpx;
}

/*margin-left*/
.ml-5 {
  margin-left: 5rpx;
}

.ml-10 {
  margin-left: 10rpx;
}

.ml-15 {
  margin-left: 15rpx;
}

.ml-20 {
  margin-left: 20rpx;
}

.ml-25 {
  margin-left: 25rpx;
}

.ml-30 {
  margin-left: 30rpx;
}

.ml-35 {
  margin-left: 35rpx;
}

.ml-40 {
  margin-left: 40rpx;
}

.ml-45 {
  margin-left: 45rpx;
}

.ml-50 {
  margin-left: 50rpx;
}

.ml-55 {
  margin-left: 55rpx;
}

.ml-60 {
  margin-left: 60rpx;
}

.ml-65 {
  margin-left: 65rpx;
}

.ml-70 {
  margin-left: 70rpx;
}

.ml-75 {
  margin-left: 75rpx;
}

.ml-80 {
  margin-left: 80rpx;
}

.ml-85 {
  margin-left: 85rpx;
}

.ml-90 {
  margin-left: 90rpx;
}

.ml-95 {
  margin-left: 90rpx;
}

.ml-100 {
  margin-left: 100rpx;
}

/*margin-right*/
.mr-5 {
  margin-right: 5rpx;
}

.mr-10 {
  margin-right: 10rpx;
}

.mr-15 {
  margin-right: 15rpx;
}

.mr-20 {
  margin-right: 20rpx;
}
.mr-18 {
  margin-right: 18rpx;
}

.mr-25 {
  margin-right: 25rpx;
}

.mr-30 {
  margin-right: 30rpx;
}

.mr-35 {
  margin-right: 35rpx;
}

.mr-40 {
  margin-right: 40rpx;
}

.mr-45 {
  margin-right: 45rpx;
}

.mr-50 {
  margin-right: 50rpx;
}

.mr-55 {
  margin-right: 55rpx;
}

.mr-60 {
  margin-right: 60rpx;
}

.mr-65 {
  margin-right: 65rpx;
}

.mr-70 {
  margin-right: 70rpx;
}

.mr-75 {
  margin-right: 75rpx;
}

.mr-80 {
  margin-right: 80rpx;
}

.mr-85 {
  margin-right: 85rpx;
}

.mr-90 {
  margin-right: 90rpx;
}

.mr-95 {
  margin-right: 90rpx;
}

.mr-100 {
  margin-right: 100rpx;
}

/*padding*/
.p-5 {
  padding: 5rpx;
}

.p-10 {
  padding: 10rpx;
}

.p-15 {
  padding: 15rpx;
}

.p-20 {
  padding: 20rpx;
}

.p-25 {
  padding: 25rpx;
}

.p-30 {
  padding: 30rpx;
}

.p-35 {
  padding: 35rpx;
}

.p-40 {
  padding: 40rpx;
}

.p-45 {
  padding: 45rpx;
}

.p-50 {
  padding: 50rpx;
}

.p-55 {
  padding: 55rpx;
}

.p-60 {
  padding: 60rpx;
}

.p-65 {
  padding: 65rpx;
}

.p-70 {
  padding: 70rpx;
}

.p-75 {
  padding: 75rpx;
}

.p-80 {
  padding: 80rpx;
}

.p-85 {
  padding: 85rpx;
}

.p-90 {
  padding: 90rpx;
}

.p-95 {
  padding: 95rpx;
}

.p-100 {
  padding: 100rpx;
}

/*padding-top*/
.pt-5 {
  padding-top: 5rpx;
}

.pt-10 {
  padding-top: 10rpx;
}

.pt-15 {
  padding-top: 15rpx;
}

.pt-20 {
  padding-top: 20rpx;
}

.pt-25 {
  padding-top: 25rpx;
}

.pt-30 {
  padding-top: 30rpx;
}

.pt-35 {
  padding-top: 35rpx;
}

.pt-40 {
  padding-top: 40rpx;
}

.pt-45 {
  padding-top: 45rpx;
}

.pt-50 {
  padding-top: 50rpx;
}

.pt-55 {
  padding-top: 55rpx;
}

.pt-60 {
  padding-top: 60rpx;
}

.pt-65 {
  padding-top: 65rpx;
}

.pt-70 {
  padding-top: 70rpx;
}

.pt-75 {
  padding-top: 75rpx;
}

.pt-80 {
  padding-top: 80rpx;
}

.pt-85 {
  padding-top: 85rpx;
}

.pt-90 {
  padding-top: 90rpx;
}

.pt-95 {
  padding-top: 95rpx;
}

.pt-100 {
  padding-top: 100rpx;
}

/*padding-bottom*/
.pb-5 {
  padding-bottom: 5rpx;
}

.pb-10 {
  padding-bottom: 10rpx;
}

.pb-15 {
  padding-bottom: 15rpx;
}

.pb-20 {
  padding-bottom: 20rpx;
}

.pb-25 {
  padding-bottom: 25rpx;
}

.pb-30 {
  padding-bottom: 30rpx;
}

.pb-35 {
  padding-bottom: 35rpx;
}

.pb-40 {
  padding-bottom: 40rpx;
}

.pb-45 {
  padding-bottom: 45rpx;
}

.pb-50 {
  padding-bottom: 50rpx;
}

.pb-55 {
  padding-bottom: 55rpx;
}

.pb-60 {
  padding-bottom: 60rpx;
}

.pb-65 {
  padding-bottom: 65rpx;
}

.pb-70 {
  padding-bottom: 70rpx;
}

.pb-75 {
  padding-bottom: 75rpx;
}

.pb-80 {
  padding-bottom: 80rpx;
}

.pb-85 {
  padding-bottom: 85rpx;
}

.pb-90 {
  padding-bottom: 90rpx;
}

.pb-95 {
  padding-bottom: 95rpx;
}

.pb-100 {
  padding-bottom: 100rpx;
}

/*padding-right*/
.pr-5 {
  padding-right: 5rpx;
}

.pr-10 {
  padding-right: 10rpx;
}

.pr-15 {
  padding-right: 15rpx;
}

.pr-20 {
  padding-right: 20rpx;
}

.pr-25 {
  padding-right: 25rpx;
}

.pr-30 {
  padding-right: 30rpx;
}

.pr-35 {
  padding-right: 35rpx;
}

.pr-40 {
  padding-right: 40rpx;
}

.pr-45 {
  padding-right: 45rpx;
}

.pr-50 {
  padding-right: 50rpx;
}

.pr-55 {
  padding-right: 55rpx;
}

.pr-60 {
  padding-right: 60rpx;
}

.pr-65 {
  padding-right: 65rpx;
}

.pr-70 {
  padding-right: 70rpx;
}

.pr-75 {
  padding-right: 75rpx;
}

.pr-80 {
  padding-right: 80rpx;
}

.pr-85 {
  padding-right: 85rpx;
}

.pr-90 {
  padding-right: 90rpx;
}

.pr-95 {
  padding-right: 95rpx;
}

.pr-100 {
  padding-right: 100rpx;
}

/*padding-left*/
.pl-5 {
  padding-right: 5rpx;
}

.pl-10 {
  padding-left: 10rpx;
}

.pl-15 {
  padding-left: 15rpx;
}
.pl-16 {
  padding-left: 16rpx;
}
.pl-18 {
  padding-left: 18rpx;
}
.pl-20 {
  padding-left: 20rpx;
}

.pl-25 {
  padding-left: 25rpx;
}

.pr-30 {
  padding-left: 30rpx;
}

.pl-35 {
  padding-left: 35rpx;
}

.pl-40 {
  padding-left: 40rpx;
}

.pl-45 {
  padding-left: 45rpx;
}

.pl-50 {
  padding-left: 50rpx;
}

.pl-55 {
  padding-left: 55rpx;
}

.pl-60 {
  padding-left: 60rpx;
}

.pl-65 {
  padding-left: 65rpx;
}

.pl-70 {
  padding-left: 70rpx;
}

.pl-75 {
  padding-left: 75rpx;
}

.pl-80 {
  padding-left: 80rpx;
}

.pl-85 {
  padding-left: 85rpx;
}

.pl-90 {
  padding-left: 90rpx;
}

.pl-95 {
  padding-left: 95rpx;
}

.pl-100 {
  padding-left: 100rpx;
}

.flex_max {
  flex: 2;
}
.b-r-10 {
  border-radius: 10rpx;
}
/*垂直对齐*/
.text-c {
  text-align: center;
}

/*字体颜色*/
.f-bold {
  font-weight: 700;
}

.c-gray3 {
  color: #333333;
}
.c-gray4 {
  color: #6e6e79;
}
.c-29 {
  color: #292929;
}
.c-8a {
  color: #8a8a8a;
}

.c-37 {
  color: #373737;
}
.c-66 {
  color: #666666;
}

.c-white {
  color: #fff;
}
.c-brown {
  color: #88541f;
}
.c-74 {
  color: #747474;
}

.c-note {
  color: #858585;
}

.c-orange {
  color: #f15353;
}
.c-green {
  color: #38a526;
}
.c-8c {
  color: #8c8c8c;
}
.c-b5 {
  color: #5b5b5b;
}
.c-2c {
  color: #2c2c2c;
}
.c-66 {
  color: #666;
}

.c-f1 {
  color: #f14e4e;
}

.c-fa {
  color: #ffaa29;
}

.c-invoice {
  color: #575757;
}
.c-71 {
  color: #717171;
}

.c-grayF {
  color: #83591f;
}

.c-gray {
  color: #757272;
}

.c-20 {
  color: #202020;
}
.c-888 {
  color: #888;
}

.c-7c {
  color: #7c7c7c;
}
.c-90 {
  color: #909090;
}
.c-66A3E7 {
  color: #66a3e7;
}

/**/
.bg-white {
  background-color: #fff;
}
.bg-f5 {
  background-color: #f5f5f5;
}

.c-regular {
  color: #767676;
}

/*字体样式*/
.f-ping-fang {
  font-family: 'PingFang-SC-Bold';
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #999999;
}

.f-regular {
  font-family: 'PingFang-SC-Regular';
  font-weight: normal;
  font-stretch: normal;
  line-height: 36rpx;
  letter-spacing: 0rpx;
}

/*高度*/
.mb30 {
  height: 30rpx;
  clear: both;
}

.mb20 {
  height: 20rpx;
  clear: both;
}

.mb10 {
  height: 10rpx;
  clear: both;
}
.mb24 {
  height: 24rpx;
  clear: both;
}
.mb40 {
  height: 40rpx;
  clear: both;
}
.mb96 {
  height: 96rpx;
  clear: both;
}

.mb50 {
  height: 50rpx;
  clear: both;
}

.mb106 {
  height: 106rpx;
  clear: both;
}
.mb100 {
  height: 100rpx;
  clear: both;
}
.mb155 {
  height: 155rpx;
  clear: both;
}

.mb84 {
  height: 84rpx;
  clear: both;
}
.white-pb {
  padding: 100rpx 0;
}

.w-100 {
  width: 100vw;
}
.h-100 {
  height: 100vh;
}
/* 卡片 */
.m-card-view {
  background-color: #fff;
  padding: 25rpx 30rpx 30rpx 30rpx;
  border-radius: 10rpx;
}
.m-card-view .m-card-title {
  font-size: 28rpx;
  font-weight: 700;
}
.f {
  display: flex;
}
.f1 {
  flex: 1;
}
.fac {
  align-items: center;
}
.fjc {
  justify-content: center;
}
.fw {
  flex-wrap: wrap;
}
.fjsb {
  justify-content: space-between;
}
/* 纵向两端对齐 */
.f-c-sa {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
/* 金额划线 */
.line-through {
  text-decoration: line-through;
}
