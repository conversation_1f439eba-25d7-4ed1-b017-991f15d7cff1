<!-- 批量下单删除组件 -->
<template>
	<view>
		<u-popup :show="bulkDelShow" @close="bulkDelCancel" @open="opens" :round="20" mode="center">
			<view class="popup-main">
					<view class="title d-cc">
						<view>提示</view>
						<view class="title_close d-f" @click="bulkDelCancel">
							<view class="iconfont icon-close11 "></view>
						</view>
					</view>
					<view class="content">
						<view class="content-hint">确定要删除该数据吗?</view>
						<view class="bulk-group d-cc">
							<view class="bulk-btn" @click="bulkDelCancel">取消</view>
							<view class="bulk-btn red-btn" @click="bulkDelConfirm">确定</view>
						</view>
					</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		name:"bulkDelPopup",
		props:{
			bulkDelShow: {
				type:Boolean,
				default:false
			},
			bulkDelId: {
				type:Number,
				default:0
			}
		},
		data() {
			return {
				
			};
		},
		methods:{
			bulkDelCancel() {
				this.$emit('bulkDelCancel' ,this.bulkDelShow);
			},
			bulkDelConfirm() {
				this.$emit('bulkDelConfirm' ,this.bulkDelId);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.popup-main {
		.title {
			padding: 40rpx 0 50rpx 0;
			position: relative;
			color: #202020;
			text-align: center;
			line-height: 36rpx;
			font-size: 34rpx;
		}
		.title_close {
			position: absolute;
			// width: 50rpx;
			height: 50rpx;
			right:30rpx;
			align-items: flex-end;
			// text-align: right;
		}
		.content {
			margin: 0 40rpx;
			padding-bottom: 28rpx;
			.content-hint {
				color: #6C6C6C;
				font-size: 26rpx;
				text-align: center;
			}
			.bulk-group {
				margin-top: 72rpx;
				.bulk-btn {
					border-radius: 4rpx;
					border: solid 1rpx #a1a1a1;
					padding: 14rpx 100rpx;
					margin-right: 48rpx;
					font-size: 22rpx;
					position: relative;
					// &::before {
					// 	content: "";
					// 	position: absolute;
					// 	left:0;
					// 	top: 0;
					// 	width: 200%;
					// 	height: 200%;
					// 	border: 1px solid #a1a1a1;
					// 	/* 以（0,0）为放缩中心点 */
					// 	transform-origin: 0  0;
					// 	transform: scale(0.5);
					// }
				}
				.red-btn {
					background-color: #f14e4e;
					border:0rpx;
					color: #fff;
					&::before {
						content: "";
						position: absolute;
						left:0;
						top: 0;
						width: 200%;
						height: 200%;
						border: none;
						/* 以（0,0）为放缩中心点 */
						transform-origin: 0  0;
						transform: scale(0.5);
					}
				}
			}
		}
	}
</style>
