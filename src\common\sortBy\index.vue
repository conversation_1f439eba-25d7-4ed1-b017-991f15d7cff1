<template>
  <div>
    <div class="sort-options">
      <div
        v-for="option in items[0]"
        :key="option.value"
        :class="{ active: sortBy === option.value }"
        @click="toggleSort(option.value)"
      >
        <span class="sort-label">{{ option.label }}</span>
        <div class="sort-arrows">
          <!-- #ifdef APP-PLUS || H5 -->
          <view style="transform: scale(0.45)">
            <u-icon
              name="arrow-up-fill"
              :color="
                sortBy === option.value && sortOrder === 'asc'
                  ? '#F11111'
                  : '#666666'
              "
            />
            <u-icon
              name="arrow-down-fill"
              :color="
                sortBy === option.value && sortOrder === 'desc' ? '#F11111' : '#666666'
              "
            />
          </view>
          <!-- #endif -->
          <!-- #ifdef MP-WEIXIN-->
          <view class="d-cc-c ml-5 mt_2">
            <u-icon
              name="arrow-up-fill"
              :color="
                sortBy === option.value && sortOrder === 'asc'
                  ? '#F11111'
                  : '#666666'
              "
              size="13"
            ></u-icon>
            <u-icon
              name="arrow-down-fill"
              :color="
                sortBy === option.value && sortOrder === 'desc' ? '#F11111' : '#666666'
              "
              size="13"
            ></u-icon>
          </view>
          <!-- #endif -->
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.sort-options {
  display: flex;
  /* height: 100upx; */
  /* background: #eee; */
}

.sort-options > div {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  /* margin-left: 30rpx; */
  cursor: pointer;
}

.sort-label {
  font-size: 12px;
  color: #666666;
}

.sort-arrows {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 5px;
}

.arrow-up,
.arrow-down {
  /* #ifdef H5 */
  font-size: 6rpx;
  line-height: 10px;
  /* #endif */
  /* #ifdef MP-WEIXIN */
  font-size: 8px;
  line-height: 8px;
  /* #endif */
}

.arrow-up {
  color: #666666;
}

.arrow-down {
  color: #666666;
}

.active .sort-label {
  color: #666666;
}

.active .arrow-up {
  color: #f11111;
}

.active .arrow-down {
  color: #f11111;
}
</style>

<script>
export default {
  props: {
    items: {
      type: Array,
      required: true,
    },
    sortBy: {
      type: String,
      required: false,
    },
    sortOrder: {
      type: String,
      required: false,
    },
  },
  methods: {
    toggleSort(sortBy) {
      if (this.sortBy === sortBy) {
        this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc'
      } else {
        this.sortBy = sortBy
        this.sortOrder = 'asc'
      }
      this.$emit('sort-change', {
        sort: this.sortOrder,
        field: this.sortBy,
        sortBy: sortBy,
        sortOrder: this.sortOrder,
      })
    },
  },
}
</script>
