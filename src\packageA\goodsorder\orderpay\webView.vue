<template>
	<view>
		<web-view :src="locationHref" :progress="false"></web-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				locationHref:'',
				title:'订单支付'
			}
		},
		methods: {
			
		},
		onLoad(options) {
			console.log(options.url);
			if((options.url??'') !== '') {
				this.locationHref = options.url;
			}
		},
		onReady(){
			uni.setNavigationBarTitle({
					title:this.title
			})
		}
	}
</script>

<style>

</style>
