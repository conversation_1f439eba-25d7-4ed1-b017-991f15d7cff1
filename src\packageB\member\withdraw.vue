<!-- 提现 -->
<template>
  <view id="withdraw">
    <!-- 自定义提现头部 -->
    <!-- <view class="custom-navbar" :style="{ marginTop: heightTop + 'px' }">
      <u-icon name="arrow-left" @click="onBack" size="40rpx"></u-icon>
      <text style="font-weight: blod; font-size: 32rpx">提现</text>
      <text></text>
    </view> -->
    <view v-if="showWithdraw">
      <view class="withdraw c-20">提现金额：￥{{ withdrawal_amount }}</view>
      <view class="hint" v-if="withdrawal_type == 1">
        手续费：{{ balanceCharge }}
      </view>
      <view class="hint" v-if="withdrawal_type == 2">
        <!-- 手续费：{{ incomeCharge }} -->
        手续费：￥{{ toYuan(PoundageAmount) }} 劳务税：￥{{ toYuan(ServiceTax) }}
      </view>
      <view class="withdraw-way">
        <view class="d-cf pb_26 withdraw_manner">
          <view class="title">提现方式</view>
          <view
            v-if="withdrawal_type === 2"
            style="display: flex; align-items: center"
            @click="withdrawShow = true"
          >
            <text v-if="withdrawal_mode === 1 && manual_withdrawal === 1">手动方式</text>
            <text v-if="withdrawal_mode === 2 && convergence_payed === 1">汇聚提现</text>
            <text v-if="withdrawal_mode === 4">微信-工猫提现</text>
            <text v-if="withdrawal_mode === 5">支付宝-工猫提现</text>
            <text v-if="withdrawal_mode === 6">银行卡-工猫提现</text>
            <text v-if="withdrawal_mode === 7 && withdrawal_to_balance === 1">提现到站内余额</text>
            <text class="iconfont icon-member_right"></text>
          </view>
          <view class="way d-cf" v-if="withdrawal_type === 1">
            <view
              v-if="balance_manual_withdrawal === 1"
              class="way-withdraw d-cf"
              :class="withdrawal_mode === 1 ? 'on' : ''"
               @click="mode(1)"
            >
              <view class="way-circle">
                <view :class="withdrawal_mode === 1 ? 'circle-on' : ''"></view>
              </view>
              <text>手动方式</text>
            </view>
            <view
              v-if="balance_convergence_payed === 1"
              class="way-withdraw d-cf"
              :class="withdrawal_mode === 2 ? 'on' : ''"
              @click="mode(2)"
            >
              <view class="way-circle">
                <view :class="withdrawal_mode === 2 ? 'circle-on' : ''"></view>
              </view>
              <text>汇聚提现</text>
            </view>
          </view>
        </view>
        <view
          class="withdraw-list"
          v-if="withdrawal_mode === 4 || withdrawal_mode === 5"
        >
          <view class="withdraw-item d-bf" @click="withdrawOn">
            <view v-if="withdrawal_mode === 4">
              提现到
              <text v-if="wx_mini_openid === ''" class="withdraw_text">
                微信请完善身份证信息，姓名，微信号
              </text>
              <text v-else class="withdraw_text">微信-工猫提现</text>
            </view>
            <view v-if="withdrawal_mode === 5">
              提现到
              <text v-if="ali_account === ''" class="withdraw_text">
                支付宝请完善身份证信息，姓名，支付宝
              </text>
              <text v-else class="withdraw_text">支付宝-工猫提现</text>
            </view>
            <view class="iconfont icon-member_right"></view>
          </view>
        </view>
        <view class="withdraw-list" v-else>
          <view
            class="withdraw-item d-bf"
            @click="withdrawOn"
            v-if="
              (balance_withdrawal === 1 && withdrawal_type === 1) ||
              (income_withdrawal === 1 && withdrawal_type === 2)
            "
          >
            <view>提现到银行卡</view>
            <view>{{ bankAccount }}</view>
            <view class="iconfont icon-member_right"></view>
          </view>
        </view>
      </view>

      <view class="withdraw-btn d-cc" @click="withdrawBtn">立即提现</view>
    </view>
    <u-popup :show="show" @close="close" @open="open" :round="20">
      <view class="main">
        <view class="header d-c">
          <view class="title">选择银行卡</view>

          <view class="iconfont icon-close11 title_close" @click="close"></view>
        </view>
        <view class="content">
          <block v-for="(item, index) in cardList" :key="index">
            <view
              class="bank-item"
              @click="bankBtn(item.bank_account, item.id)"
            >
              <view>{{ item.bank_name }}</view>
              <view>{{ item.bank_account }}</view>
              <view class="c-74">{{ item.account_name }}</view>
            </view>
          </block>
          <view class="mb20"></view>
        </view>
      </view>
    </u-popup>
    <!-- 提现方式弹层 -->
    <u-popup :show="withdrawShow" :round="20">
      <view class="withdraw_main">
        <view class="withdraw_header">
          <view @click="popupCancel">取消</view>
          <view class="withdraw_title">提现方式</view>
          <view class="withdraw_confirm" @click="popupConfim">确认</view>
        </view>
        <view class="withdraw_body withdraw-way">
          <view class="d-cf pb_26">
            <view class="way d-cf popup_withdraw">
              <view
                v-if="manual_withdrawal === 1"
                class="way-item d-cf"
                :class="withdrawal_empty_mode === 1 ? 'on' : ''"
              >
                <text>手动方式</text>
                <view class="way-circle" @click="mode(1)">
                  <view
                    :class="withdrawal_empty_mode === 1 ? 'circle-on' : ''"
                  ></view>
                </view>
              </view>
              <view
                v-if="convergence_payed === 1"
                class="way-item d-cf"
                :class="withdrawal_empty_mode === 2 ? 'on' : ''"
              >
                <text>汇聚提现</text>
                <view class="way-circle" @click="mode(2)">
                  <view
                    :class="withdrawal_empty_mode === 2 ? 'circle-on' : ''"
                  ></view>
                </view>
              </view>
              <view
                v-if="withdrawal_to_balance === 1"
                class="way-item d-cf"
                :class="withdrawal_empty_mode === 7 ? 'on' : ''"
              >
                <text>提现到站内余额</text>
                <view class="way-circle" @click="mode(7)">
                  <view
                    :class="withdrawal_empty_mode === 7 ? 'circle-on' : ''"
                  ></view>
                </view>
              </view>
              <!-- <view
                class="way-item d-cf"
                :class="withdrawal_empty_mode === 4 ? 'on' : ''"
              >
                <text>微信-工猫提现</text>
                <view class="way-circle" @click="mode(4)">
                  <view
                    :class="withdrawal_empty_mode === 4 ? 'circle-on' : ''"
                  ></view>
                </view>
              </view> -->
              <view
                class="way-item d-cf"
                :class="withdrawal_empty_mode === 5 ? 'on' : ''"
                v-if="gong_mall_ali === 1 && enable === '1'"
              >
                <text>支付宝-工猫提现</text>
                <view class="way-circle" @click="mode(5)">
                  <view
                    :class="withdrawal_empty_mode === 5 ? 'circle-on' : ''"
                  ></view>
                </view>
              </view>
              <view
                class="way-item d-cf"
                :class="withdrawal_empty_mode === 6 ? 'on' : ''"
                v-if="gong_mall_bank === 1 && enable === '1'"
              >
                <text>银行卡-工猫提现</text>
                <view class="way-circle" @click="mode(6)">
                  <view
                    :class="withdrawal_empty_mode === 6 ? 'circle-on' : ''"
                  ></view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </u-popup>
    <!-- 微信或支付宝提现时提示 -->
    <u-popup mode="center" :show="withdrawalTip" :round="20" @close="tipClose">
      <view class="withdrawal_tip">
        <view class="tip_header">
          <text></text>
          <text>提示</text>
          <u-icon name="close" @click="tipCancel"></u-icon>
        </view>
        <view class="tip_main">
          <view v-if="withdrawal_mode === 4">
            请先完善资料【含身份证信息，姓名，微信号】
          </view>
          <view v-if="withdrawal_mode === 5">
            请先完善资料【含身份证信息，姓名，支付宝】
          </view>
        </view>
        <view class="tip_footer">
          <view class="tip_button">
            <u-button type="info" size="medium" @click="tipCancel">
              取消
            </u-button>
          </view>
          <view class="tip_button">
            <u-button type="error" size="medium" @click="tipConfim">
              去填写
            </u-button>
          </view>
        </view>
      </view>
    </u-popup>
    <!-- <view v-if="contractUrl">
      <web-view :src="contractUrl" />
    </view> -->
  </view>
</template>

<script>
export default {
  data() {
    return {
      enable: '1',
      gong_mall_ali: 1,
      gong_mall_bank: 1,
      showWithdraw: true,
      show: false,
      withdrawShow: false, // 提现方式弹层
      withdrawal_empty_mode: 1, // 临时存储提现方式
      withdrawalTip: false, // 微信或支付宝提现时提示
      id_card: '', // 身份证号
      ali_account: '', // 支付宝账号
      full_name: '', // 姓名
      wx_mini_openid: '', // 小程序ID
      wx_openid: '', // 公众号ID
      contractUrl: '', //电签地址
      formData: {},
      settlementBalance: 0, //结算的余额
      balance_withdrawal_charge: 0,
      incomeSetting_charge: 0,
      withdrawal_amount: 0,
      withdrawal_mode: 0, //提现方式 1手动 2汇聚 4微信 5支付宝 6银行卡
      withdrawal_type: 2, //提现类型 1结算余额提现 2收入余额提现
      balance_withdrawal: 0, // 结算余额提现,绑定银行卡按钮显示
      income_withdrawal: 0, //收入余额 提现,绑定银行卡按钮显示
      bankAccount: '',
      user_bank_id: null,
      cardList: [],
      withdrawList: [
        {
          id: 0,
          name: '提现到银行卡',
        },
        {
          id: 1,
          name: '提现到微信',
        },
        {
          id: 2,
          name: '提现到支付宝',
        },
      ],
      cardList: [],
      PoundageAmount: 0, // 手续费
      ServiceTax: 0, // 劳务税
      convergence_payed: 2, // 控制收入余额汇聚提现
      manual_withdrawal: 2, // 控制收入余额手动提现
      balance_convergence_payed: 2, // 控制结算余额汇聚提现
      balance_manual_withdrawal: 2, // 控制结算余额手动提现
      heightTop: 0, // 屏幕顶部高度
      withdrawal_to_balance: 2, // 控制站内余额提现显示或隐藏
    }
  },
  computed: {
    incomeCharge() {
      let charge = 0
      charge = this.withdrawal_amount * (this.incomeSetting_charge / 10000)
      return '￥' + charge.toFixed(2)
    },
    balanceCharge() {
      let charge = 0
      charge = this.withdrawal_amount * (this.balance_withdrawal_charge / 10000)
      return '￥' + charge.toFixed(2)
    },
  },
  onLoad(options) {
    if ((options.withdrawal_type ?? '') !== '') {
      this.withdrawal_type = parseInt(options.withdrawal_type)
    }
      this.heightTop = uni.getMenuButtonBoundingClientRect().height
  },
  onShow() {
    this.getUserInfo() // 获取个人信息
    this.getUserBankList() //银行卡列表
    if (this.withdrawal_type === 1) {
      this.getUserBalance()
    } else {
      this.getUserIncome() //收入
    }
  },
  methods: {
    // 自定义返回按钮 测试 --------误删
    // onBack() {
    //   if (this.contractUrl) {
    //     this.contractUrl = '';
    //     this.showWithdraw = true
    //   }else {
    //     this.navTo('/packageB/wallet/wallet')
    //   }
      // 跳转页面的另一种方案 测试 --------误删
      // if (this.showWithdraw) {
      //   this.navTo('/packageB/wallet/wallet')
      // } else {
      //     // this.navTo('/packageB/member/withdraw')
      //     this.contractUrl = '';
      //     this.showWithdraw = true
      // }
    // },
    close() {
      this.show = false
    },
    mode(value) {
      if (this.withdrawal_type === 1) {
        this.withdrawal_mode = value
      } else {
        this.withdrawal_empty_mode = value
      }
    },
    withdrawOn() {
      if (this.withdrawal_mode === 4 || this.withdrawal_mode === 5) {
        uni.navigateTo({
          url: '/packageB/user/personalData/personalData',
        })
      } else {
        if (this.cardList.length > 0) {
          this.show = true
        } else {
          this.toast('请先绑定银行卡')
        }
      }
    },
    bankBtn(account, id) {
      this.user_bank_id = id
      this.bankAccount = account
      this.show = false
    },
    getUserBankList() {
      //获取银行卡列表
      this.post('/api/finance/getUserBankList', {}, true)
        .then(res => {
          if (res.code === 0) {
            let data = res.data
            this.cardList = data
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    getWithdrawSetting() {
      this.post('/api/finance/getWithdrawSetting', { amount: parseInt(this.withdrawal_amount * 100) }, true)
        .then(res => {
          if (res.code === 0) {
            let data = res.data
            this.PoundageAmount = data.PoundageAmount // 手续费
            this.ServiceTax = data.ServiceTax // 劳务税 
            this.balance_withdrawal = data.balanceSetting.balance_withdrawal
            this.income_withdrawal = data.incomeSetting.income_withdrawal
            this.balance_withdrawal_charge =
              data.balanceSetting.withdrawal_charge
            this.incomeSetting_charge = data.incomeSetting.withdrawal_charge
            this.gong_mall_ali = data.incomeSetting.gong_mall_ali
            this.gong_mall_bank = data.incomeSetting.gong_mall_bank
            this.enable = data.gongMallSetting.enable
            this.convergence_payed = data.incomeSetting.convergence_payed // 控制提现类型显示或隐藏
            this.manual_withdrawal = data.incomeSetting.manual_withdrawal // 控制提现类型显示或隐藏
            this.balance_convergence_payed = data.balanceSetting.convergence_payed // 控制提现类型显示或隐藏
            this.balance_manual_withdrawal = data.balanceSetting.manual_withdrawal // 控制提现类型显示或隐藏
            this.withdrawal_to_balance = data.incomeSetting.withdrawal_to_balance // 控制站内余额提现显示或隐藏
          } else {
            // this.toast(res.msg);
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    getUserIncome() {
      // 收入信息
      this.post('/api/finance/getUserIncome', {}, true, true, false)
        .then(res => {
          if (res.code === 0) {
            let data = res.data
            this.withdrawal_amount = this.toYuan(data.income_amount)
            // this.getWithdrawSetting() //提现设置
          } else {
            // this.toast(res.msg);
          }
        })
        .catch(Error => {
          console.log(Error)
        }).finally(() => {
          this.getWithdrawSetting() //提现设置最后都执行
        });
    },
    getUserBalance() {
      this.post('/api/finance/getUserBalance', {}, true)
        .then(res => {
          if (res.code === 0) {
            let data = res.data
            this.balanceList = data
            for (let i in this.balanceList) {
              if (this.balanceList[i].type === 2) {
                this.settlementBalance = this.balanceList[i].settlement_balance //结算余额(结算余额只使用type=2的即可)
              }
            }
            this.withdrawal_amount = this.toYuan(this.settlementBalance)
            this.getWithdrawSetting() //提现设置
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          // console.log(Error);
        })
    },
    // 封装提现方法
    withdrawPackage(withdrawal_amount) {
      this.post(
        '/api/finance/withdraw',
        {
          user_bank_id: this.user_bank_id,
          withdrawal_amount: withdrawal_amount,
          withdrawal_mode: this.withdrawal_mode,
          withdrawal_type: this.withdrawal_type,
        },
        true,
        true,
      )
        .then(res => {
          if (res.code === 0) {
            let data = res.data
            setTimeout(() => {
              this.toast(res.data)
            }, 1000)
            if (this.withdrawal_type === 1) {
              this.getUserBalance()
            } else {
              this.getUserIncome() //收入
            }
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          // console.log(Error);
        })
    },
    // 封装获取电签方法 电签仅适用于工猫提现
    async getContractUrl(withdrawal_amount) {
      // this.contractUrl = 'https://www.baidu.com'
      // 当提现方式为收入提现并且提现金额大于0执行
      if (this.withdrawal_type === 2 && withdrawal_amount > 0) {
        if (this.id_card && this.full_name) {
          const res = await this.post('/api/gongmall/getContractStatus', {
            identity: this.id_card,
          })
          if (res.data.status === 'true') {
            this.showWithdraw = true
            this.withdrawPackage(withdrawal_amount)
            this.contractUrl = ''
          } else {
            this.toast(res.msg)
            // 前端遗留暂时勿动
            // this.contractUrl = res.data.url
            // 跳转到电签页面
            uni.reLaunch({
              url: '/packageB/webView/webView?src=' + encodeURIComponent(res.data.url),
            })
            // this.showWithdraw = false
          }
        } else {
          this.toast('请填写个人身份信息')
          // 延迟0.5秒跳转提示
          setTimeout(() => {
            this.navTo('/packageB/user/personalData/personalData')
          }, 500)
        }
      } else {
        this.toast('提现金额不能小于0')
      }
    },
    withdrawBtn() {
      let withdrawal_amount = this.toFen(this.withdrawal_amount)
      // 当withdrawal_mode=4微信或withdrawal_mode=5支付宝提现时查询账号是否存在否则提现到银行卡
      switch (this.withdrawal_mode) {
        case 0:
          this.toast('请选择提现方式')
          break;
        case 4:
          if (
            this.id_card !== '' &&
            this.full_name !== '' &&
            this.wx_mini_openid !== ''
          ) {
            this.getContractUrl(withdrawal_amount)
          } else {
            this.withdrawalTip = true
          }
          break
        case 5:
          if (
            this.id_card !== '' &&
            this.ali_account !== '' &&
            this.full_name !== ''
          ) {
            this.getContractUrl(withdrawal_amount)
          } else {
            this.withdrawalTip = true
          }
          break
        case 6:
          if (this.bankAccount) {
            this.getContractUrl(withdrawal_amount)
          } else {
            this.toast('请选择银行卡')
          }
          break
        case 7:
          this.withdrawPackage(withdrawal_amount)
          break
        default:
          if (this.bankAccount) {
            this.withdrawPackage(withdrawal_amount)
          } else {
            this.toast('请选择银行卡')
          }
          break
      }
    },
    // 提现方式弹层取消
    popupCancel() {
      ;(this.withdrawShow = false),
        (this.withdrawal_empty_mode = this.withdrawal_mode)
    },
    // 提现方式弹层确认
    popupConfim() {
      ;(this.withdrawShow = false),
        (this.withdrawal_mode = this.withdrawal_empty_mode)
      if (this.withdrawal_mode === 4 || this.withdrawal_mode === 5) {
        this.bankAccount = ''
        this.user_bank_id = null
      }
    },
    // 微信或支付宝提现提示
    tipCancel() {
      this.withdrawalTip = false
    },
    // 跳转到个人资料页
    tipConfim() {
      this.withdrawalTip = false
      this.withdrawOn()
    },
    getUserInfo() {
      this.post('/api/center/getProfile', {}, true).then(res => {
        ;(this.id_card = res.data.id_card),
          (this.ali_account = res.data.ali_account),
          (this.full_name = res.data.full_name),
          (this.wx_mini_openid = res.data.wx_mini_openid),
          (this.wx_openid = res.data.wx_openid)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
#withdraw {
  margin: 20rpx 20rpx 0 20rpx;
  .custom-navbar {
    width: 100%;
    height: 130rpx;
    // margin-top: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .withdraw {
    padding: 30rpx 0 30rpx 24rpx;
    background-color: #fff;
    font-size: 28rpx;
  }
  .hint {
    margin: 17rpx 0 30rpx 24rpx;
    color: #828282;
    font-size: 22rpx;
  }
  .withdraw-way {
    background-color: #fff;
    padding: 25rpx 26rpx 0 24rpx;
    .withdraw_manner {
      display: flex;
      justify-content: space-between;
    }
    .title {
      color: #202020;
      font-size: 28rpx;
      margin-right: 105rpx;
    }
    .way-item {
      width: 690rpx;
      display: flex;
      margin-top: 20rpx;
      margin-bottom: 30rpx;
      justify-content: space-between;
      // margin-right: 70rpx;
      color: #666666;
      font-size: 24rpx;
      .way-circle {
        width: 30rpx;
        height: 30rpx;
        position: relative;
        border: solid 1px #828282;
        border-radius: 50%;
        margin-right: 15rpx;
      }
    }
    .way-withdraw {
      margin-right: 70rpx;
      color: #666666;
      font-size: 24rpx;
      .way-circle {
        width: 21rpx;
        height: 21rpx;
        position: relative;
        border: solid 1px #828282;
        border-radius: 50%;
        margin-right: 15rpx;
      }
    }
    .on {
      color: #f14e4e;
      .way-circle {
        border: solid 1px #f14e4e;
      }
      .circle-on {
        width: 14rpx;
        height: 14rpx;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -55%);
        background-color: #f14e4e;
        border-radius: 50%;
      }
    }
    .withdraw-list {
      .withdraw-item {
        padding: 0 0 26rpx 0;
        .withdraw_text {
          color: #101010;
          font-weight: bold;
          font-size: 28rpx;
          margin-left: 6rpx;
        }
      }
    }
  }
  .withdraw-btn {
    width: 650rpx;
    height: 70rpx;
    margin: 0 auto;
    position: fixed;
    bottom: 25rpx;
    left: 50%;
    transform: translateX(-50%);
    background-color: #f14e4e;
    border-radius: 6rpx;
    font-size: 28rpx;
    color: #fff;
  }
  .main {
    width: 100%;
    height: 709rpx;
    border-radius: 20rpx;
    margin: 0 auto 10rpx auto;
    background: #fff;
    .header {
      width: 100%;
      height: 80rpx;
      line-height: 80rpx;
      position: relative;
      .title_close {
        position: absolute;
        right: 30rpx;
      }
    }
    .content {
      height: 629rpx;
      margin: 0 30rpx 0 30rpx;
      overflow-y: scroll;
      .bank-item {
        font-size: 24rpx;
        view {
          margin-bottom: 24rpx;
        }
      }
    }
  }
  .withdraw_main {
    width: 100%;
    // height: 500rpx;
    border-radius: 20rpx;
    margin: 0 auto 10rpx auto;
    background: #fff;
    .withdraw_header {
      padding: 36rpx;
      display: flex;
      justify-content: space-between;
      .withdraw_title {
        color: #101010;
      }
      .withdraw_confirm {
        color: #ff3a3a;
      }
    }
    .withdraw_body {
      .popup_withdraw {
        // height: 300rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    }
  }
  .withdrawal_tip {
    width: 620rpx;
    height: 380rpx;
    .tip_header {
      display: flex;
      justify-content: space-between;
      padding: 24rpx;
    }
    .tip_main {
      width: 420rpx;
      height: 100rpx;
      text-align: center;
      margin: auto;
      padding-top: 40rpx;
      padding-bottom: 40rpx;
      color: #101010;
      font-weight: bold;
    }
    .tip_footer {
      width: 310rpx;
      margin: auto;
      display: flex;
      justify-content: space-between;
      .tip_button {
        width: 120rpx;
        height: 68rpx;
      }
    }
  }
}
</style>
