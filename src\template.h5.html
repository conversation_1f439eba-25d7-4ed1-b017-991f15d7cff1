<!DOCTYPE html>
<html lang="zh-CN">

	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<title>
			<%= htmlWebpackPlugin.options.title %>
		</title>
		<script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js">
		</script>
		<script>
			window.jweixin = wx;
		</script>
		<script>
			document.addEventListener('DOMContentLoaded', function() {
				document.documentElement.style.fontSize = document.documentElement.clientWidth / 20 + 'px'
			})
			var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS
				.supports('top: constant(a)'))
			document.write(
				'<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
				(coverSupport ? ', viewport-fit=cover' : '') + '" />')
		</script>
		<link rel="stylesheet" href="<%= BASE_URL %>static/index.<%= VUE_APP_INDEX_CSS_HASH %>.css" />
		<link rel="stylesheet" href="<%= BASE_URL %>static/adapt-pc/pc.css" />
		<!-- 				<link rel="icon" href="" /> -->
	</head>

	<body>
		<noscript>
			<strong>Please enable JavaScript to continue.</strong>
		</noscript>
		<div id="app"></div>

		<!-- built files will be auto injected -->
		<!-- uni-app 电脑端兼容模板容器 -->
		<!-- <uni-adapt-pc></uni-adapt-pc> -->
		<!-- uni-app 电脑端兼容模板 -->
		<!-- 电脑端放置H5内容容器，helang-mobile-href 为固定标识不可修改 -->
		<!-- 二维码 -->
		<!-- 				<script type="text/html" id="tpl-adapt-pc">
					<div class="container">
							<iframe src="helang-mobile-href"></iframe>
							<div class="right-side">
								<div class="d-cf">
									<div class="d-cf">
										<img
											src="https://shop-yunzshop-com.oss-cn-hangzhou.aliyuncs.com/newimage/4fcbd209f8578cfb7f78ad2c96a0f789.jpeg"
											style="border-radius: 50%;height: 47.5px;width: 47.5px;" />
									</div>
									<div style="color: #515a63;font-size: 17px;margin-left: 10px;" class="">供应链中台</div>
								</div>
								
								<div style="border:1px solid #e4e4e4;width: 160px;height: 195px;margin-top: 10px;padding:10px;" class="radius20 bg-white">
									<div class="d-cc-c">
										<div style="color: #666;font-size: 15px;" >手机微信 <p style="margin-left: 7.5px;">"扫一扫"</p></div>
										<div style="margin-top: 10px;">
											<div id="qrcode" ref="qrcode"></div>
										</div>
									</div>
								</div>
							</div>
					</div>
				</script> -->
		<!-- uni-app 电脑端浏览兼容脚本文件 -->
		<!-- 				<script type="text/javascript" src="<%= BASE_URL %>static/adapt-pc/pc.js"></script>
				<script src="<%= BASE_URL %>static/adapt-pc/qrcode.js"></script> -->
		<!-- 				<script defer>
					
					let pcurl = '';
					let routes = "";
					
					window.addEventListener('message', e => {
						let routeUrl =  e.data;
						let routeSlice = null;
						let indexUrl = 'pages/index/index'
						if(Object.prototype.toString.call(routeUrl) === "[object Object]") {
							routeUrl = '/pages/index/index'
						}
						pcurl = routeUrl.slice(1);
						sessionStorage.setItem("routeUrl",'1');
						this.qrcode(pcurl);
						
					}, false)
						function qrcode(pcurl) {
							let qrcodeDom = document.getElementById("qrcode");
							if(!qrcodeDom) return
							qrcodeDom.innerHTML = "";
							new QRCode('qrcode',{
								 width: 140, // 设置宽度，单位像素
								 height: 140, // 设置高度，单位像素
								 text: window.location.href + pcurl // 设置二维码内容或跳转地址
								})
							// 
						}
							
						
				</script> -->
		<script>
			
		</script>
	</body>

</html>
