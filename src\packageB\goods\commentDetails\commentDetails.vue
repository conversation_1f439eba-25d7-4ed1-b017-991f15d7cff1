<!--商品评价列表 原型图没有对应页面 -->
<template>
	<view class="m-20 pb_30">
		<view style="background-color: #FFFFFF;" class="p-20 mb-10" v-for="(item,index) in list.list" :key="index"
			@click="toEvaluationDetails(item)">
			<view class="d-bf">
				<view class="d-f">
					<u-avatar :src="item.avatar" size="60"></u-avatar>
					<view class="ml-15">
						<view class="fs-1">{{item.nickname}}</view>
						<view style="color: #a3a3a3;" class="fs-1 mt_20">{{item.user.user_level.name}}</view>
					</view>
				</view>
				<view style="color: #a3a3a3;transform: scale(0.8);" class="fs-0">{{item.created_at}}</view>
			</view>
			<view class="mt-5">
				<uni-rate :readonly="true" v-model="item.level" :size="18" />
			</view>
			<view class="mt-10 fs-1-5">{{item.content}}</view>
			<view class="mt-30" v-if="item.imageUrls!=''">
				<view style="#525252" class="fs-1-5">晒图:</view>
				<view class="mt-15 d-f">
					<view v-for="(item1,index1) in item.imageUrls" :key="index1" :class="index1 == 0?'':'ml_15'">
						<u--image :src="item1" width="130rpx" height="130rpx" radius="8"  @click="imgListPreview(item1)"></u--image>
					</view>
				</view>
			</view>
			<view class="d-ef mb_25 mt-10" style="color: #757575;">
				<view class="pl_20 pr_20 fs-1-5 d-cc pb_3 br">
					评论数:{{item.count}}</view>
			</view>
		</view>
		<view class="d-cc fs-1 c-5e5e5e mb-25 mt-25">
			<view v-if="$store.state.homeNoMore&&list.length != 0">暂无更多~</view>
			<view v-if="list.length == 0">暂无数据~</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				goodsID: '', //店铺id
				value: 5,
				list: [], //评论详情
				goodsCommentsPage: { //评论分页
					page: 1,
					pageSize: 10
				},
			}
		},
		onLoad(opation) {
			this.goodsID = opation.goodsID //接收上个页面传过来的店铺id
			this.goodsComments();
		},
		onReachBottom() { //触底刷新
			if (!this.$store.state.homeNoMore) {
				this.goodsComments()
			}
		},
		methods: {
			goodsComments() { //获取评论
				this.get('/api/comment/list?productId=' + this.goodsID + '&page=' + this.goodsCommentsPage.page +
					'&pageSize=' + this.goodsCommentsPage.pageSize, {}, true).then(data => {
					this.list = data.data
					for (var i = 0; i < this.list.list.length; i++) { //商品评论图片转为数组存储方式
						this.list.list[i].imageUrls = data.data.list[i].imageUrls.split(",");
					}
					if (this.list.page <= this.list.total / this.list.pageSize) { //判断是不是最后一页
						this.$store.commit("upHomeNoMore", false)
						this.goodsCommentsPage.page += 1
					} else {
						this.$store.commit("upHomeNoMore", true)
					}
					for (var i = 0; i < this.list.list.length; i++) { //时间格式转换
						this.list.list[i].created_at = this.formatDateTime(this.list.list[i].created_at, 6)
					}

				})
			},
			toEvaluationDetails(item) { //跳转到评论详情页面
				let arrar = JSON.stringify(item)
				this.navTo("evaluationDetails/evaluationDetails?item=" + encodeURIComponent(arrar))
			}
		}
	}
</script>

<style>
	.br {
		border: 1rpx #b3b3b3 solid;
		border-radius: 30rpx;
	}
</style>
