module.exports = {
  env: {
    es2021: true,
    browser: true,
  },
  extends: [
    'eslint-config-standard',
    'plugin:eslint-plugin-vue/vue3-essential',
    'plugin:eslint-plugin-prettier/recommended',
  ],
  overrides: [],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
  },
  plugins: ['vue'],
  rules: {
    'vue/no-v-for-template-key-on-child': 'off',
    'vue/no-deprecated-slot-attribute': 'off',
  },
  globals: {
    uni: true,
    wx: true,
  },
}
