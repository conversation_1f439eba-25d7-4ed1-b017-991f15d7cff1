<!-- 商品详情 -->
<template>
  <view>
    <!-- 轮播图 -->
    <view>
      <u-gap bgColor="transparent" height="15"></u-gap>
      <u-swiper
        :list="list.gallery"
        :current="progressBar.currentNum"
        @change="e => (progressBar.currentNum = e.current)"
        :autoplay="false"
        indicatorStyle="right: 20px"
        height="750"
        keyName="src"
        @click="amplification"
        imgMode="widthFix"
      >
        <view slot="indicator" class="indicator-num">
          <text class="indicator-num__text">
            {{ progressBar.currentNum + 1 }}/{{ progressBar.currentSum.length }}
          </text>
        </view>
      </u-swiper>
      <!-- #ifdef H5 -->
      <view
        style="
          position: absolute;
          top: 50rpx;
          left: 10rpx;
          background-color: rgb(152, 152, 152, 0.7);
          border-radius: 50%;
          padding: 10rpx;
        "
        @click="navigateBack()"
      >
        <view class="iconfont icon-member-left c-white"></view>
      </view>
      <!-- #endif -->
    </view>
    <!-- <view>
			<swiper class="swiper" :indicator-dots="true" :autoplay="true" style="width: 750rpx;height: 750rpx">
				<swiper-item>
					<view>
						<video
							src="https://supply-chain-yunzmall.oss-cn-beijing.aliyuncs.com/2022424/1650781670ca8d8de2d37ec47e74d2698b3de34043.mp4"
							style="width: 750rpx;height: 750rpx">
						</video>
						<cover-view style="position: fixed;width:100%;height:100%;top:0;left:0;background:red">
						</cover-view>
					</view>
				</swiper-item>
				<swiper-item v-for="(item,index) in list.gallery" :key="index">
					<view>
						<u--image src="item.src"></u--image>
					</view>
				</swiper-item>
			</swiper>
		</view> -->
    <!-- 商品详情 -->
    <view class="bg-white m-20 radius15 pl_26 pr_26">
      <view class="fw-b ell pt_30">
        {{ list.title }}
      </view>
      <view class="d-bf c-959595 fs-1-5 mt_27">
        <view>
          <text>库存:</text>
          <text>{{ list.stock }}</text>
        </view>
        <view>
          <text>销量:</text>
          <text>{{ list.sales }}</text>
        </view>
      </view>
      <view>
        <view class="mt-25 pr-20" :class="myClass.top">
          <view
            class="d-cc-c"
            :class="myClass.item"
            v-if="
              page_setup_setting.details_setting.is_web_super_wholesal_price ===
              0
            "
          >
            <!-- <view class="c-f14e4e fw-b" v-if="latestUserLevel == 0" @click="jumpLogin">
							{{checkNull(user)?'¥'+list.price:'价格登录可见'}}</view> -->
            <view
              v-if="userLevelPriceTitle && isMatchingPriceAuthority(list.id)"
              class="c-f14e4e fw-b"
            >
              {{ '¥' + specificationPrice }}
            </view>
            <view v-else-if="userLevelPriceTitle" class="c-f14e4e fw-b">
              {{ '¥' + userLevelPriceTitle }}
            </view>
            <view v-else class="c-f14e4e fw-b" @click="jumpLogin">
              {{ checkNull(user) ? '¥' + specificationPrice : '价格登录可见' }}
            </view>
            <view class="mt-15 fs-2">
              {{
                page_setup_setting.language_setting.super_wholesal_price === ''
                  ? '超级批发价'
                  : page_setup_setting.language_setting.super_wholesal_price
              }}
            </view>
          </view>
          <view
            class="d-cc-c"
            :class="myClass.item"
            v-if="page_setup_setting.details_setting.is_web_price === 0"
          >
            <view
              v-if="userLevelPriceTitle && isMatchingPriceAuthority(list.id)"
              class="c-f14e4e fw-b"
            >
              <!-- {{ '¥' + list.price }} -->
              {{ '¥' + toYuan(list3.min_normal_price ? list3.min_normal_price : list3.normal_price) }}
            </view>
            <view v-else-if="userLevelPriceTitle" class="c-f14e4e fw-b">
              {{ '¥' + userLevelPriceTitle }}
            </view>
            <view v-else class="c-f14e4e fw-b" @click="jumpLogin">
              <!-- {{ checkNull(user) ? '¥' + list.price : '价格登录可见' }} -->
              {{
                checkNull(user)
                  ? '¥' + toYuan(list3.min_normal_price ? list3.min_normal_price : list3.normal_price)
                  : '价格登录可见'
              }}
            </view>
            <view class="mt-15 fs-2">
              {{
                page_setup_setting.language_setting.price === ''
                  ? '批发价'
                  : page_setup_setting.language_setting.price
              }}
            </view>
          </view>
          <view
            class="d-cc-c"
            :class="myClass.item"
            v-if="
              page_setup_setting.details_setting
                .is_web_suggested_retail_price === 0
            "
          >
            <view
              v-if="userLevelPriceTitle && isMatchingPriceAuthority(list.id)"
              class="c-f14e4e fw-b"
            >
              <!-- {{ '¥' + list.price }} -->
              {{ '¥' + toYuan(list3.origin_price) }}
            </view>
            <view v-else-if="userLevelPriceTitle" class="c-f14e4e fw-b">
              {{ '¥' + userLevelPriceTitle }}
            </view>
            <view v-else class="c-f14e4e fw-b">
              ¥{{ toYuan(list3.origin_price) }}
            </view>
            <view class="mt-15 fs-2">
              {{
                page_setup_setting.language_setting.suggested_retail_price ===
                ''
                  ? '建议零售价'
                  : page_setup_setting.language_setting.suggested_retail_price
              }}
            </view>
          </view>
        </view>
      </view>
      <view
        style="height: 1rpx; background-color: #eaeaea"
        class="mt-25"
      ></view>
      <view class="d-bf pt_29 pb_32">
        <!-- #ifdef MP-WEIXIN -->
        <button open-type="share" class="d-f">
          <view class="iconfont icon-bc_share_card c-2b2b2b d-cc"></view>
          <view class="fs-2-5 d-cc fw-b c-2b2b2b ml-10">分享</view>
        </button>
        <!-- #endif -->
        <!-- #ifdef APP-PLUS || H5 -->
        <button class="d-f" @click="$store.commit('upShadeShow', true)">
          <view class="iconfont icon-bc_share_card c-2b2b2b d-cc"></view>
          <view class="fs-2-5 d-cc fw-b c-2b2b2b ml-10">分享</view>
        </button>
        <!-- #endif -->
        <view
          class="iconfont fs-2 d-cc"
          :class="
            commodity.collectionOfGoods == 1
              ? 'icon-bc_like c-f14d4d'
              : 'icon-bc_like_normal c-2b2b2b'
          "
          style="padding-right: 125rpx; font-size: 45rpx"
          @click="enshrine(goodsID, 1)"
        >
          <text class="fs-2 ml-15 fw-b c-2b2b2b">{{ commodity.name }}</text>
        </view>
      </view>
    </view>
    <!-- 规格选择 -->
    <view class="bg-white m-20 radius15 pt_30 pl_23 pr_20 pb_28">
      <view class="d-f" @click="show = true" v-if="!aSingleSpecification">
        <view class="d-f">
          <text class="c-666666">选择</text>
          <view class="fw-b ml_22">已选：</view>
        </view>
        <view style="width: 440rpx" class="fw-b">{{ list3.title }}</view>
        <view class="iconfont icon-advertise-next mt_3 ml_35"></view>
      </view>
      <view :class="!aSingleSpecification ? 'mt_37' : ''">
        <text class="c-666666">发货</text>
        <text class="fw-b ml_22">{{ list6.city }}{{ list6.county }}</text>
      </view>
    </view>
    <!--  -->
    <!-- 评论 -->
    <view
      class="m-20 radius15 pl_26 pt_29 pb_34 bg-white pr_20"
      v-if="list4.total != 0"
    >
      <view class="d-bf">
        <view class="fw-b d-cc">
          <view class="fs-2-5">精选口碑</view>
          <view class="c-f14e4e fs-1 ml_5">({{ list4.total }})</view>
        </view>
        <view class="c-f14e4e d-c">
          <text class="fw-b fs-0-5" @click="toEvaluate()">
            好评率{{ list.level }}%
          </text>
          <view class="iconfont icon-advertise-next fs-1-5"></view>
        </view>
      </view>
      <!-- 评论内容 -->
      <view
        class="mt_32"
        v-if="list4.list && list4.list.length > 0 && commMore === false"
      >
        <view class="d-f">
          <view>
            <u--image
              :showLoading="true"
              :src="list4.list[0].avatar"
              width="70rpx"
              height="70rpx"
            ></u--image>
          </view>
          <view class="ml_17 fs-0-5">
            <view>
              <text class="c-262626">{{ list4.list[0].nickname }}</text>
              <text class="c-7b7b7b ml_23">
                {{ list4.list[0].user.user_level.name }}
              </text>
            </view>
            <view class="mt_15">
              <text class="c-7b7b7b">{{ list4.list[0].created_at }}</text>
            </view>
          </view>
        </view>
        <view style="color: #4c4c4c" class="fs-1 mt_31">
          {{ list4.list[0].content }}
        </view>
        <view class="d-f flow mt_17">
          <view
            v-for="(item1, index1) in list4.list[0].imageUrls"
            :key="index1"
            :class="index1 == 0 ? '' : 'ml_15'"
          >
            <u--image
              :src="item1"
              width="130rpx"
              height="130rpx"
              radius="8"
              @click="imgListPreview(item1)"
            ></u--image>
          </view>
        </view>
      </view>
      <block v-if="list4.list && list4.list.length > 0 && commMore">
        <view
          :class="index == 0 ? 'mt_32' : 'mt_50'"
          v-for="(item, index) in list4.list"
          :key="index"
        >
          <view class="d-f">
            <view>
              <u--image
                :showLoading="true"
                :src="item.avatar"
                width="70rpx"
                height="70rpx"
              ></u--image>
            </view>
            <view class="ml_17 fs-0-5">
              <view>
                <text class="c-262626">{{ item.nickname }}</text>
                <text class="c-7b7b7b ml_23">
                  {{ item.user.user_level.name }}
                </text>
              </view>
              <view class="mt_15">
                <text class="c-7b7b7b">{{ item.created_at }}</text>
              </view>
            </view>
          </view>
          <view style="color: #4c4c4c" class="fs-1 mt_31">
            {{ item.content }}
          </view>
          <view class="d-f flow mt_17">
            <view
              v-for="(item1, index1) in item.imageUrls"
              :key="index1"
              :class="index1 == 0 ? '' : 'ml_15'"
            >
              <u--image
                :src="item1"
                width="130rpx"
                height="130rpx"
                radius="8"
                @click="imgListPreview(item1)"
              ></u--image>
            </view>
          </view>
        </view>
      </block>
      <view class="f fjc mt_20" v-if="list4.list">
        <view class="f fac" @click="commMore = true">
          <text class="mr_10" style="color: #a5a5a5">
            {{ commMore ? '收起' : '展开' }}展开
          </text>
          <u-icon
            :name="commMore ? 'arrow-up' : 'arrow-down'"
            color="#a5a5a5"
          ></u-icon>
        </view>
      </view>
    </view>
    <!--  -->
    <!-- 商品信息 -->
    <view
      class="m-20 fs-1-5 radius15 pl_26 pt_29 pb_38 bg-white"
      v-if="checkNull(list.attrs)"
    >
      <view>商品信息</view>
      <view style="margin-top: 28rpx">
        <view v-for="(item, index) in list.attrs" :key="index">
          <view v-if="index <= merchandiseNewsShowNum" class="d-f mt-10">
            <view
              style="
                width: 159rpx;
                background-color: #f3f3f3;
                border-radius: 5rpx;
              "
              class="d-cc fs-1"
            >
              {{ item.name }}
            </view>
            <text style="width: 500rpx" class="d-cf fs-1 mr-20 ml_38">
              {{ item.value }}
            </text>
          </view>
        </view>
        <view
          class="fs-1 mt-20 c-333 d-c c-666666"
          v-if="list.attrs.length >= 10"
          @click="hiddenInformation()"
        >
          {{ merchandiseNewsShow ? '收起' : '展开' }}
          <view
            :class="
              merchandiseNewsShow
                ? 'iconfont icon-member-top ml_10 mt_2'
                : 'iconfont icon-member-bottom ml_10 mt_2'
            "
            style="font-size: 32rpx"
          ></view>
        </view>
        <!-- <view class="d-f">
					<view style="width: 159rpx;height: 170rpx;background-color: #f3f3f3;margin-top: 8rpx;"></view>
					<view style="margin-left: 38rpx;width: 442rpx;color: #3a3a3a;" class="d-cc">
						{{item.value}}
					</view>
				</view> -->
      </view>
    </view>
    <view class="m-20 fs-1-5 radius15 pl_26 pt_29 pb_38 bg-white" v-if="videoList.length > 0">
      <view class="f fac fjsb">
        <view class="fw-b fs-2-5">商品视频（{{ videoListLength }}）</view>
        <view class="f fac fs-2 mr-25" style="color: #808080;" @click="goGoodsVideo">查看全部
          <view class="iconfont icon-member_right icon_aaa"></view>
        </view>
      </view>
      <view class="mt-30 f">
        <template v-for="item in videoList">
          <view class="video-box mr-10" style="position: relative;" :key="item.id" @click="goVideo(item)">
            <u--image :src="item.cover_url" radius="16rpx" width="156rpx"
            height="156rpx"></u--image>
            <view class="icon-you-video f fac fjc">
              <view class="iconfont icon-you"></view>
            </view>
          </view>
        </template>
      </view>
    </view>
    <view class="m-20 fs-1-5 radius15 pl_26 pt_29 pb_38 bg-white" v-if="materialList.length > 0">
      <view class="f fac fjsb">
        <view class="fw-b fs-2-5">商品素材（{{ materialTotal }}）</view>
        <view class="f fac fs-2 mr-25" style="color: #808080;" @click="goGoodsMaterial">查看全部
          <view class="iconfont icon-member_right icon_aaa"></view>
        </view>
      </view>
      <view class="mt-20 f fac fjsb" @click="goProduct(materialList[0].id)">
        <view>
          <view class="f fac">
            <view>
              <u-avatar :src="user.avatar" size="64rpx" shape="circle"></u-avatar>
            </view>
            <view class="ml-10">
              <view class="black blod fs-1-5">{{ user.nickname ? user.nickname
                    : '用户' + user.username.slice(-4) }}</view>
              <view class="grey fs-1 mt-10">{{ formatDateTime(materialList[0].created_at) }}</view>
            </view>
          </view>
          <view class="mt-20 black fs-2 ell">
            {{ materialList[0].title }}
          </view>
        </view>
        <view v-if="materialList[0].img_url.length !== 0" class="mr-25 ml-20">
          <u-image :src="materialList[0].img_url[0]" width="156rpx" height="156rpx" radius="16rpx"></u-image>
        </view>
      </view>
    </view>
    <!--  -->
    <!-- 店铺信息 -->
    <view
      @tap="goStoreDetails"
      class="m-20 bg-white radius15 pl_26 pt_29 pr_27 pb_38"
    >
      <view class="d-bf">
        <view class="d-f">
          <u--image
            :showLoading="true"
            :src="list6.shop_logo"
            width="80rpx"
            height="80rpx"
          ></u--image>
          <view class="ml_16">
            <view style="color: #202020" class="fs-1-5">{{ list6.name }}</view>
            <view class="fs-0-5 d-f mt-15">
              <view class="f d-cf fs-0-5">
                <view
                  class="mr_20 p-10"
                  style="background: #f3f3f3; border-radius: 4rpx"
                >
                  <text class="c-666666">描述相符</text>
                  <text class="ml-5 c-f14e4e">{{ list6.describe_score }}</text>
                </view>
                <view
                  class="mr_20 p-10"
                  style="background: #f3f3f3; border-radius: 4rpx"
                >
                  <text class="c-666666">卖家服务</text>
                  <text class="ml-5 c-f14e4e">{{ list6.service_score }}</text>
                </view>
                <view
                  class="p-10"
                  style="background: #f3f3f3; border-radius: 4rpx"
                >
                  <text class="c-666666">物流服务</text>
                  <text class="ml-5 c-f14e4e">{{ list6.shopping_score }}</text>
                </view>
              </view>
            </view>
            <view class="f d-cf mt-30">
              <view>
                <text class="c-666666">商品总数</text>
                <text class="ml-15 c-f14e4e">{{ list6.goods_count }}</text>
              </view>
              <view class="ml-45">
                <text class="c-666666">热销(件)</text>
                <text class="ml-15 c-f14e4e">{{ list6.hot_sale }}</text>
              </view>
            </view>
          </view>
          <view class="mt-35 ml-60" @click.stop="enshrine(list.supplier_id, 2)" v-if="list.supplier_id">
            <view
              class="iconfont d-cc"
              style="font-size: 50rpx"
              :class="
                stores.whetherStoreCollects === 1
                  ? 'icon-bc_like c-f14d4d'
                  : 'icon-bc_like_normal c-2b2b2b'
              "
            ></view>
            {{ stores.name }}
          </view>
        </view>
      </view>
      <!-- <view class="d-cc mt-30">
        <view @tap="goStoreDetails">
          <view
            style="background: #f14e4e"
            class="c-white radius30 fs-0-5 pt_8 pb_8 pl_18 pr_18"
          >
            {{ list.supplier_id == 0 ? '进入平台' : '进入店铺' }}
          </view>
        </view>
        <view class="ml-40" v-if="stores.name">
          <view
            style="border: #f14e4e solid 1rpx"
            class="c-f14e4e radius30 fs-0-5 pt_5 pb_5 pl_18 pr_18"
            v-if="list.supplier_id != 0"
            @click="enshrine(list.supplier_id, 2)"
          >
            {{ stores.name }}
          </view>
        </view>
      </view> -->
    </view>
    <!--  -->
    <!-- 店铺推荐 -->
    <view>
      <view class="d-f m-20">
        <text
          style="
            width: 5rpx;
            height: 27rpx;
            background-color: #f14e4e;
            margin-top: 10rpx;
          "
        ></text>
        <view
          style="margin-left: 11rpx; color: #202020"
          class="d-f fs-2-5 d-cc ml_11"
        >
          店铺推荐
        </view>
      </view>
      <div>
        <sideTosideSetup :list="list7"></sideTosideSetup>
      </div>
    </view>
    <!--  -->
    <!-- 商品详情 -->
    <view
      class="bg-white p-20 mt-25 d-cc-c"
      v-if="checkNull(list.detail_images)"
    >
      <view style="width: 400rpx">
        <u-divider text="商品详情"></u-divider>
      </view>
      <view class="mt-15" style="width: 100%;">
        <!-- style="font-size: 0" -->
        <!-- #ifdef MP-WEIXIN -->
        <rich-text :nodes="list.detail_images"></rich-text>
        <!-- #endif -->
        <!-- #ifdef H5 -->
        <div class="ql-editor" v-html="list.detail_images">
        </div>
        <!-- #endif -->
      </view>
      <!-- 商品资质 -->
      <view class="mt-60 mb-15" v-if="checkNull(list.qualifications)">
        <view class="d-cc-c">
          <view style="width: 400rpx">
            <u-divider text="商品资质" textSize="16"></u-divider>
          </view>
        </view>
        <view class="mt-15">
          <view class="d-bf2 flow">
            <view
              v-for="(item, index) in list.qualifications"
              :key="index"
              class="mt-40"
              @click="imgListPreview(item.src)"
            >
              <view class="fs-1-5 mb_20">{{ item.title }}</view>
              <image :src="item.src" mode="widthFix" />
            </view>
          </view>
        </view>
      </view>
    </view>
    <!--  -->
    <!-- 规格选择弹窗 -->
    <view>
      <u-action-sheet
        :show="show"
        @close="show = false"
        round="30"
        title="规格选择"
      >
        <view class="p-50">
          <view class="d-f">
            <u--image
              :showLoading="true"
              width="100rpx"
              height="100rpx"
              :src="list3.image_url ? list3.image_url : list.image_url"
              radius="5"
            ></u--image>
            <view class="ml-20">
              <view>
                <view
                  v-if="
                    userLevelPriceTitle &&
                    isMatchingPriceAuthority(this.list.id)
                  "
                  style="color: #c5483d"
                  class="d-cf"
                >
                  ¥{{ specificationPrice }}
                </view>
                <view v-else style="color: #c5483d" class="d-cf">
                  ¥{{
                    userLevelPriceTitle
                      ? userLevelPriceTitle
                      : specificationPrice
                  }}
                </view>
              </view>
              <view class="c-7b7b7b fs-1 mt-10 d-cf">
                库存{{ list3.stock }}个
              </view>
              <view class="c-7b7b7b fs-1 mt-10 d-cf">
                已选择:{{ list3.title }}
              </view>
            </view>
          </view>
          <view
            class="mt-40 d-f flow"
            ref="dv1"
            id="dv1"
            :style="
              specificationsScroll ? 'height: 462rpx;overflow: scroll;' : ''
            "
          >
            <view
              v-for="(item, index) in specificationsSet"
              :key="index"
              class="d-f mt-10 mr-10"
            >
              <u-tag
                :text="item.title"
                type="error"
                size="large"
                :name="index"
                :plain="item.check"
                @click="specificationIsSelected(index)"
              ></u-tag>
            </view>
          </view>

          <view class="d-bf mt-60" style="margin-bottom: 234rpx">
            <view class="fs-1">购买数量</view>
            <u-number-box buttonSize="60" v-model="specificationsNum"></u-number-box>
          </view>
          <view class="mt-50">
            <u-button
              type="error"
              color="#f56c6c"
              text="确定"
              @click="addShoppingCart"
              v-if="popupWindowType == 'shoppingCar'"
            ></u-button>
            <u-button color="#f56c6c" type="error" text="确定" @click="buy" v-else></u-button>
          </view>
        </view>
      </u-action-sheet>
    </view>
    <!--  -->
    <!-- 底部按钮 -->
    <view
      class="position-sticky fixed-bottom d-bf fs-2 pl-20 pr_20 pt-10 pb-10 bg-white"
    >
      <view
        class="d-cf"
        :style="{
          justifyContent: addressService ? 'flex-start' : 'space-around',
        }"
      >
        <view
          style="position: relative"
          v-for="(item, index) in list2"
          :key="index"
          :class="index == 0 ? '' : 'ml-20'"
          @click="toPage(item.name)"
        >
          <view
            class="d-cc-c shopPingNum"
            v-if="item.type === 3 && addressService"
          >
            <view
              :class="item.icon"
              style="font-size: 35rpx; color: #969896"
            ></view>
            <view class="c-7e7e7e mt-10">{{ item.name }}</view>
          </view>
          <view
            class="d-cc-c shopPingNum"
            v-if="item.type === 1 || item.type === 2"
          >
            <view class="num" v-if="item.type === 2">
              <u-badge type="error" max="99" :value="shopPingNum"></u-badge>
            </view>
            <view
              :class="item.icon"
              style="font-size: 35rpx; color: #969896"
            ></view>
            <view class="c-7e7e7e mt-10">{{ item.name }}</view>
          </view>
        </view>
      </view>
      <view class="d-f" style="color: #ffffff">
        <view class="btnbg p-20 pl-55 pr-50" @click="onJudge('shoppingCar')">
          加入购物车
        </view>
        <view class="btnbg2 p-20 pl-50 pr-55" @click="onJudge('buy')">
          立即购买
        </view>
      </view>
    </view>
    <u-modal
      :show="pShow"
      :content="content"
      @confirm="confirm"
      confirmText="去升级"
      confirmColor="red"
      :showCancelButton="true"
      @cancel="pShow = false"
    ></u-modal>
    <!-- 遮罩层 -->
    <shade></shade>
    <!-- 返回顶部 -->
    <toTop :flage="flage"></toTop>
  </view>
</template>

<script>
import sideTosideSetup from '../../components/sideTosideSetup/sideTosideSetup.vue'
import toTop from '../../../common/toTop/toTop.vue'
import shade from '../../../common/shade/shade.vue'
// #ifdef H5
import 'quill/dist/quill.snow.css'
// #endif
export default {
  components: {
    sideTosideSetup,
    toTop,
    shade,
  },
  data() {
    return {
      commMore: false,
      myClass: {
        top: 'd-bf',
        item: '',
      },
      type: 0,
      discount_type: 0,
      flage: '', // 返回顶部按钮是否显示
      shopPingNum: 0,
      redirectPageUrl: '',
      aSingleSpecification: false, // 是否为单规格
      specificationsScroll: false, // 规格滚动
      goodsID: '', // 商品id
      addressService: '', // 客服地址
      page_setup_setting: {
        details_setting: {},
        details_setting: {},
        language_setting: {},
      },
      user: '', // 用户信息
      specification: '', // 规格
      show: false, // 选择规格模态框弹出
      current: 0,
      specificationsNum: 1, // 规格选择数量,
      popupWindowType: 'shoppingCar', // 弹窗类型
      skulist: [], // 保存不选择规格的商品属性
      list: [], // 商品属性
      list3: {}, // 已选择的规格
      list4: [], // 评论列表
      list6: '', // 店铺信息
      list7: [], //
      videoList: [], // 视频列表
      videoListLength: 0, // 视频列表数量
      materialList: [], // 素材列表
      materialTotal: 0, // 素材列表总数
      specificationsSet: [], // 规格集合
      merchandiseNewsShow: false, // 商品信息隐藏显示
      merchandiseNewsShowNum: 10, // 商品信息显示数量
      commodity: {
        collectionOfGoods: '', // 商品是否收藏
        name: '收藏',
      },
      stores: {
        whetherStoreCollects: '', // 店铺是否收藏
        name: '',
      },
      progressBar: {
        // 进度条
        currentNum: '', // 导航狼进度条
        currentSum: '', // 导航栏总页数
      },
      list2: [
        {
          icon: 'iconfont icon-info_store',
          name: '首页',
          type: 1,
        },
        {
          icon: 'iconfont icon-icozhuanhuan',
          name: '购物车',
          type: 2,
        },
        {
          icon: 'iconfont icon-ht_list_line_allmessage',
          name: '客服',
          type: 3,
        },
      ],

      goodsCommentsPage: {
        // 评论分页
        page: 1,
        pageSize: 10,
      },

      commodityPage: {
        // 商品推荐分页
        page: 1,
        pageSize: 6,
      },
      isPermissible: true,
      pShow: false,
      content: '当前等级暂无购买权限',
    }
  },
  watch: {
    show(newVal, oldVal) {
      this.$nextTick(() => {
        // #ifndef MP-WEIXIN
        if (this.$refs.dv1.$el.offsetHeight >= 231) {
          this.specificationsScroll = true
        }
        // #endif
        // #ifdef MP-WEIXIN
        const query = wx.createSelectorQuery()
        const that = this
        query
          .select('#dv1')
          .boundingClientRect(function (rect) {
            if (rect.height >= 231) {
              that.specificationsScroll = true
            }
          })
          .exec()
        // #endif
      })
    },
  },
  computed: {
    userLevelPriceTitle() {
      return this.$store.state.userLevelPriceTitle || ''
    },
    specificationPrice() {
      return this.toYuan(this.list3.price)
    },
    /* specificationPrice() {
      // 规格选择的超级批发价
      if (this.$store.state.discount_type === 1) {
        return this.toYuan(this.superTradePrice(this.list3.exec_price))
      } else {
        return this.$store.state.newUserData.discountRate == 0
          ? this.toYuan(this.list3.price)
          : parseInt(
              this.toYuan(
                this.toYuan(
                  this.accMul(
                    this.list3.price,
                    this.$store.state.newUserData.discountRate,
                  ),
                ),
              ) * 100,
            ) / 100
      }
    }, */
    latestUserLevel() {
      return this.$store.state.newUserData.latestUserLevel
    },
  },
  onLoad(opation) {
    this.obtainProductDetails(opation.id, opation.isProductManage)
    this.goodsID = opation.id
    this.getType()
    this.user = uni.getStorageSync('user')
    if (this.user) this.getNewUserData()
    this.framework()
    this.getDiscountType()
    this.redirectPageUrl = uni.getStorageSync('redirect_page')
    if ((this.redirectPageUrl ?? '') === '') {
      this.redirectPageUrl = ''
      if (this.user) this.shoppingList()
    } else {
      this.getBatchOrderCarts()
    }
    if (this.checkNull(this.user)) {
      this.getVideo() // 获取视频
      this.getMaterial() // 获取素材
    }
    // this.purchasePermission()
  },
  onShareAppMessage() {
    let str = ''
    if (uni.getStorageSync('user')) {
      let pid = parseInt(uni.getStorageSync('user').id);
      str = "&invite_code=" + pid
    }
    // 分享
    return {
      title: this.list.title,
      path:
        '/packageA/commodity/commodity_details/commodity_details?id=' +
        this.goodsID + str,
    }
  },
  onShareTimeline() {
    let query = ''
    if (uni.getStorageSync('user')) {
      let pid = parseInt(uni.getStorageSync('user').id);
      query = "invite_code=" + pid
    }
    // 分享朋友圈
    return {
      title: this.list.title,
      path:
        '/packageA/commodity/commodity_details/commodity_details?id=' +
        this.goodsID,
        query
    }
  },
  onHide() {
    this.show = false
  },
  onUnload() {
    this.$store.commit('upShadeShow', false)
  },
  methods: {
    async getVideo() {
      let params = {
        page: 1,
        pageSize: 4,
        product_id: parseInt(this.goodsID)
      }
      let res = await this.post('/api/video/center/list',params);
      if (res.code === 0) {
        this.videoList = res.data.list
        this.videoListLength = res.data.total
      }
    },
    // 获取素材列表
    async getMaterial() {
      const data = {
        page: 1,
        pageSize: 1,
        product_id: parseInt(this.goodsID)
      }
      const res= await this.post('/api/material/center/list', data)
        if (res.code === 0) {
          res.data.list.forEach(item => {
            if (item.img_url) {
              item.img_url_show = item.img_url.split(',').slice(0, 3)
              item.img_url = item.img_url.split(',')
            } else {
              item.img_url = []
          }
        })
        this.materialList = res.data.list
        this.materialTotal = res.data.total
      }
    },
    purchasePermission(formData) {
      // 提交订单
      this.post('/api/trade/checkout', this.formData, true, true)
        .then(res => {
          if (res.code === 2) {
            this.isPermissible = false
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    onJudge(value) {
      if (this.list.is_display === 0) {
        this.toast('该商品已下架')
        return
      }
      if (this.isPermissible) {
        this.shopingFn(value)
      } else {
        this.pShow = true
      }
    },
    confirm() {
      this.show = false
      uni.navigateTo({
        url: '/packageD/memberLevel/memberRight',
      })
    },
    shopingFn(popupWindowType) {
      if (!this.checkNull(this.user)) {
        this.toast('请先登录')
        setTimeout(() => {
          this.jumpLogin()
        }, 700)
        return
      }
      if (
        this.$store.state.userLevelPriceTitle &&
        !this.isMatchingPriceAuthority(this.list.id)
      ) {
        this.toast('暂无购买该商品权限')
        return
      }
      /**
       * (show = true), (popupWindowType = 'shoppingCar') 加入购物车
        (show = true), (popupWindowType = 'buy') 立即购买
       */
      this.show = true
      this.popupWindowType = popupWindowType
    },
    async getType() {
      const { code, data } = await this.get(
        '/api/supplier/getGoodsDetatilStoreInformation',
        { goods_id: this.goodsID },
      )
      if (code === 0) {
        this.id = data.id
        this.type = data.type
        this.list6 = data.supplier
        this.supplierType = data.type;
      }
    },
    async getDiscountType() {
      const { code, data } = await this.post('/api/user/findSetting')
      if (code === 0) {
        this.discount_type = data.setting.value.discount_type
      }
    },
    jumpLogin() {
      if (this.checkWenxin() && !this.checkNull(this.user)) {
        // 公众号登录
        this.isWeiXinBrowser()
        return
      }
      if (!this.user) {
        this.navTo('/pages/login/login')
      }
    },
    obtainProductDetails(id, isProductManage) {
      // 获取商品详情
      let api
      if (isProductManage === '1') {
        api = '/api/smallShop/product/findProduct' // 商店商品详情
      } else {
        api = this.checkNull(uni.getStorageSync('user')) ? 'api/product/getByLogin'  : '/api/product/get'
      }
      const params = {
        id,
      }
      this.get(api, params, true).then(data => {
        this.list = data.data.product
        this.skulist = {...data.data.product}
        if (data.data.product.plugin_id === 18) {
          uni.navigateTo({
            url:
              '/packageD/curriculum/curriculum_details?id=' +
              data.data.product.id,
          })
        }
        // #ifdef H5
        this.setWeixinShare(
          this.list.title,
          this.list.desc,
          this.list.image_url,
        )
        // #endif
        /* this.list.detail_images = this.list.detail_images.replace(
          /<img/g,
          '<img style="max-width:100%;height:auto;display:block;"',
        )
        this.list.detail_images = this.list.detail_images.replace(
          /figure/g,
          'div',
        ) */
        /* this.list.price = this.toYuan(new_product.price)
        this.list.normal_price = this.toYuan(new_product.normal_price)
        this.list.origin_price = this.toYuan(new_product.origin_price) */

        if (data.data.product.skus && data.data.product.skus.length > 1) {
          data.data.product.skus = data.data.product.skus.sort((a, b) => {
            return a.price - b.price
          })
        }

        this.specificationsSet = data.data.product.skus
        // 1 = 单规格 0 = 多规格  data.data.product.single_option === 1 ? data.data.product : 
        this.list3 =  data.data.product.single_option === 1 ? data.data.product :  this.specificationsSet[0] // 默认选中第一个规格
        this.progressBar.currentSum = data.data.product.gallery
        if (this.checkNull(this.list.video_url)) {
          // 判断该商品是否有视频
          this.list.gallery.unshift({
            src: this.list.video_url,
            type: 'video',
          })
        }
        for (let i = 0; i < this.specificationsSet.length; i++) {
          // 添加规格是否选中是否镂空显示
          if (i == 0) {
            this.$set(this.specificationsSet[i], 'check', false)
          } else {
            this.$set(this.specificationsSet[i], 'check', true)
          }
        }
        if (this.specificationsSet.length == 1) {
          // 判断是否为单规格
          this.aSingleSpecification = true
        } else {
          // 轮播图
          this.list.gallery = []
          this.progressBar.currentSum = [];

          if (this.list3.gallery !== null) {
            if (this.list3.gallery.length > 0) {
              this.list.gallery.push(...this.list3.gallery)
              this.progressBar.currentSum = this.list.gallery;
              if (this.list3.video_url) {
                this.list.gallery.unshift({
                    type: 'video',
                    src: this.list3.video_url,
                });
              }
            } else {
              this.list.gallery.push(...this.skulist.gallery)
              this.progressBar.currentSum = this.list.gallery;
              if (this.skulist.video_url) {
                this.list.gallery.unshift({
                    type: 'video',
                    src: this.skulist.video_url,
                });
              }
            }
          } else {
            this.list.gallery.push(...this.skulist.gallery)
            this.progressBar.currentSum = this.list.gallery;
          }
          // 商品名称
          /* if (this.list3.title) {
            this.list.title = this.list3.title
          } else {
            this.list.title = this.skulist.title
          } */
          // 属性
          if (this.list3.attrs !== null) {
            if (this.list3.attrs.length > 0) {
              this.list.attrs = this.list3.attrs
            }else{
              this.list.attrs = this.skulist.attrs
            }
          }else{
            this.list.attrs = this.skulist.attrs
          }

          // 详情
          if (this.list3.describe) {
            this.list.detail_images = this.list3.describe
          }else{
            this.list.detail_images = this.skulist.detail_images
          }
        }

        this.list.detail_images = this.list.detail_images.replace(
          /<img/g,
          '<img style="max-width:100%;height:auto;display:block;"',
        )
        this.list.detail_images = this.list.detail_images.replace(
          /figure/g,
          'div',
        )
        this.goodsComments(data.data.product.id)
        // this.storeInformation()
        this.storeIsRecommended()
        if (this.user) this.collectState(id, 1) // 获取商品收藏状态
        if (this.list.supplier_id != 0) {
          // 店铺不为平台才获取店铺的收藏状态
          if (this.user) this.collectState(this.list.supplier_id, 2)
        }
      })
    },
    goodsComments(productId) {
      // 获取评论
      this.get(
        '/api/comment/list?productId=' +
          productId +
          '&page=' +
          this.goodsCommentsPage.page +
          '&pageSize=' +
          this.goodsCommentsPage.pageSize,
        {},
        true,
      ).then(data => {
        this.list4 = data.data
        for (var i = 0; i < this.list4.list.length; i++) {
          // 商品评论图片转为数组存储方式
          this.list4.list[i].imageUrls = data.data.list[i].imageUrls.split(',')
        }
        if (this.checkNull(this.list4.list)) {
          for (var i = 0; i < this.list4.list.length; i++) {
            // 时间格式转换
            this.list4.list[i].created_at = this.formatDateTime(
              this.list4.list[i].created_at,
              5,
            )
          }
        }
      })
    },
    /* storeInformation(stair) {
      // 店铺信息
      this.get(
        '/api/supplier/saleInfo?id=' + this.list.supplier_id,
        {},
        true,
      ).then(data => {
        this.list6 = data.data.supplier
      })
    }, */
    goStoreDetails() {
      uni.navigateTo({
        url:
          '/packageB/store/storeDetails?id=' + this.id + '&type=' + this.type,
      })
    },
    // 去往商品视频
    goGoodsVideo() {
      uni.navigateTo({
        url:
          '/packageE/marketingTool/miniVideo/goodsVideo?id=' + this.goodsID
      })
    },
    // 去往商品素材
    goGoodsMaterial() {
      uni.navigateTo({
        url:
          '/packageE/marketingTool/materialCenter/goodsMaterial?id=' + this.goodsID
      })
    },
    // 跳转商品详情
    goProduct(id) {
      this.navTo(
        '/packageE/marketingTool/materialCenter/materialCenterDetail?id=' +
          id,
      )
    },
    // 商品小视频详情
    goVideo(item) {
      this.navTo('/packageE/marketingTool/miniVideo/commodityVideo?id=' + item.id + '&product_id=' + this.goodsID + '&pause=true')
    },
    addShoppingCart() {
      // 加入购物车
      const arry = {
        shopping_carts: [
          {
            qty: this.specificationsNum,
            sku_id: this.list.single_option === 1 ? this.list3.skus[0].id : this.list3.id,
          },
        ],
      }

      if (this.specificationsNum >= this.list3.stock) {
        this.showText('选择的数量大于库存啦')
        return
      }
      this.post('/api/shoppingcart/add', arry, true).then(data => {
        if (data.msg == '添加成功') {
          uni.showToast({
            title: data.msg,
            icon: 'none',
            duration: 2000,
          })

          if ((this.redirectPageUrl ?? '') === '') {
            this.redirectPageUrl = ''
            this.shoppingList() // 获取购物车数量
          } else {
            this.getBatchOrderCarts()
          }
          uni.removeStorageSync('redirect_page')
        }
      })
      this.show = false
    },
    storeIsRecommended(stair) {
      // 商品推荐
      let url = '/api/product/list'
      if (this.user) url = '/api/product/listLogin'
      this.get(
        url +
          '?page=' +
          this.commodityPage.page +
          '&pageSize=' +
          this.commodityPage.pageSize +
          '&supplier_id=' +
          this.list.supplier_id,
        {},
        true,
      ).then(data => {
        for (let i = 0; i < data.data.list.length; i++) {
          // 分转元
          data.data.list[i].price = this.toYuan(data.data.list[i].price)
          data.data.list[i].origin_price = this.toYuan(
            data.data.list[i].origin_price,
          )
        }
        this.list7 = data.data.list
      })
    },
    onPageScroll(e) {
      // 根据距离顶部距离是否显示回到顶部按钮
      if (e.scrollTop > 10) {
        // 当距离大于10时显示回到顶部按钮
        this.flage = true
      } else {
        this.flage = false
      }
    },
    collectState(value, type) {
      // 获取收藏状态
      value = parseInt(value)
      const json = {
        item_id: value,
        type,
      }
      this.post('/api/favorite/findFavorite', json, true).then(data => {
        if (type == 1) {
          this.commodity.collectionOfGoods = data.data.enable
        } else {
          if (data.data.enable == 1) {
            this.stores.name = '已收藏'
          } else {
            this.stores.name = '收藏'
          }
          this.stores.whetherStoreCollects = data.data.enable
        }
        console.log(this.stores.whetherStoreCollects,'?????')
      })
    },
    enshrine(value, type) {
      // 收藏/取消
      value = parseInt(value)
      const json = {
        item_id: value,
        type,
      }
      this.post('/api/favorite/createFavorite', json, true).then(data => {
        if (type == 1) {
          this.collectState(this.goodsID, 1)
        } else {
          this.collectState(this.list.supplier_id, 2)
        }

        if (data.data.enable == 1) {
          // 成功提示
          this.showText('已收藏')
        } else {
          this.showText('已取消')
        }
      })
    },
    toEvaluate() {
      // 跳转到全部评价页面
      this.navTo(
        '/packageB/goods/commentDetails/commentDetails?goodsID=' + this.goodsID,
      )
    },
    toPage(name) {
      // 底部页面跳转
      if (name == '首页') {
        this.tabTo('/pages/index/index')
      } else if (name == '购物车') {
        this.tabTo('/pages/shoping_car/shoping_car')
      } else if (name == '客服') {
        this.externalLinkJudgment(this.addressService)
      }
    },
    amplification(index) {
      // 浏览图片
      if (this.list.gallery[index].type == 'video') {
        return
      }
      this.imgListPreview(this.list.gallery[index].src)
    },
    getNewUserData() {
      // 获取最新的用户权重和用户折扣率
      this.post('/api/center/index', {}, true).then(data => {
        this.$store.commit('upNewUserData', {
          discountRate: data.data.user.level.discount / 100,
        })
        this.$store.commit('upNewUserData', {
          latestUserLevel: data.data.user.level_id,
        })
      })
    },
    framework() {
      // 获取客服的跳转链接
      this.get('/api/home/<USER>', {}, true).then(data => {
        this.addressService = data.data.footer.service_link
        this.page_setup_setting = data.data.page_setup_setting
        this.settingClass()
      })
    },
    settingClass() {
      if (
        this.page_setup_setting.details_setting.is_web_super_wholesal_price !==
          0 ||
        this.page_setup_setting.details_setting.is_web_price !== 0 ||
        this.page_setup_setting.details_setting
          .is_web_suggested_retail_price !== 0
      ) {
        this.myClass.top = 'f fac'
        this.myClass.item = 'mr_100'
      }
    },
    buy() {
      if (this.list.min_buy_qty <= this.specificationsNum) {
        // 点击立即购买的时候获取数据
        const json = {
          items: [
            {
              qty: this.specificationsNum,
              sku_id: this.list.single_option === 1 ? this.list3.skus[0].id : this.list3.id,
            },
          ],
        }
        this.post('/api/trade/buy', json, true).then(data => {
          this.navTo(
            '/packageA/goodsorder/goodsorder?buy_id=' + data.data.buy_id,
          )
        })
      } else {
        this.toast('最小起订量为' + this.list.min_buy_qty)
      }
    },
    specificationIsSelected(index) {
      // 规格选中
      for (let i = 0; i < this.specificationsSet.length; i++) {
        // 添加规格是否选中是否镂空显示
        if (index != i) {
          this.$set(this.specificationsSet[i], 'check', true)
        }
        this.$set(this.specificationsSet[index], 'check', false)
        this.list3 = this.specificationsSet[index]
      }
      // 判断是否为单规格
      if (this.aSingleSpecification) {
        return
      }
      // 轮播图
      this.list.gallery = []
      this.progressBar.currentSum = [];
      this.progressBar.currentNum = 0
      if (this.list3.gallery !== null) {

        if (this.list3.gallery.length > 0) {
          if (this.list3.video_url) {
            this.list.gallery.push({
                type: 'video',
                src: this.list3.video_url,
            });
          }
          this.list.gallery.push(...this.list3.gallery)
          this.progressBar.currentSum = this.list.gallery;
        } else {
          if (this.skulist.video_url) {
            this.list.gallery.push({
                type: 'video',
                src: this.skulist.video_url,
            });
          }
          this.list.gallery.push(...this.skulist.gallery)
          this.progressBar.currentSum = this.list.gallery;
        }
      } else {

        if (this.skulist.video_url) {
          this.list.gallery.push({
              type: 'video',
              src: this.skulist.video_url,
          });
        }
        this.list.gallery.push(...this.skulist.gallery)
        this.progressBar.currentSum = this.list.gallery;
      }
      // 商品名称
      /* if (this.list3.title) {
        this.list.title = this.list3.title
      } else {
        this.list.title = this.skulist.title
      } */
      // 属性
      if (this.list3.attrs !== null) {
        if (this.list3.attrs.length > 0) {
          this.list.attrs = this.list3.attrs
        }else{
          this.list.attrs = this.skulist.attrs
        }
      }else{
        this.list.attrs = this.skulist.attrs
      }

      // 详情
      if (this.list3.describe) {
        this.list.detail_images = this.list3.describe
      }else{
        this.list.detail_images = this.skulist.detail_images
      }
    },
    shoppingList() {
      this.post(
        '/api/shoppingcart/list',
        {
          checked: 0,
        },
        true,
      )
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            this.shopList = data.shopping_carts
            if (this.shopList) {
              this.shopPingNum = this.shopList.length
            }
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    getBatchOrderCarts() {
      this.get('/api/shoppingcart/getBatchOrderCarts', {}, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            this.shopList = data.shopping_carts
            if (this.shopList) {
              this.shopPingNum = this.shopList.length
            }
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    hiddenInformation() {
      // 隐藏显示商品信息
      this.merchandiseNewsShow = !this.merchandiseNewsShow
      if (this.merchandiseNewsShow == true)
        return (this.merchandiseNewsShowNum = 999)
      this.merchandiseNewsShowNum = 9
    },
  },
}
</script>

<style lang="scss" scoped>
.indicator {
  @include flex(row);
  justify-content: center;

  &__dot {
    height: 6px;
    width: 6px;
    border-radius: 100px;
    background-color: rgba(255, 255, 255, 0.35);
    margin: 0 5px;
    transition: background-color 0.3s;

    &--active {
      background-color: #ffffff;
    }
  }
}

.indicator-num {
  padding: 2px 0;
  background-color: rgba(0, 0, 0, 0.35);
  border-radius: 100px;
  width: 35px;
  @include flex;
  justify-content: center;

  &__text {
    color: #ffffff;
    font-size: 12px;
  }
}

.u-page {
  &__button-item {
    margin: 0 30px 15px 0;
  }
}

.position-sticky {
  position: -webkit-sticky !important;
  position: sticky !important;

  .shopPingNum {
    position: relative;

    .num {
      position: absolute;
      left: 56%;
      top: -5rpx;
    }
  }
}

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: calc(var(--window-bottom) + 0rpx);
  left: 0;
  z-index: 1030;
  margin-bottom: 6;
}

.btnbg {
  background: linear-gradient(to right, #ffca1d, #ff8c17);
  border-top-left-radius: 50rpx;
  border-bottom-left-radius: 50rpx;
}

.btnbg2 {
  background: linear-gradient(to right, #fe5932, #ef1225);
  border-top-right-radius: 50rpx;
  border-bottom-right-radius: 50rpx;
}

::v-deep .u-swiper__wrapper__item__wrapper__video {
  position: initial;
}

button::after {
  border: none;
}

button {
  background-color: #fff;
  border-radius: 0;
}
.ql-editor{
  padding-left: 0 !important;
  padding-right: 0 !important;
  line-height: 0 !important;
}

.video-box {
  width: 156rpx;
  height: 156rpx;
  border-radius: 16rpx;
}
.icon-you-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: #FFF;
  font-size: 30rpx;
  top: 54rpx;
  left: 54rpx;
}
.black {
  color: #161616;
}
.blod {
  font-weight: bold;
}
.grey {
  color: #808080;
}

::v-deep .u-tag--large {
  min-height: 32rpx;
  height: none;
}

::v-deep .u-tag__text {
  max-width: 600rpx;
  text-align: left;
}
</style>
