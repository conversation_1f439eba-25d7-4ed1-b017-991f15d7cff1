<!-- 公告和新手指引列表 -->
<template>
	<view class="p-30">
		<!--  -->
		<view class="mt-20">
			<view>
				<u-tabs :list="list" :scrollable="false" lineHeight="7rpx" lineWidth="130rpx" lineColor="#ee0a24"
					:activeStyle="{transform: 'scale(1)',fontWeight: 'bold',color:'#202020'}"
					:inactiveStyle="{ transform: 'scale(1)',fontWeight: 'bold',color:'#202020'}"
					itemStyle="height: 70rpx;" keyName="title" @click="classify" style="margin-left: -10%;">
				</u-tabs>
			</view>
		</view>
		<!--  -->
		<view class="mt-50">
			<view v-for="(item,index) in guideList" :key="item.id" class="mt-30" @click="toAbout(item.article_id)">
				<view class="d-bf">
					<view class="fw-b">{{item.title}}</view>
					<view>
						<u-icon name="arrow-right" color="#bdbdbd"></u-icon>
					</view>
				</view>
				<view class="brl"></view>
			</view>
		</view>
		<view class="d-cc c-7e7e7e" v-if="guideList.length == 0">暂无数据~</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [],
				guideList: [],
			}
		},
		onLoad() {
			uni.$on('refreshData',() => {
				this.getNotice();
			});
			this.getNotice()
			this.getTheTitle();
		},
		onUnload() {
		  // 移除监听事件  
		   uni.$off('refreshData');  
		},
		methods: {
			getNotice() { //获取公告
				this.get('/api/home/<USER>', {}, true).then(data => {
					this.list = data.data
					this.guideList = this.list[0].child
				})
			},
			classify(index) { //一级分类
				if (this.checkNull(index.child)) {
					this.guideList = index.child
				}else{
					this.guideList = []
				}
			},
			getTheTitle(){ //获取页面标题
				this.get('/api/home/<USER>', {}, true).then(data => {
					uni.setNavigationBarTitle({
						title: data.data.tab.title
					})
				})
			},
			toAbout(id) {
				this.navTo('noticeDetails?id=' + id)
			}
		}
	}
</script>
<style lang="scss" scoped>
	page {
		background-color: #fff;
	}
	.brl {
		background-color: #F6F6F6;
		width: 750rpx;
		height: 5rpx;
		margin-top: 30rpx;
		margin-left: -30rpx;
	}
</style>
