<template>
  <view>
    <view class="top">
      <view style="height: 130rpx"></view>
      <view class="img-user">
        <u-avatar :src="user.avatar" size="128rpx" shape="circle"></u-avatar>
      </view>
      <view class="top-user">
        <span class="user-name">
          {{ user.nickname ? user.nickname : '用户' + user.username.slice(-4) }}
        </span>
      </view>
    </view>
    <!-- <view class="divider"></view> -->
    <view class="f fac fjsb tabs">
      <template v-for="item in tabsList">
        <view
          class="f fac tabs-box"
          @click="changeTabs(item)"
          :key="item.status"
        >
          <view
            class="tabname"
            :style="{ color: item.status === status ? '#00001C' : '#3B3B4A' }"
          >
            {{ item.name }}
          </view>
          <view v-if="item.status === status" class="line"></view>
          <view v-else class="line-false"></view>
        </view>
      </template>
    </view>
    <view class="box" v-if="tableList.length">
      <view class="material-box" v-for="item in tableList" :key="item.id">
        <view class="material-item f fac fjsb">
          <view class="f fac pl_25 pt_30">
            <u-image
              :src="item.user.avatar"
              width="80rpx"
              height="80rpx"
              shape="circle"
            ></u-image>
            <view class="ml_15">
              <view class="fs-2-5 blod black">{{ item.user.nickname }}</view>
              <view class="mt_10 grey fs-1">
                {{ formatDateTime(item.created_at) }}
              </view>
            </view>
          </view>
          <view v-if="item.status == 1" class="info-view1 mr_25">审核中</view>
          <view v-if="item.status == 2" class="info-view2 mr_25">已发布</view>
          <view v-if="item.status == 3" class="info-view3 mr_25">
            审核不通过
          </view>
        </view>
        <view class="pl_25 pr_25 mt_40 black blod fs-3">
          {{ item.title }}
        </view>
        <view class="pl_25 pr_25 mt_35 black fs-2-5">
          {{ item.content }}
        </view>
        <view class="pl_25 pr_25 pb_30 mt_40 f fac">
          <view
            v-for="(items, index) in item.img_url_show"
            :key="index"
            class="pr-10"
            @click="downImage(items)"
          >
            <u-image
              :src="items"
              width="210rpx"
              height="210rpx"
              radius="16rpx"
            ></u-image>
          </view>
          <video
            v-if="item.video_url && item.img_url.length < 3"
            style="width: 200rpx; height: 200rpx"
            :src="item.video_url"
            :controls="false"
          />
        </view>
        <view
          class="d-ef pr_25 mt_30 blue"
          @click="goMaterialCenterDetail(item)"
        >
          <view>查看详情</view>
          <u-icon name="arrow-right" color="#2658FA"></u-icon>
        </view>
        <view
          v-if="item.status == 3"
          class="rejected_reason mt_40 ml_25 mr_25 f"
        >
          <view style="min-width: 180rpx">不通过原因：</view>
          <view>
            <u-parse :content="item.rejected_reason"></u-parse>
          </view>
        </view>
      </view>
    </view>
    <view v-else class="material-empty">
      <img src="https://yxgyl.obs.cn-south-1.myhuaweicloud.com/2025718/05487f7ffa0937af1892d4bcf10c0a20" />
      <view>暂无相关素材</view>
    </view>
    <view
      class="pb_30"
      v-if="
        tableList.length === total && tableList.length !== 0 && status === 0
      "
    >
      <u-divider textSize="30" text="已经到底了"></u-divider>
    </view>
    <view
      class="pb_30"
      v-if="
        tableList.length === auditTotal &&
        tableList.length !== 0 &&
        status === 1
      "
    >
      <u-divider textSize="30" text="已经到底了"></u-divider>
    </view>
    <view
      class="pb_30"
      v-if="
        tableList.length === adoptTotal &&
        tableList.length !== 0 &&
        status === 2
      "
    >
      <u-divider textSize="30" text="已经到底了"></u-divider>
    </view>
    <view
      class="pb_30"
      v-if="
        tableList.length === rejectTotal &&
        tableList.length !== 0 &&
        status === 3
      "
    >
      <u-divider textSize="30" text="已经到底了"></u-divider>
    </view>

    <view class="btn-box">
      <view class="add-box" @click="addMaterial">
        <view class="iconfont icon-fabu"></view>
        <view class="mt_5">发布</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      user: {},
      tabsList: [
        {
          name: '全部',
          status: 0,
        },
        {
          name: '审核中',
          status: 1,
        },
        {
          name: '已发布',
          status: 2,
        },
        {
          name: '审核不通过',
          status: 3,
        },
      ],
      status: 0,
      tableList: [],
      page: 1,
      pageSize: 10,
      total: null,
      auditTotal: null,
      adoptTotal: null,
      rejectTotal: null,
    }
  },
  async onReachBottom() {
    const data = {
      status: this.status,
      page: ++this.page,
      pageSize: this.pageSize,
    }
    if (this.tableList.length < this.total) {
      const res = await this.post('/api/material/self/list', data)
      if (res.code === 0) {
        res.data.list.forEach(item => {
          if (item.img_url) {
            item.img_url_show = item.img_url.split(',').slice(0, 3)
            item.img_url = item.img_url.split(',')
          } else {
            item.img_url = []
          }
        })
        this.tableList = this.tableList.concat(res.data.list)
      }
    }
  },
  onLoad() {
    this.getMaterialList()
    this.user = uni.getStorageSync('user')
  },
  methods: {
    // 获取我的素材列表
    async getMaterialList() {
      const data = {
        status: this.status,
        page: this.page,
        pageSize: this.pageSize,
      }
      const res = await this.post('/api/material/self/list', data)
      if (res.code === 0) {
        res.data.list.forEach(item => {
          if (item.img_url) {
            item.img_url_show = item.img_url.split(',').slice(0, 3)
            item.img_url = item.img_url.split(',')
          } else {
            item.img_url = []
          }
        })
        this.tableList = res.data.list
        this.total = res.data.total
        this.auditTotal = res.data.auditTotal
        this.adoptTotal = res.data.adoptTotal
        this.rejectTotal = res.data.rejectTotal
        this.tabsList[0].name = `全部(${res.data.total})`
        this.tabsList[1].name = `审核中(${res.data.auditTotal})`
        this.tabsList[2].name = `已发布(${res.data.adoptTotal})`
        this.tabsList[3].name = `审核不通过(${res.data.rejectTotal})`
      }
    },
    // 切换审核状态
    changeTabs(e) {
      this.status = e.status
      this.page = 1
      this.getMaterialList()
    },
    // 跳转发布素材
    addMaterial() {
      this.navTo('/packageE/marketingTool/materialCenter/addMaterial')
    },
    // 跳转详情
    goMaterialCenterDetail(item) {
      this.navTo(
        '/packageE/marketingTool/materialCenter/myMaterialDetail?id=' + item.id,
      )
    },
  },
}
</script>

<style lang="scss" scoped>
.top {
  height: 360rpx;
  width: 100%;
  background-image: linear-gradient(180deg, #2658fa 0%, #f5f5f5 100%);
  position: relative;
  box-sizing: border-box;
  .img-user {
    width: 128rpx;
    height: 128rpx;
    border-radius: 50%;
    position: absolute;
    background-color: #ffffff;
    padding: 6rpx;
    top: 160rpx;
    left: 24rpx;
  }
  .top-user {
    height: 122rpx;
    background: #f5f5f5;
    border-radius: 30rpx 30rpx 0rpx 0rpx;
    padding-top: 22rpx;
    box-sizing: border-box;
    margin-top: 100rpx;
    .user-name {
      margin-left: 177rpx;
      // padding-top: 24rpx;
      color: #00001c;
      font-size: 40rpx;
      font-weight: bold;
    }
  }
}
.divider {
  width: 702rpx;
  height: 2rpx;
  background: #e9e9ec;
  margin: 4rpx auto 0;
}
.tabs {
  width: 680rpx;
  height: 32rpx;
  margin: 37rpx auto;
  .tabs-box {
    font-size: 28rpx;
    margin: 0 auto;
    flex-direction: column;
    .tabname {
      margin-bottom: 12rpx;
    }
    .line {
      width: 34rpx;
      height: 6rpx;
      background-color: #2658fa;
      border-radius: 3rpx;
    }
    .line-false {
      width: 34rpx;
      height: 6rpx;
      border-radius: 3rpx;
    }
  }
}
.box {
  padding-bottom: 100rpx;
}
.material-box {
  width: 702rpx;
  background-color: #ffffff;
  margin-top: 20rpx;
  border-radius: 20rpx;
  margin-left: 24rpx;
  padding-bottom: 35rpx;
  .material-item {
    .info-view1 {
      padding: 10rpx 20rpx;
      background-color: rgb(249, 109, 32, 0.1);
      border-radius: 12rpx;
      color: #f96d20;
    }
    .info-view2 {
      padding: 10rpx 20rpx;
      background: #f0f0f1;
      border-radius: 12rpx;
      color: #232426;
    }
    .info-view3 {
      padding: 10rpx 20rpx;
      background-color: rgba(241, 83, 83, 0.1);
      border-radius: 12rpx;
      color: #f15353;
    }
  }
}
.material-empty {
  position: absolute;
  top: 34%;
  left: 34%;
  color: #6e6e79;
  img {
    width: 230rpx;
    height: 230rpx;
  }
  view {
    margin-top: 23rpx;
    margin-left: 32rpx;
  }
}
.rejected_reason {
  width: 654rpx;
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 24rpx 32rpx 24rpx 24rpx;
  box-sizing: border-box;
}
.btn-box {
  position: fixed;
  text-align: center;
  bottom: 100rpx;
  right: 60rpx;
  .add-box {
    width: 96rpx;
    height: 96rpx;
    border-radius: 50%;
    background-color: #2658fa;
    color: #ffffff;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }
}
.black {
  color: #161616;
}
.blod {
  font-weight: bold;
}
.grey {
  color: #808080;
}
.blue {
  color: #2658fa;
}
</style>
