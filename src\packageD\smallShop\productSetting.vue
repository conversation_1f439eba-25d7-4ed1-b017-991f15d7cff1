<template>
  <view class="relatvie">
    <view class="b-r-10 m-20 p-40 bg-white">
      <view class="fs-3 mb_30">定价策略</view>
      <view>
        <u-row gutter="20">
          <u-col span="2">
            <text>销售价</text>
          </u-col>
          <!-- radio group start -->
          <u-col span="4">
            <u-radio-group
              placement="column"
              v-model="takeGroup"
              iconSize="24rpx"
            >
              <u-radio
                size="40"
                activeColor="#f15353"
                labelSize="28rpx"
                labelColor="#202020"
                :customStyle="{ marginBottom: '34rpx' }"
                v-for="(item, index) in takeValues"
                :key="index"
                :label="item.name"
                :name="item.value"
                @change="takeChange(item.name, item.value)"
              ></u-radio>
            </u-radio-group>
          </u-col>
          <!-- radio group end -->
          <!-- input group start -->
          <u-col span="6">
            <view class="d-bf">
              <u-input
                placeholder="协议价比例"
                border="surround"
                v-model="formData.agreement_price_ratio"
                fontSize="24rpx"
                type="number"
              ></u-input>
              <view class="ml_30">%</view>
            </view>
            <view class="d-bf">
              <u-input
                placeholder="建议零售价比例"
                border="surround"
                v-model="formData.origin_price_ratio"
                fontSize="24rpx"
                type="number"
              ></u-input>
              <view class="ml_30">%</view>
            </view>
            <view class="d-bf">
              <u-input
                placeholder="指导价比例"
                border="surround"
                v-model="formData.guide_price_ratio"
                fontSize="24rpx"
                type="number"
              ></u-input>
              <view class="ml_30">%</view>
            </view>
            <view class="d-bf">
              <u-input
                placeholder="营销价比例"
                border="surround"
                v-model="formData.activity_price_ratio"
                fontSize="24rpx"
                type="number"
              ></u-input>
              <view class="ml_30">%</view>
            </view>
          </u-col>
          <!-- radio group end -->
        </u-row>
      </view>
      <view class="mt_20">
        <view class="mt_10 fs-1 c-8a">
          注意：技术服务比例为{{ server_ratio }}% 默认为建议零售价*100%
        </view>
        <view class="mt_10 fs-1 c-8a">
          如设置协议价*130%，则商品的销售价格为该商品协议价*130%；利润
          为协议价*（130%-100%-3%）；利润包含提现手续费！
        </view>
      </view>
    </view>
    <view class="b-r-10 d-f-c m-20 mt_30 p-40 bg-white">
      <view class="fs-3 mb_30">定价策略</view>
      <view class="d-cf">
        <text class="w20">开启提示窗</text>
        <view>
          <u-switch
            v-model="formData.tip"
            activeColor="#f56c6c"
            :activeValue="1"
            :inactiveValue="0"
          ></u-switch>
        </view>
      </view>
      <view class="mt_20">
        <view class="mt_10 fs-1 c-8a">
          关闭后选品直接按照定价策略导入商品，不在弹出确认和改价弹窗。
        </view>
      </view>
    </view>
    <view class="con-fixed">
      <view>
        <u-button type="error" text="保存" @click="onSubmit"></u-button>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  data() {
    return {
      server_ratio: 0,
      settingId: null,
      takeGroup: 'is_origin_price',
      takeValues: [
        {
          name: '协议价',
          value: 'is_agreement_price',
        },
        {
          name: '建议零售价',
          value: 'is_origin_price',
        },
        {
          name: '指导价',
          value: 'is_guide_price',
        },
        {
          name: '营销价',
          value: 'is_activity_price',
        },
      ],
      formData: {
        is_agreement_price: 0, // 协议价是否勾选
        is_origin_price: 0, // 建议零售价是否勾选
        is_guide_price: 0, // 指导价是否勾选
        is_activity_price: 0, // 营销价是否勾选
        agreement_price_ratio: 100, // 协议价比例
        origin_price_ratio: 100, // 建议零售价比例
        guide_price_ratio: 100, // 指导价比例
        activity_price_ratio: 100, // 营销价比例
        tip: 1, // 提示是否开启
      },
    }
  },
  onShow() {
    this.getInit()
  },
  methods: {
    takeChange(name, value) {
      this.formData.is_agreement_price = 0
      this.formData.is_origin_price = 0
      this.formData.is_guide_price = 0
      this.formData.is_activity_price = 0
      this.formData[value] = 1
    },
    onSubmit() {
      if(!this.takeGroup){
        this.toast("请选择定价策略")
        return
      }
      if(this.formData.agreement_price_ratio < 100){
        this.toast("输入不能小于100")
        return
      }
      if(this.formData.origin_price_ratio < 100){
        this.toast("输入不能小于100")
        return
      }
      if(this.formData.guide_price_ratio < 100){
        this.toast("输入不能小于100")
        return
      }
      if(this.formData.activity_price_ratio < 100){
        this.toast("输入不能小于100")
        return
      }
      switch(this.takeGroup){
        case "is_activity_price":
          this.formData.is_activity_price = 1
          break;
        case "is_agreement_price":
            this.formData.is_agreement_price = 1
          break;
        case "is_guide_price":
            this.formData.is_guide_price = 1
          break;
        case "is_origin_price":
            this.formData.is_origin_price = 1
          break;
      }
      const params = {
        id: this.settingId,
        value: {
          ...this.formData,
          agreement_price_ratio: Number(this.formData.agreement_price_ratio),
          origin_price_ratio: Number(this.formData.origin_price_ratio),
          guide_price_ratio: Number(this.formData.guide_price_ratio),
          activity_price_ratio: Number(this.formData.activity_price_ratio),
        },
      }
      const api = '/api/smallShop/setting/updateShopProductSetting' // 更新定价策略
      this.put(api, params, true)
        .then(res => {
          this.toast(res.msg)
          setTimeout(() => {
            uni.navigateBack({
              delta: 1,
            })
          }, 500)
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    getInit() {
      const api = '/api/smallShop/setting/getShopProductSetting'
      this.get(api)
        .then(res => {
          if (res.code === 0) {
            this.server_ratio = res.data.server_ratio / 100
            res.data.setting.value.activity_price_ratio = res.data.setting.value.activity_price_ratio < 100 ? 100 : res.data.setting.value.activity_price_ratio
            res.data.setting.value.agreement_price_ratio = res.data.setting.value.agreement_price_ratio < 100 ? 100 : res.data.setting.value.agreement_price_ratio
            res.data.setting.value.guide_price_ratio = res.data.setting.value.guide_price_ratio < 100 ? 100 : res.data.setting.value.guide_price_ratio
            res.data.setting.value.origin_price_ratio = res.data.setting.value.origin_price_ratio < 100 ? 100 : res.data.setting.value.origin_price_ratio
            Object.assign(this.formData, res.data.setting.value)
            this.settingId = res.data.setting.id
            // this.takeGroup
            let value = res.data.setting.value
            if(value.is_activity_price){
              this.takeGroup = 'is_activity_price'
            }
            if(value.is_agreement_price){
              this.takeGroup = 'is_agreement_price'
            }
            if(value.is_guide_price){
              this.takeGroup = 'is_guide_price'
            }
            if(value.is_origin_price){
              this.takeGroup = 'is_origin_price'
            }
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
  },
}
</script>
<style scoped>
::v-deep .u-input {
  margin-bottom: 20rpx;
  height: 40rpx;
  padding: 6rpx 18rpx !important;
}
::v-deep .u-col {
  align-self: flex-start;
}
</style>
<style lang="scss" scoped>
.w20 {
  width: 20vw;
}
.relatvie {
  position: relative;
  height: 100vh;
}
.con-fixed {
  position: fixed;
  bottom: 0;
  padding: 3vw;
  width: 94vw;
  background: white;
}
</style>
