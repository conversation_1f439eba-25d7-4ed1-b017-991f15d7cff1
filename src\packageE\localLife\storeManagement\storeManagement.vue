<template>
  <view>
    <view class="container">
      <!-- 信息卡 -->
      <view class="back_drop">
        <!-- 信息 -->
        <view class="message f d-bf">
          <view class="f fac">
            <view class="ml_35 pt_30">
              <u-image
                width="110"
                height="110"
                shape="circle"
                :src="store_data.user.avatar"
              ></u-image>
            </view>
            <view class="pt_30 ml_15">
              <view class="fontSize32 white">{{ store_data.name }}</view>
              <view class="f fac mt_5">
                <view class="iconfont icon-home-line6 white fontSize26"></view>
                <view class="ml_10 white fontSize26">店主</view>
              </view>
            </view>
          </view>
          <view class="change-btn mt_30 mr_35 f d-cf d-c" @click="changeStore">
            <view class="white fontSize26">切换</view>
            <view class="iconfont icon-xia white"></view>
          </view>
        </view>
        <!-- 门店操作 -->
        <view class="store_operate">
          <u-grid :border="false" col="4">
            <u-grid-item @click="scanCode">
              <view class="iconfont icon-icon_scan iconf-img"></view>
              <text class="grid-text fs-1 mt_10">扫码验销</text>
            </u-grid-item>
            <u-grid-item @click="verifyNews">
              <view class="iconfont icon-shumayanquan iconf-img"></view>
              <text class="grid-text fs-1 mt_10">输码验销</text>
            </u-grid-item>
            <u-grid-item
              @click="
                navTo(
                  `/packageE/localLife/verificationDetail/verificationDetail?store_id=${store_id}`,
                )
              "
            >
              <view class="iconfont icon-hexiaomingxi iconf-img"></view>
              <text class="grid-text fs-1 mt_10">核销明细</text>
            </u-grid-item>
            <u-grid-item
              @click="
                navTo(
                  `/packageE/localLife/saleDetail/saleDetail?storeId=${store_id}`,
                )
              "
            >
              <view class="iconfont icon-shoumaimingxi iconf-img"></view>
              <text class="grid-text fs-1 mt_10">售卖明细</text>
            </u-grid-item>
            <u-grid-item
              class="mt_10"
              @click="
                navTo(
                  `/packageE/localLife/productManage/productManage?store_id=${store_id}`,
                )
              "
            >
              <view class="iconfont icon-shangpin1 iconf-img"></view>
              <text class="grid-text fs-1 mt_10">商品管理</text>
            </u-grid-item>
            <u-grid-item
              class="mt_10"
              @click="
                navTo(
                  `/packageE/localLife/verificationerManage/verificationerManage?store_id=${store_id}`,
                )
              "
            >
              <view class="iconfont icon-hexiaoyuan iconf-img"></view>
              <text class="grid-text fs-1 mt_10">核销员管理</text>
            </u-grid-item>
            <u-grid-item class="mt_10" @click="navToIncome">
              <view class="iconfont icon-manefanxian iconf-img"></view>
              <text class="grid-text fs-1 mt_10">收益管理</text>
            </u-grid-item>
          </u-grid>
        </view>
      </view>
      <!-- 数据统计 -->
      <view class="data_count">
        <view class="count-title f fac d-bf">
          <view class="ml_25 mt_25 fs-2-5 count">数据统计</view>
          <view class="date-time f fac mt_25">
            <view
              class="today"
              :class="{ 'choose-day': chooseDate === 'today' }"
              @click="chooseDay('today')"
            >
              今日
            </view>
            <view
              class="yesterday"
              :class="{ 'choose-day': chooseDate === 'yesterday' }"
              @click="chooseDay('yesterday')"
            >
              昨日
            </view>
            <view
              class="all"
              :class="{ 'choose-day': chooseDate === 'all' }"
              @click="chooseDay('all')"
            >
              全部
            </view>
          </view>
        </view>
        <view class="data_number">
          <u-grid :border="false" col="3">
            <u-grid-item>
              <text class="count fs-2-5">
                {{ orderData.verified_order }}
              </text>
              <text class="grid-text grey mt_10">核销订单数</text>
            </u-grid-item>
            <u-grid-item>
              <text class="count fs-2-5">
                {{ toYuan(orderData.wait_settled_income) }}
              </text>
              <text class="grid-text grey mt_10">未结算收益</text>
            </u-grid-item>
            <u-grid-item>
              <text class="count fs-2-5">
                {{ toYuan(orderData.settled_income) }}
              </text>
              <text class="grid-text grey mt_10">已结算收益</text>
            </u-grid-item>
            <u-grid-item>
              <text class="count fs-2-5 mt_20">
                {{ orderData.wait_verified_order }}
              </text>
              <text class="grid-text grey mt_10">待核销订单数</text>
            </u-grid-item>
            <u-grid-item>
              <text class="count fs-2-5 mt_20">
                {{ orderData.pay_order_count }}
              </text>
              <text class="grid-text grey mt_10">支付订单数</text>
            </u-grid-item>
            <u-grid-item>
              <text class="count fs-2-5 mt_20">
                {{ orderData.refund_order_count }}
              </text>
              <text class="grid-text grey mt_10">退款订单数</text>
            </u-grid-item>
            <u-grid-item>
              <text class="count fs-2-5 mt_20">
                {{ orderData.sale_product_count }}
              </text>
              <text class="grid-text grey mt_10">在售商品</text>
              <text class="tip">在售商品不随周期变化</text>
            </u-grid-item>
          </u-grid>
        </view>
      </view>
    </view>
    <store-popup
      ref="storePopup"
      @change-store-other="changeStoreOther"
    ></store-popup>
    <u-popup :round="30" :show="newsShow" @close="close">
      <view class="code-box">
        <view class="f fjsb fac verifyNews-title">
          <view class="title">输码验券</view>
          <view class="iconfont icon-close11" @click="close"></view>
        </view>
        <view class="con-verifyNews">
          <view>
            <u-input
              placeholder="请输入券码"
              border="surround"
              fontSize="28rpx"
              customStyle="height: 104rpx;"
              v-model="value"
            ></u-input>
          </view>
          <view style="margin-top: 108rpx">
            <u-button
              customStyle="height: 80rpx;font-size:30rpx"
              type="error"
              size="small"
              shape="circle"
              text="确认"
              @click="onVerifyNews"
            ></u-button>
          </view>
        </view>
      </view>
    </u-popup>
    <!-- 消息提示 -->
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import storePopup from './components/storePopup.vue'

export default {
  name: 'storeManagement',
  components: { storePopup },

  data() {
    return {
      newsShow: false,
      value: '', // 券码
      chooseDate: 'all', // 默认选中全部
      type: 0, // 0-全部 1-今天 2-昨天
      store_id: 0, // 核销门店ID
      store_data: {}, // 门店信息
      orderData: {}, // 门店数据统计
    }
  },
  mounted() {
    this.getLocalLifeUse()
  },
  onLoad() {
    // 只能在wx浏览器上使用
    if (this.checkWenxin()) {
      const url = window.location.href.split('#')[0]
      this.get('/api/wechatofficial/getJsConfig', {
        url,
      }).then(res => {
        const data = res.data
        jweixin.config({
          debug: false,
          appId: data.app_id,
          timestamp: data.timestamp,
          nonceStr: data.nonce_str,
          signature: data.signature,
          jsApiList: ['scanQRCode'],
        })
      })
    }
  },
  methods: {
    // 获取门店数据
    async getLocalLifeUse() {
      const res = await this.get('/api/localLife/front/getLocalLifeUse')
      if (res.code === 0) {
        this.store_id = res.data.store.id
        this.store_data = res.data.store
        this.getBrandStatistics(res.data.store.id)
      }
    },
    // 获取门店订单数据
    async getBrandStatistics(store_id) {
      const params = {
        store_id: parseInt(store_id),
        type: parseInt(this.type),
      }
      const res = await this.get(
        '/api/localLife/front/getBrandStatistics',
        params,
      )
      if (res.code === 0) {
        this.orderData = res.data
      }
    },
    // 切换门店按钮
    changeStore() {
      this.$refs.storePopup.storeShow = true
      this.$refs.storePopup.getLocalLifeUseStores(this.store_id)
    },
    // 切换其他门店
    async changeStoreOther(storeId, index) {
      const res = await this.get('/api/localLife/front/getLocalLifeUseStores')
      if (res.code === 0) {
        this.store_data = res.data[index]
        this.store_id = storeId
        this.getBrandStatistics(storeId)
      }
    },
    // 切换日期
    chooseDay(date) {
      this.chooseDate = date
      switch (date) {
        case 'all':
          this.type = 0
          break
        case 'today':
          this.type = 1
          break
        case 'yesterday':
          this.type = 2
          break
        default:
          break
      }
      this.getBrandStatistics(this.store_id)
    },
    // 收益管理跳转
    navToIncome() {
      this.navTo(`/pages/promotion/promotion?localLife=${45}`)
      uni.setStorageSync('tabBarActive', 2)
    },
    // 输码验销
    verifyNews() {
      this.newsShow = true
    },
    // 关闭核销验码
    close() {
      this.newsShow = false
      this.value = ''
    },
    // 确认核销验码
    async onVerifyNews() {
      let params = {
        store_id: parseInt(this.store_id),
        code: this.value,
      }
      let res = await this.get(
        '/api/localLife/front/VerificationsOrderCode',
        params,
      )
      if (res.code === 0) {
        this.$refs.uToast.show({
          message: '核销成功',
          duration: 4000,
        })
        this.newsShow = false
      }
    },
    // 微信H5扫码
    initScanQRCode() {
      let that = this
      jweixin.ready(() => {
        jweixin.scanQRCode({
          needResult: 1,
          scanType: ['qrCode'],
          success: res => {
            console.log(res)
            let str = res.resultStr.split('=')
            if (str[0] === 'code') {
              that.navTo(
                `/packageE/localLife/verification/verification?code=${str[1]}&store_id=${that.store_id}`,
              )
            } else {
              that.toast('无效二维码')
            }
          },
          fail: res => {
            that.toast('扫码失败')
          },
        })
      })
    },
    // 扫码配置
    scanCode() {
      // #ifdef MP-WEIXIN
      let that = this
      uni.scanCode({
        scanType: ['qrCode'],
        success: function (res) {
          let str = res.result.split('=')
          if (str[0] === 'code') {
            that.navTo(
              `/packageE/localLife/verification/verification?code=${str[1]}&store_id=${that.store_id}`,
            )
          } else {
            that.toast('无效二维码')
          }
        },
        fail: res => {
          that.toast('扫码失败')
        },
      })
      // #endif

      // 微信h5可否使用
      if (this.checkWenxin()) {
        this.initScanQRCode()
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.container {
  width: 100vw;
  height: 100vh;
  background-color: #f5f5f5;
  .back_drop {
    width: 750rpx;
    height: 272rpx;
    background: linear-gradient(180deg, #f15353 0%, #f5f5f5 100%);
    .message {
      .change-btn {
        width: 130rpx;
        height: 56rpx;
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 33rpx;
      }
    }
    .store_operate {
      margin-top: 40rpx;
      margin-left: 24rpx;
      margin-right: 24rpx;
      width: 702rpx;
      height: 319rpx;
      background-color: #ffffff;
      border-radius: 16rpx;
    }
  }
  .data_count {
    width: 702rpx;
    height: 447rpx;
    background-color: #ffffff;
    margin-top: 245rpx;
    border-radius: 16rpx;
    margin-left: 24rpx;
    margin-right: 24rpx;
    .count-title {
      .date-time {
        margin-right: 24rpx;
        .today {
          width: 100rpx;
          height: 56rpx;
          border-radius: 12rpx 0 0 12rpx;
          text-align: center;
          line-height: 56rpx;
          color: #f15353;
          border: 1px solid #f15353;
        }
        .yesterday {
          width: 100rpx;
          height: 56rpx;
          text-align: center;
          line-height: 56rpx;
          color: #f15353;
          border-top: 1px solid #f15353;
          border-bottom: 1px solid #f15353;
        }
        .all {
          width: 100rpx;
          height: 56rpx;
          border-radius: 0rpx 12rpx 12rpx 0rpx;
          text-align: center;
          line-height: 56rpx;
          color: #f15353;
          border: 1px solid #f15353;
        }
      }
    }
    .data_number {
      margin-top: 44rpx;
    }
  }
}
.fontSize32 {
  font-size: 32rpx;
}
.fontSize26 {
  font-size: 26rpx;
}
.white {
  color: #ffffff;
}
.iconf-img {
  margin: 34rpx 0 0 0;
  width: 56rpx;
  height: 56rpx;
  font-size: 50rpx;
  color: #00001c;
  display: block;
}
.count {
  color: #00001c;
  font-weight: bold;
}
.grey {
  color: #6e6e79;
  font-style: 24rpx;
}
.choose-day {
  color: #ffffff !important;
  background-color: #f15353;
}
.tip {
  font-size: 18rpx;
  color: #f96d20;
  font-weight: normal;
  width: 200rpx;
  height: 30rpx;
  border-radius: 30rpx;
  line-height: 26rpx;
  background: rgba(#f96d20, 0.15);
  display: flex;
  justify-content: center;
  align-items: center;
}

.con-verifyNews {
  padding: 0rpx 36rpx 30rpx 36rpx;
  // height: 200rpx;
}
.verifyNews-title {
  padding: 0 36rpx;
  height: 117rpx;
  .title {
    font-size: 32rpx;
    color: #00001c;
    font-weight: bold;
  }
}

.code-box {
  height: 470rpx;
}
</style>
