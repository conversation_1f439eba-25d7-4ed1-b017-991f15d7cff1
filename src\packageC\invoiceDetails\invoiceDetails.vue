<!-- 发票详情 -->
<template>
	<view>
		<view class="page-bg">
			<view class="details-head d-cf">
				<view class="iconfont icon-fontclass-gant<PERSON><PERSON>"></view>
				<view>
					<view class="head-state" v-if="billDetail.status ===0">待开票</view>
					<view class="head-state" v-if="billDetail.status ===1">待确认</view>
					<view class="head-state" v-if="billDetail.status ===2">已开票</view>
					<view class="head-amount" v-if="billDetail.amount && billDetail.status !== 0">
						已开发票金额：￥{{billDetail.amount.toFixed(2) || 0.00}}
					</view>
					<view class="head-amount" v-if="billDetail.amount && billDetail.status === 0">
						预计可开发票金额：￥{{billDetail.amount.toFixed(2) || 0.00}}
					</view>
				</view>
			</view>
			<view class="details-content">
				<view class="details-type mb_40">
					<text class="type-name">发票类型</text>
					<text v-if="billDetail.type === 1">电子普通发票</text>
					<text v-if="billDetail.type === 2">增值税发票</text>
				</view>
				<view class="details-type mb_20">
					<text class="type-name">发票抬头</text>
					<text class="type-content" v-if="billDetail.account_type === 1">个人</text>
					<text class="type-content" v-if="billDetail.account_type === 2">单位</text>
				</view>
				<view class="details-img d-f mb_20">
					<view class="bill-img" v-if="billDetail.image">
						<image :src="billDetail.image"></image>
					</view>
					<view class="item-footer d-ef" v-if="billDetail.status ===1">
						<view 
							class="confirm footer-btn"
							 @click="invoiceConfirm(id,2)"
						>
							确定
						</view>
						<view 
							class="refuse footer-btn"
							@click="invoiceConfirm(id,0)"
						>
							拒绝
						</view>
					</view>
				</view>
				<view class="details-type mb_40 mt_38" v-show="billDetail.company_name">
					<text class="type-name">单位名称</text>
					<text class="type-content">{{billDetail.company_name}}</text>
				</view>
				<view class="details-type mb_40" v-show="billDetail.company_code">
					<text class="type-name">纳税人识别号</text>
					<text class="type-content">{{billDetail.company_code}}</text>
				</view>
				<view class="details-type mb_40" v-show="billDetail.detail_type">
					<text class="type-name">发票内容</text>
					<text class="type-content" v-if="billDetail.detail_type === 1">商品明细</text>
					<text class="type-content" v-if="billDetail.detail_type === 2">商品类别</text>
				</view>
				<view class="details-type mb_40" v-show="billDetail.sign_address">
					<text class="type-name">单位地址</text>
					<text class="type-content">{{billDetail.sign_address}}</text>
				</view>
				<view class="details-type mb_40" v-show="billDetail.sign_mobile">
					<text class="type-name">单位电话</text>
					<text class="type-content">{{billDetail.sign_mobile}}</text>
				</view>
				<view class="details-type mb_40" v-show="billDetail.opening_bank">
					<text class="type-name">开户银行</text>
					<text class="type-content">{{billDetail.opening_bank}}</text>
				</view>
				<view class="details-type mb_40" v-show="billDetail.bank_account">
					<text class="type-name">银行账号</text>
					<text class="type-content">{{billDetail.bank_account}}</text>
				</view>
				<view class="details-type mb_40" v-show="billDetail.mobile">
					<text class="type-name">收票人手机</text>
					<text class="type-content">{{billDetail.mobile}}</text>
				</view>
				<view class="details-type mb_40" v-show="billDetail.email">
					<text class="type-name">收票人邮箱</text>
					<text class="type-content">{{billDetail.email}}</text>
				</view>
				<view class="details-type pb_40" v-show="billDetail.created_at">
					<text class="type-name">申请时间</text>
					<text class="type-content">{{billDetail.created_at}}</text>
				</view>
			</view>
		</view>
		
		<view class="logistics d-f-c pb_40" v-show="billDetail.status !== 0">
			<text class="type-name mb-30">快递公司：{{billDetail.express_code}}</text>
			<text class="type-content mb-30">物流单号：{{billDetail.express_company}}</text>
			
			<view class="c-orange" 
				@click="navTo(`/packageB/member/logistics?code=${billDetail.express_company}&company_name=${billDetail.express_code}`)">查看物流</view>
		</view>
		
		<view class="goods-list" v-if="!$isEmpty.isEmpty(this.order)">
			<view class="goods-item">
				<view class="title">开票商品</view>
				<block v-for="(goodsItem, goodsIndex) in order.order_items" :key="goodsItem.id">
					<view class="goods-wrap d-f" @click="navTo('/packageA/commodity/commodity_details/commodity_details?id=' + goodsItem.product_id)">
						<view class="goods-left">
							<image :src="goodsItem.image_url"></image>
						</view>
						<view class="goods-right d-f">
							<view class="wrap">
								<u--text 
									:lines="2"
									color="#0c0d0e"
									size="28rpx"
									lineHeight="36rpx"
									:text="goodsItem.title"></u--text>
								<view class="sku-title">规格：{{goodsItem.sku_title}}</view>
							</view>
							<view class="wrap-num">
								<view>￥{{goodsItem.amount.toFixed(2) || 0.00}}</view>
								<view>共{{goodsItem.qty}}件</view>
							</view>
						</view>	
					</view>
				</block>
				<view class="goods-attribute">
					<view class="details-type mb_34">
						<text class="type-name">订单编号</text>
						<text class="type-content">{{order.order_sn}}</text>
					</view>
					<block v-for="(item,index) in amountItems" :key="index">
						<view class="details-type mb_34">
							<text class="type-name">{{item.title}}</text>
							<text class="type-content">￥{{item.amount.toFixed(2) || 0.00 }}</text>
						</view>
					</block>
					<view class="details-type mb_34">
						<text class="type-name">订单实付金额</text>
						<text class="type-content c-orange">￥{{order.amount}}</text>
					</view>
				</view>
			</view>
		</view>

		<uni-table border  emptyText="暂无更多数据" v-if="technologyFeeBill.length > 0">
			<!-- 表头行 -->
			<uni-tr>
				<uni-th align="center">订单编号</uni-th>
				<uni-th align="left">技术服务费（元）</uni-th>
			</uni-tr>
			<!-- 表格数据行 -->
			<block v-for="(item,index) in technologyFeeBill" :key="index">
				<uni-tr>
					<uni-td>{{item.order.order_sn}}</uni-td>
					<uni-td>{{item.amount}}</uni-td>
				</uni-tr>
			</block>
		</uni-table>
	</view>
</template>

<script>
	import uniTable from "@/uni_modules/uni-table/components/uni-table/uni-table.vue"
	export default {
		components:{
			uniTable,
		},
		data() {
			return {
				id:0,
				billDetail:{},
				billApi:'',
				type:'',
				order:{},
				amountItems:[],
				technologyFeeBill:[]
			}
		},
		onLoad(options) {
			if((options.id??'') !== '') {
				this.id = parseInt(options.id);
			}
			if((options.type??'') !== '') {
				this.type = options.type;
			}
			// if(this.type === 'order') { //从订单列表和发票列表进去的详情不同,打不开页面的时候就调用这个接口试试getBillWithOrderBillId
			// 	this.billApi = `/api/bill/getBillWithOrderBillId?id=${this.id}`;
			// } else {
			// 	this.billApi = `/api/bill/getBill?id=${this.id}`;
			// }
			this.billDetails(this.id);
		},
		methods: {
			billDetails(id) {
				this.get(`/api/bill/getBill?id=${id}`, {}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						this.billDetail = data.bill;
						this.billDetail.amount = this.toYuan(data.bill.amount);
						
						this.billDetail.created_at = this.formatDateTime(this.billDetail.created_at,6);
						if(!this.$isEmpty.isEmpty(this.billDetail.order_bill[0])) {
							this.order = this.billDetail?.order_bill[0]?.order;
							this.order.amount = this.toYuan(this.order.amount);
							this.amountItems = this.order.amount_detail?.amount_items;
							this.amountItems.map(item => {
								item.amount = this.toYuan(item.amount)
							});
							this.order.order_items.map(item => {
								item.amount = this.toYuan(item.amount)
							})
						} else {
							this.technologyFeeBill = this.billDetail.technology_fee_bill;
							this.technologyFeeBill.map(item => {
								item.amount = this.toYuan(item.amount);
							})
						}
					}
				}).catch((Error) => {
					// console.log(Error);
				})
			},
			invoiceConfirm(id,status) {
				this.post('/api/bill/confirmBill', {id,status}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						this.toast(res.msg);
						
					} else {
						this.toast(res.msg);
					}
					setTimeout(() => {
						this.billDetails(this.id);
					},500);
				}).catch((Error) => {
					// console.log(Error);
				})
			},
		}
	}
</script>
<style scoped>
	::v-deep .table--border {
		box-sizing: border-box;
	}
</style>
<style lang="scss" scoped>
	.page-bg {
		background-color: #f15353;
		.details-head {
			padding: 48rpx 0 46rpx 0;
			.icon-fontclass-gantanhao {
				// width: 54rpx;
				// height: 54rpx;
				// background-color: #ffffff;
				color: #fff;
				font-size: 54rpx;
				margin: 0 20rpx 0 44rpx;
			}
			.head-state {
				color: #fff;
				font-size: 32rpx;
				margin-bottom: 15rpx;
			}
			.head-amount {
				font-size: 26rpx;
				color: #ffffff;
			}
		}
		.details-content {
			background-color: #fff;
			border-radius: 20rpx 20rpx 0rpx 0rpx;
			padding: 36rpx 0 0 27rpx;
			margin-bottom: 20rpx;
			.details-type {
				.type-name {
					color: #747474;
					font-size: 28rpx;
					margin-right: 35rpx;
				}
				.type-content {
					color: #0c0d0e;
					font-size: 28rpx;
				}
			}
			.details-img {
				align-items: flex-end;
				.bill-img {
					margin-right: 50rpx;
					image {
						width: 200rpx;
						height: 128rpx;
						border-radius: 4rpx;
					}
				}
				.item-footer {
					.footer-btn {
						padding: 8rpx 36rpx;
						border-radius: 4rpx;
						color: #FFFFFF;
						margin-right: 28rpx;
					}
					.confirm {
						background-color: rgba(243, 42, 42, 100);
					}
					.refuse {
						background-color: rgba(255, 170, 79, 100);
					}
				}
				
			}
		}
	}
	.logistics {
		background-color: #fff;
		padding: 32rpx 0 27rpx 28rpx;
		margin-bottom: 20rpx;
	}
	.goods-list {
		.goods-item {
			background-color: #fff;
			padding: 32rpx 0 27rpx 28rpx;
			margin-bottom: 20rpx;
			.title {
				color: #1d1d1d;
				font-size: 30rpx;
				margin-bottom: 34rpx;
			}
			.goods-wrap {
				margin-bottom: 20rpx;
				.goods-left {
					margin-right: 16rpx;
					image {
						width: 110rpx;
						height: 110rpx;
						background-color: #909090;
						border-radius: 4rpx;
					}
				}
				.goods-right {
					.wrap {
						.sku-title {
							color: #747474;
							margin-top: 25rpx;
						}
					}
					.wrap-num {
						margin: 0 30rpx 0 52rpx;
						text-align: center;
					}
				}
			}
		}
		.goods-attribute {
			margin-top: 35rpx;
			
			.details-type {
				.type-name {
					color: #747474;
					font-size: 28rpx;
					margin-right: 35rpx;
				}
				.type-content {
					color: #0c0d0e;
					font-size: 28rpx;
				}
				.c-orange {
					color: #f15353;
				}
			}
		}
	}
</style>
