<!-- 汇聚余额充值记录 -->
<template>
	<view>
		<view class="wallet-list" v-if="!$isEmpty.isEmpty(rechargeList)">
			<block v-for="(item,index) in rechargeList" :key="index" >
				<view class="item d-bf" >
					<view class="d-f-c font_size12 c-29">
						<view class="mb_30 " >{{item.pay_sn}}</view>
						<view>剩余：{{item.remaining_amount.toFixed(2) || 0.00 }}</view>
						<view class="font_size11 c-8a mt-10">{{item.created_at}}</view>
					</view>
					<view class="d-f-c text-r">
						<view class="mb_30 c-orange" v-if="item.pay_status">+{{item.amount.toFixed(2) || 0.00}}</view>
						<view class="mb_30 c-green" v-else>{{item.amount.toFixed(2) || 0.00}}</view>
						<view class="font_size11 c-8a mt-10">{{item.pay_status?'已支付':'未支付'}}</view>
					</view>
				</view>
			</block>
		</view>
		<view v-else class="bg-white white-pb">
			<u-empty mode="data">
			</u-empty>
		</view>
	</view>
</template>

<script>
	export default {
		name:"rechargeList",
		props:{
			rechargeList:{
				type:Array,
				default:() => {
					return []
				}
			}
		},
		data() {
			return {
				
			};
		},
	}
</script>

<style lang="scss">
	.wallet-list {
		background-color: #fff;
		padding: 0 26rpx;
			.item {
				padding: 28rpx  0 26rpx;
				border-bottom: 1px solid #f2f2f2;
				&:last-of-type {
					border-bottom: none;
				}
			}
	}
</style>
