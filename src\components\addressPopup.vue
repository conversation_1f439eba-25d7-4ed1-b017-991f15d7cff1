<!-- 选择省市区组件 -->
<template>
  <view>
    <!-- 选择地区 -->
    <view
      class="b-mask"
      :class="addressShow ? 'true' : ''"
      @tap.stop="_closeDateLw"
    ></view>
    <view class="dateBe" :class="addressShow ? 'true' : ''">
      <view class="head">
        <view class="ll" @tap.stop="_closeDateLw">取消</view>
        <view class="rr" @tap.stop="_okAddress">确定</view>
      </view>
      <view class="main">
        <picker-view
          indicator-style="height: 50px;"
          style="width: 100%; height: 300px"
          :value="[selectIndex[0], selectIndex[1], selectIndex[2]]"
          @change="bindChange"
        >
          <picker-view-column id="provinceData">
            <view
              v-for="(item, index) in addressData"
              :key="index"
              style="line-height: 50px"
            >
              {{ item.name }}
            </view>
          </picker-view-column>
          <picker-view-column id="cityData">
            <view
              v-for="(item, index) in cityData"
              :key="index"
              style="line-height: 50px"
            >
              {{ item.name }}
            </view>
          </picker-view-column>
          <picker-view-column id="districtData">
            <view
              v-for="(item, index) in districtData"
              :key="index"
              style="line-height: 50px"
            >
              {{ item.name }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
  </view>
</template>

<script>
import { Debounce } from '@/utils/debounce.js'
export default {
  name: 'addressPopup',
  props: {
    addressShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 选中的省市区数组下标
      selectIndex: [0, 0, 0],
      // 所有的地区数据
      addressData: [],
      // 城市地区数据
      cityData: [],
      // 区级数据
      districtData: [],
      addressName: '',
      AddressSet: {
        province: '',
        city: '',
        county: '',
        province_id: 0,
        city_id: 0,
        county_id: 0,
      },
    }
  },
  mounted() {
    //要放在dom执行后
    this._initAddressInfo()
  },
  methods: {
    // 确定
    _okAddress() {
      let provinceCheckData = this.getSelectData(1)
      let cityCheckData = this.getSelectData(2)
      let countyCheckData = this.getSelectData(3)
      this.addressName =
        provinceCheckData.name +
        ' ' +
        cityCheckData.name +
        ' ' +
        countyCheckData.name
      this.AddressSet.province = provinceCheckData.name
      this.AddressSet.province_id = provinceCheckData.id
      this.AddressSet.city = cityCheckData.name
      this.AddressSet.city_id = cityCheckData.id
      this.AddressSet.county = countyCheckData.name
      this.AddressSet.county_id = countyCheckData.id
      this.$emit('AddressSetOn', this.AddressSet, this.addressName)
    },
    // 关闭选择收货地址
    _closeDateLw() {
      this.$emit('_closeDateLw', this.addressShow)
    },
    // 获取所有地区数据
    async _initAddressInfo() {
      const { code, data } = await this.get(
        '/api/region/getAreasData',
        {},
        true,
      )
      if (code === 0) {
        this.addressData = data
        this.cityData = this.getChildren(2)
        this.districtData = this.getChildren(3)
      }
    },
    // 获取子级数组
    getChildren(level) {
      let arr = []
      switch (level) {
        case 2:
          arr = this.addressData[this.selectIndex[0]].children
          break
        case 3:
          arr =
            this.addressData[this.selectIndex[0]].children[this.selectIndex[1]]
              .children
          break
      }
      return arr
    },
    // 获取选中的数据
    getSelectData(level = 1) {
      let arr = []
      switch (level) {
        case 1:
          arr = this.addressData[this.selectIndex[0]]
          break
        case 2:
          arr =
            this.addressData[this.selectIndex[0]].children[this.selectIndex[1]]
          break
        case 3:
          arr =
            this.addressData[this.selectIndex[0]].children[this.selectIndex[1]]
              .children[this.selectIndex[2]]
          break
      }
      return arr
    },
    // 更改数据触发
    bindChange(e) {
      this.selectIndex = e.detail.value
      this.cityData = this.getChildren(2)
      this.districtData = this.getChildren(3)
    },
  },
}
</script>

<style scoped>
.dateBe {
  position: fixed;
  bottom: 0rpx;
  left: -5rpx;
  width: 760rpx;
  padding: 0rpx 5rpx;
  box-sizing: border-box;
  z-index: 11000;
  font-size: 28rpx;
  border-top: 1rpx solid #d9d9d9;
  opacity: 0;
  transform: translate(-750rpx, 0rpx);
}

.dateBe.true {
  opacity: 1;
  transform: translate(0rpx, 0rpx);
}

.dateBe .head {
  display: flex;
  flex-flow: nowrap;
  padding: 0rpx 30rpx;
  line-height: 80rpx;
  border-bottom: 1rpx solid #d9d9d9;
  background: #f8f8f8;
}

.dateBe .head .ll {
  flex: 1;
}

.dateBe .head .rr {
  text-align: right;
  flex: 1;
}

.dateBe .main {
  background: #f8f8f8;
}

.dateBe .main view {
  text-align: center;
}
</style>
