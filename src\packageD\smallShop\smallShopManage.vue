<!-- 小商店管理 -->
<template>
  <view>
    <view class="head">
      <view class="head_my d-f">
        <view class="user-info-img">
          <u-avatar
            size="110rpx"
            shape="circle"
            :src="userInfo.avatar"
          ></u-avatar>
        </view>
        <view class="my_name">
          <view v-if="(isMPWEIXIN && checkNull(userInfo)) || !isMPWEIXIN">
            <view class="font_size16 c-white mt-15">
              {{ userInfo.username || '普通用户' }}
            </view>
            <!-- <view v-if="checkNull(user)">
              <view class="d-f font_size12 c-white">
                邀请码:{{ userInfo.invite_code }}
                <view class="my_copy c-orange bg-white" @tap="myCopy"
                  >复制</view
                >
              </view>
              <view class="mt-20 d-cf">
                <view class="my_member">
                  <image
                    class="my_member_img"
                    src="https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/img/level-icon.png"
                  >
                  </image>
                  <text>{{ levelName }}</text>
                  <view class="fa-angle-right"></view>
                </view>
              </view>
            </view> -->
          </view>
        </view>
        <view
          class="iconfont icon-massage_set"
          @click="navTo('/packageB/user/personalData/personalData')"
        ></view>
      </view>
      <view class="u-demo-block__content">
        <u-row
          customStyle="margin-top:26rpx;height: 130rpx;background:#fff;border-radius:10rpx;"
        >
          <u-col span="6">
            <view class="demo-layout d-cc-c f-bold">
              <view class="mb-20 font_size18 c-f14d4d">
                ￥{{ this.toYuan(statistics.order_count) }}
              </view>
              <view class="c-7e7e7e">订单金额</view>
            </view>
          </u-col>
          <u-col span="6">
            <view class="demo-layout d-cc-c f-bold">
              <view class="mb-20 font_size18 c-f14d4d">
                ￥{{ this.toYuan(statistics.price_sum) }}
              </view>
              <view class="c-7e7e7e">总收益</view>
            </view>
          </u-col>
        </u-row>
      </view>
    </view>
    <view class="my_content">
      <!-- 我的订单 -->
      <view class="m-card-view mt_20">
        <view class="m-card-title d-bf mb_20">
          <text>我的订单</text>
          <view
            @click="navTo('/packageD/smallShop/smallShopOrder')"
            class="all-order-more font_size12 c-8a d-cf fontw500"
          >
            全部订单
            <u-icon name="arrow-right" color="#8a8a8a" size="24"></u-icon>
          </view>
        </view>
        <u-line></u-line>
        <view class="mt_20">
          <u-grid :border="false" col="3">
            <u-grid-item @click="navTo('/packageD/smallShop/smallShopOrder')">
              <view class="font_size18 c-f14d4d f-bold">{{ order.all }}</view>
              <view class="mt_20">全部订单</view>
            </u-grid-item>
            <u-grid-item
              @click="navTo('/packageD/smallShop/smallShopOrder?status=1')"
            >
              <view class="font_size18 c-f14d4d f-bold">
                {{ order.wait_send_num }}
              </view>
              <view class="mt_20">待发货订单</view>
            </u-grid-item>
            <u-grid-item
              @click="navTo('/packageD/smallShop/smallShopAfterSales')"
            >
              <view class="font_size18 c-f14d4d f-bold">
                {{ order.back_num }}
              </view>
              <view class="mt_20">售后订单</view>
            </u-grid-item>
          </u-grid>
        </view>
      </view>
      <!-- 今日数据 -->
      <view class="m-card-view mt_20">
        <view class="m-card-title d-cf mb_20">
          <text>今日数据</text>
          <view class="ml_20 font_size12 c-8a time-view fontw500">
            <text>{{ time_now }}</text>
          </view>
        </view>
        <u-line></u-line>
        <view class="mt_20">
          <u-grid :border="false" col="3">
            <u-grid-item>
              <view class="font_size18 c-f14d4d f-bold">
                ￥{{ this.toYuan(today.pay_um) }}
              </view>
              <view class="mt_20 mb_20">支付金额</view>
              <view class="c-66A3E7">
                昨日 ￥{{ this.toYuan(yesterday.pay_um) }}
              </view>
            </u-grid-item>
            <u-grid-item>
              <view class="font_size18 c-f14d4d f-bold">
                {{ today.order_count }}
              </view>
              <view class="mt_20 mb_20">订单数</view>
              <view class="c-66A3E7">昨日 {{ yesterday.order_count }}</view>
            </u-grid-item>
            <u-grid-item>
              <view class="font_size18 c-f14d4d f-bold">
                {{ today.browse_count }}
              </view>
              <view class="mt_20 mb_20">访问数</view>
              <view class="c-66A3E7">昨日 {{ yesterday.browse_count }}</view>
            </u-grid-item>
          </u-grid>
        </view>
      </view>
      <!-- 我的小商店 -->
      <view class="m-card-view mt_20 mb_20">
        <view class="m-card-title d-bf mb_20">
          <text>我的小商店</text>
          <view
            class="font_size12 c-8a d-cf fontw500 c-66A3E7"
            @click="navTo('/packageD/smallShop/storeSetting')"
          >
            店铺设置
          </view>
        </view>
        <u-line></u-line>
        <view class="mt_20">
          <u-grid :border="false" col="2">
            <u-grid-item @click="navTo('/packageD/smallShop/productManage')">
              <view class="font_size18 c-f14d4d f-bold">
                {{ small_shop.product_count }}
              </view>
              <view class="mt_20">商品管理</view>
            </u-grid-item>
            <u-grid-item @click="navTo('/packageD/smallShop/albumManage')">
              <view class="font_size18 c-f14d4d f-bold">
                {{ small_shop.album_count }}
              </view>
              <view class="mt_20">专辑管理</view>
            </u-grid-item>
            <u-grid-item @click="navTo('/packageD/smallShop/productSelection')">
              <view class="mt_25">
                <view
                  class="icon-view icon-view1 iconfont icon-mianxinggouwu"
                ></view>
              </view>
              <view class="mt_20">在线选品</view>
            </u-grid-item>
            <u-grid-item
              v-if="courseIsShow"
              @click="navTo('/packageD/smallShop/courseManage')"
            >
              <view class="mt_25">
                <!-- <view
                  class="icon-view icon-view1 iconfont icon-mianxinggouwu"
                ></view> -->
                <img
                  src="@/static/image/crouse_manage.png"
                  style="width: 80rpx; height: 80rpx"
                />
              </view>
              <view class="mt_20">课程管理</view>
            </u-grid-item>
            <u-grid-item v-if="checkWenxin()">
              <!-- <wx-open-launch-weapp
                :appid="appid"
                :path="'pages/index/index.html?sid=' + sid"
                style="margin: 0 auto"
                class="mt_25 icon-view icon-view2 iconfont icon-shouyefill"
              >
                  <view></view>
              </wx-open-launch-weapp> -->
              <!-- <view class="mt_25">
                <view
                  style="margin: 0 auto;"
                  class="icon-view icon-view2 iconfont icon-shouyefill"
                ></view>
              </view> -->
              <wx-open-launch-weapp
                id="launch-btn"
                :appid="appid"
                :path="'pages/index/index.html?sid=' + sid"
              >
                <script type="text/wxtag-template">
                  <view style="display: block;width: 40px;height: 40px;margin-left: 15px;margin-top: 12.5px;background: rgba(252,44,73,0.2);border-radius: 50%;">
                    <image style="display: block;width: 30px;height: 30px;margin: 0 auto;padding-top: 6px;" src="https://yunxingongyinglian.oss-cn-guangzhou.aliyuncs.com/2024415/1713162894font-class-shouye.png"></image>
                  </view>
                  <view style="display: block;margin-top: 10px;width: 100%;">小商店首页</view>
                </script>
                <!-- <template>
                  <view class="mt_25">
                    <view
                      style="margin: 0 auto;"
                      class="icon-view icon-view2 iconfont icon-shouyefill"
                    ></view>
                  </view>
                  <view class="mt_20">小商店首页</view>
                </template> -->
              </wx-open-launch-weapp>
            </u-grid-item>
            <!-- #ifdef MP-WEIXIN -->
            <u-grid-item @click="toJump">
              <view class="mt_25">
                <view
                  class="icon-view icon-view2 iconfont icon-shouyefill"
                ></view>
              </view>
              <view class="mt_20">小商店首页</view>
            </u-grid-item>
            <!-- #endif -->
            <u-grid-item v-if="isLiveStream" @click="navTo('/packageD/smallShop/liveStreamManage')">
              <view class="mt_25">
                <view
                  class="icon-view icon-view1 iconfont "
                ></view>
              </view>
              <view class="mt_20">直播管理</view>
            </u-grid-item>
          </u-grid>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  name: 'smallManage',
  data() {
    return {
      // 课程管理是否显示
      courseIsShow: false,
      appid: '',
      sid: '', // 小商店店主的id
      isMPWEIXIN: false, // 是否为微信小程序端
      isLiveStream: false, //是否显示直播管理
      userInfo: {},
      time_now: {},
      statistics: {
        order_count: 0,
        price_sum: 0,
      },
      // 我的订单
      order: {
        all: 0,
        back_num: 0,
        wait_send_num: 0,
      },
      // 今日数据
      today: {
        browse_count: 0,
        order_count: 0,
        pay_um: 0,
      },
      // 昨日数据
      yesterday: {
        browse_count: 0,
        order_count: 0,
        pay_um: 0,
      },
      // 我的小商店
      small_shop: {
        album_count: 0,
        product_count: 0,
      },
    }
  },
  onShow() {
    /* this.user = uni.getStorageSync("user");
    // 未登录
    if (!this.checkNull(this.user)) {
      this.navTo("/pages/login/login");
    } */
    this.getAftersalesNum()
    this.getSmallShopData()
    this.onLoadSetting()
    // 直播管理方法
    this.getSmallShopOwnerAuthority()
  },
  onLoad() {
    // #ifdef MP-WEIXIN
    this.isMPWEIXIN = true
    // #endif
    if (this.checkWenxin() || this.isMPWEIXIN) {
      this.get('/api/smallShop/setting/getMiniAppID').then(res => {
        this.appid = res.data.appid
      })
    }
    // 微信浏览器
    if (this.checkWenxin()) {
      const url = window.location.href.split('#')[0]
      this.get('/api/wechatofficial/getJsConfig', {
        url,
      }).then(res => {
        if (res.code === 0) {
          const data = res.data
          jweixin.config({
            debug: false,
            appId: data.app_id,
            timestamp: data.timestamp,
            nonceStr: data.nonce_str,
            signature: data.signature,
            jsApiList: ['chooseImage'],
            openTagList: ['wx-open-launch-weapp'],
          })
        }
      })
    }
    this.getIsShowSetting()
  },
  methods: {
    async getIsShowSetting(){
      //courseIsShow
      const {data} = await this.post('/api/common/smallShopOwnerAuthority',{},false,false,false)
      if(data && data.length){
        for(let i = 0;i<data.length;i++){
          // 课程管理是否显示
          if(data[i].plugin_id === 18){
            this.courseIsShow = true
            break;
          }
        }
      }
    },
    // 获取售后订单数量
    async getAftersalesNum() {
      const res = await this.get('/api/smallShop/getAfterSalesCount', {}, true)
      this.order.back_num = res.data.total
    },
    // 获取小商店数据
    async getSmallShopData() {
      const res = await this.get(
        '/api/smallShop/center/getCenterInfo',
        {},
        true,
      )
      this.userInfo = res.data.info.user
      this.time_now = res.data.info.time_now
      this.statistics = res.data.info.statistics
      this.order.all = res.data.info.order.all
      this.order.wait_send_num = res.data.info.order.wait_send_num
      this.today = res.data.info.today
      this.yesterday = res.data.info.yesterday
      this.small_shop = res.data.info.small_shop
    },
    // 获取店铺设置
    async onLoadSetting() {
      const res = await this.get(
        '/api/smallShop/setting/getShopSetting',
        {},
        true,
      )
      const setting = res.data.setting
      this.sid = setting.sid // 小商店店主的id
    },
    // 获取appid
    async getAppId() {},
    // 跳转至小商店用户端
    toJump() {
      if (!this.sid) {
        this.toast('请先配置店铺')
        return
      }
      // #ifdef MP-WEIXIN
      uni.navigateToMiniProgram({
        appId: this.appid.trim(), // 要打开的小程序 appId
        path: 'pages/index/index?sid=' + this.sid, // 首页
        success(res) {
          console.log('success res:', res)
        },
        fail(res) {
          console.log('fail res:', res)
        },
        complete(res) {
          console.log('complete res:', res)
        },
      })
      /*  */
      // #endif
    },
  },
}
</script>
<style lang="scss" scoped>
.head {
  position: relative;
  background-image: linear-gradient(#f15353, #f5f5f5);
  padding: 42rpx 30rpx 0 30rpx;
  .head_my {
    .user-info-img {
      width: 110rpx;
      height: 110rpx;
      border-radius: 50%;
      overflow: hidden;

      image {
        width: 110rpx;
        height: 110rpx;
        border-radius: 50%;
      }
    }
    .my_name {
      flex: 2;
      // width:60%;
      margin-left: 28rpx;

      .my_copy {
        width: 68rpx;
        height: 30rpx;
        line-height: 30rpx;
        border-radius: 100rpx;
        padding: 4rpx 8rpx;
        font-size: 22rpx;
        transform: scale(0.9);
        text-align: center;
        font-family: Arial;
        margin-left: 30rpx;
      }

      .my_member {
        // max-width: 210rpx;
        height: 40rpx;
        line-height: 40rpx;
        padding: 0 30rpx 0 14rpx;
        background-color: #a36705;
        color: #ffaa29;
        border-radius: 100rpx;

        .my_member_img {
          width: 33rpx;
          height: 30rpx;
          vertical-align: middle;
          margin: 0 8rpx 8rpx 0;
        }

        text {
          font-size: 22rpx;
          transform: scale(0.9);
        }
      }
    }
  }
  .icon-massage_set {
    font-size: 30rpx;
    color: #fff;
  }
}
.my_content {
  padding: 0 30rpx 0 30rpx;
}
.fontw500 {
  font-weight: 500;
}
.icon-view {
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-radius: 50%;
  padding: 10rpx;
  &.icon-view1 {
    background-color: rgba($color: #fc8d2c, $alpha: 0.2);
    color: #fc8d2c;
  }
  &.icon-view2 {
    background-color: rgba($color: #fc2c49, $alpha: 0.2);
    color: #fc2c49;
  }
}
</style>
