<template>
<view>
    <page-meta :page-style=" show ? 'overflow: hidden;' : ''" />
    <view class="newPosterIndex">
      <view class="searchTop">
        <u-search placeholder="请输入搜索关键词" v-model="formData.title" @custom="searchMethod" height="64rpx" searchIconSize="32"></u-search>
      </view>
      <view class="tabsNav">
        <view class="tabsNavLis" :class="formData.type == item.id ? 'mda' : ''"
          v-for="(item,index) in typeList" @click.stop="tapType(item.id)" :key="index">{{ item.name }}</view>
      </view>
      <view class="childTabs">
        <view class="left">
          <view class="tabChild" :class="formData.tag_id == item.id ? 'mdalabel' : ''" v-for="(item,index) in tagList"  :key="index"
            @click.stop="tapLabel(item.id)" >{{ item.name }}</view>
        </view>
        <view class="right" @click.stop="showInMethod">
          筛选
          <image src="https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/newPoster%2Fselect.png"></image>
        </view>
      </view>
     <view class="posterList"  v-if="posterList.length > 0 ">
        <view class="posterLis"  v-for="(item,index) in posterList" :key="index"
          @click.stop="showPoster(item,index)"
          >
          <image :src="item.design_img_url" v-if="item.design_img_show == 1" mode="widthFix"></image>
          <image :src="item.background_url" v-else mode="widthFix"></image>
          <view class="title">{{ item.title }}</view>
        </view>
      </view>
      <view class="d-cc fs-1 c-5e5e5e mb-25">
        <view v-if="posterList.length != 0&&readMore">暂无更多~</view>
        <view v-if="posterList.length == 0">暂无数据~</view>
      </view>
      <img :src="upLoadImg" />
      <u-popup :show="show" mode="right"  mask-close-able @close="cancelTap" custom-style="height: 100%;width: 100%; ">
        <view class="rightBox">
          <view class="title">标签</view>
          <view class="tipsBox">
            <view class="tipslis"  v-for="(item,index) in tagList" :key="index" :class="item.id == poplabel ? 'modeS' : ''"
              @click.stop="chooseTag(item)">
              <view class="modeSImg"></view>
              {{ item.name }}
            </view>
          </view>
          <view  v-if="popupPage < popupLast" @click.stop="popupMore" style="text-align: center;">点击加载更多</view>
          <view class="btn">
            <view class="cancel" @click.stop="cancelTap">取消</view>
            <view class="confirm" @click.stop="confirmTap">确定</view>
          </view>
        </view>
      </u-popup>
     <u-popup
        :show="fmPosterShow"
        :closeOnClickOverlay="false"
        class="poster-popup-class"
        @close="closeFm"
        mode="center"
        custom-style="background: transparent;">
        <view class="fmPoster">
          <image class="minimg" @click.stop="scrollLeft" :src="showPosterInd == 0 ? leftImg1 : leftImg" alt="" />
          <view class="contenBox" id="posterImg">
            <view class="closeIcon" @click.stop="closeFm"></view>

          <image class="fm"  mode="widthFix"  show-menu-by-longpress v-if="posterItem.poster"
              :src="posterItem.poster && posterItem.poster.background_url" />
            <!--#ifdef MP-->
            <view class="btn" @click.stop="creatPoster">{{posterItem.is_record?'保存海报':'生成海报'}}</view>
            <!--#endif-->
            <!--#ifdef H5-->
            <view class="btn" @click.stop="creatPoster" v-if="!posterItem.is_record">{{posterItem.is_record?'保存海报':'生成海报'}}</view>
            <view class="btnTxt"  v-else>长按图片，保存到手机</view>
            <!--#endif-->
          </view>
          <image class="minimg" @click.stop="scrollRight"
            :src="showPosterInd >= (posterList && posterList.length - 1) ? rightImg1 : rightImg" />
        </view>
      </u-popup>
    </view>
    </view>
</template>
<script>
export default {
  data() {
    return {
      keywork: "",
      formData: {
        title:'',
        type:'',
        status:'',
        tag_id:'',
        pageSize: 10, // 每页10条数据
        page: 1, // 第几页
      },
      total:'',
      pluginName:'poster', //posterCenter备用参数
      // 已选类型
      active: 0,
      show: false,
      // 已选标签
      labelSelect: 0,
      poplabel: "",
      // 弹窗列表数组
      popupList: [],
      popupPage: 1,
      popupLast: 1,
      // 海报是否显示
      posterShow: false,
      // 前端生成图片参数
      posterData: {},
      // 已生成的图片参数
      defaultImg: "",
      // 选择显示的海报封面索引
      showPosterInd: 0,
      fmPosterShow: false,
      childImgaeBase: "",
      leftImg: "https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/images%2FposterCenter%2Fleft.png",
      leftImg1: "https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/images%2FposterCenter%2Fleft2.png",
      rightImg: "https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/images%2FposterCenter%2Fright.png",
      rightImg1: "https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/images%2FposterCenter%2Fright2.png",
      is_record: false,
      isLoadMore: true,
      // imgPath: app.globalData.imgPath,
      //模拟假数据
      typeList:[],
      tagList:[],
      upLoadImg:'',
      supplierImg: [], // 上传图片数组
      posterList:[] ,//海报列表
      posterItem:{
        poster:{}
      },
      posterShowBtn:false,
      posterBoxW:'',
      posterBoxH:'',
      postPreview:[]
    }
  },
  onReachBottom(){
    if (!this.$store.state.homeNoMore) {
            this.posterIndex("refresh")
    }
  },
  onLoad() {
    this.posterIndex("first");
    this.posterTypes();
    this.posterTags();


    /* let url = "https://yunxingongyinglian.oss-cn-guangzhou.aliyuncs.com/2024523/1716434513%E7%94%BB%E6%9D%BF%203.png"
    this.get("/api/poster/base",{url}).then(res=>{
      let base64img = res.data.base64
      const fs = uni.getFileSystemManager()
      console.log(fs,'????')
    }) */
  },
  computed: {
        readMore() { //为了适配小程序端{{}}不能直接读取vuex值的问题
                return this.$store.state.homeNoMore
        }
  },
  methods: {
    // 搜索方法
    searchMethod(event) {
      this.posterIndex("first");
    },
    posterIndex(item) {
      if (item != "refresh") {
        this.formData.page = 1
      }
      this.get('/api/poster/list', this.formData, true)
        .then(res => {
          if (res.code === 0) {
             const data = res.data;
             if (data.page < data.total / data.pageSize) { //判断是不是最后一页
                this.$store.commit("upHomeNoMore", false)
                this.formData.page += 1
             } else {
                this.$store.commit("upHomeNoMore", true)
             }
             if (item == "refresh") { //刷新
                this.posterList = [...this.posterList,...data.list]
             } else {
                this.posterList = data.list;
             }
             if (item == "first") { //第一次进入
                this.total = data.total
             }
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    posterTypes() {
      this.get('/api/poster/types', {}, true)
        .then(res => {
          if (res.code === 0) {
             const data = res.data;
             this.typeList = data.types;
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    posterTags() {
      this.get('/api/poster/tags', {}, true)
        .then(res => {
          if (res.code === 0) {
             const data = res.data;
             this.tagList = data.tags;
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    addRecord(url) { //添加上传图片记录用
      let query = {
        poster_id:this.posterItem.poster?.id,
        url:url
      };
      this.post('/api/poster/addRecord', query, true)
        .then(res => {
          if (res.code === 0) {
             const data = res.data;
             this.showPoster(this.posterItem.poster,this.showPosterInd)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    uploadFilePromise(file,data) {
        return new Promise((resolve, reject) => {
          let a = uni.uploadFile({
            url: this.api.host + '/api/common/upload',
            file,
            name: 'file',
            header: {
            },
            formData: data,
              success: (res) => {
                setTimeout(() => {
                        let json = JSON.parse(res.data)
                        let imgUrl = json.data?.file?.url;
                        resolve(imgUrl)
                }, 1000)
              },
            complete:(err) => {
              console.log(err);
            }
          });
        })
    },
    uploadFileWx(file,data) {
        return new Promise((resolve, reject) => {
          let a = uni.uploadFile({
            url: this.api.host + '/api/common/upload',
            filePath: file,
            name: 'file',
            formData: data,
              success: (res) => {
                setTimeout(() => {
                        let json = JSON.parse(res.data)
                        console.log(json);
                        let imgUrl = json.data?.file?.url;
                        resolve(imgUrl)
                }, 1000)
              },
            complete:(err) => {
              console.log(err);
            }
          });
        })
    },
    async posterClick(data) {
      this.posterShow = false;
      this.posterItem.poster.background_url = data.img;
      this.postPreview[0] = data.img;
      if(data.img) {
        this.posterItem.is_record = true;
      }
      //#ifdef H5
      let blobText = this.base64ToFile(data.img);
      let upLoadImg = await this.uploadFilePromise(blobText);
      //#endif

      //#ifdef MP
      let upLoadImg = await this.uploadFileWx(data.img);
      this.posterItem.poster.background_url = upLoadImg;
      //#endif
      this.addRecord(upLoadImg);
    },
    closeMethod(data) {
      this.posterShow = false;
    },
    tapType(id) {
      // this.active = id;
      this.formData.type = id;
      this.formData.page = 1;
      this.posterIndex("first");
    },
    //base64转flie
    base64ToFile(base64, name) {
      if (typeof base64 != 'string') {
        return;
      }
      var arr = base64.split(',')
      var type = arr[0]?.match(/:(.*?);/)[1]
      var fileExt = type?.split('/')[1]
      var bstr = atob(arr[1])
      var n = bstr.length
      var u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new File([u8arr], `${name}.` + fileExt, {
        type: type
      })
    },
    tapLabel(id) {
      this.formData.tag_id = id;
      // this.formData.page = 1;
      this.posterIndex("first");
      // this.getData();
    },
    //弹窗方法 start
    showInIint(key) {
      this.show = key;
      this.popupPage = 1;
      this.popupLast = 1;
      this.popupList = [];
    },
    showInMethod() {
      this.showInIint(true);
      this.poplabel = this.labelSelect;
    },
    popupMore() {
      if (this.popupPage >= this.popupLast) return;
      this.popupPage = this.popupPage + 1;
    },
    // 选择海报封面
    showPoster(item, index) {
      this.get(`/api/poster/detail?id=${item.id}`, {}, true)
        .then(res => {
          if (res.code === 0) {
             const data = res.data;
             this.posterItem = data;
             //#ifdef H5
             if(this.posterItem.poster) {
               //https://oss-hxq-test.szbaoly.com/bjsc/goods/31ea710a7e904d3bb97dca8f5b9ca61e.jpg
              // this.posterItem.poster.background_url = 'https://oss-hxq-test.szbaoly.com/bjsc/goods/31ea710a7e904d3bb97dca8f5b9ca61e.jpg';
             }
             //#endif
             this.$nextTick(() => {
               const query = uni.createSelectorQuery().in(this);
               query.select('#posterImg').boundingClientRect(data => {
                  if (data) {
                   console.log('得到布局位置信息：', data.width);
                   this.posterBoxW = data.width;
                   this.posterBoxH = data.height;
                  }
               }).exec();
             })

            if(data.is_record) {
              this.posterItem.poster.background_url = data.url;
               this.postPreview[0] = data.url;
            }
            this.showPosterInd = index;
            this.fmPosterShow = true;
          }

        })
        .catch(Error => {
          console.log(Error)
        })
    },
    chooseTag(item) {
      if(this.poplabel == item.id){
        this.poplabel = 0;
        return
      }
      this.poplabel = item.id;
    },
    cancelTap() {
      this.poplabel = this.labelSelect;
      this.showInIint(false);
    },
    confirmTap() {
      this.formData.tag_id = this.poplabel;
      this.posterIndex("first");
      this.showInIint(false);
      // this.getData();
    },
    scrollRight() {
      if (this.showPosterInd >=  this.posterList.length - 1) {
        this.toast('已经是最后一张');
        return;
      }
      if (this.showPosterInd <  this.posterList.length) {
        this.showPosterInd = Number(this.showPosterInd) + 1;
        this.posterShow = false;
      }
      this.showPoster(this.posterList[this.showPosterInd],this.showPosterInd);
    },
    scrollLeft() {
      if (this.showPosterInd == 0) {
        this.toast('已经是第一张');
        return;
      }
      if (this.showPosterInd > 0) {
        this.showPosterInd = Number(this.showPosterInd) - 1;
        this.posterShow = false;
      }
      this.showPoster(this.posterList[this.showPosterInd],this.showPosterInd);
    },
    closeFm() {
      // this.posterShow = false;
      // this.posterData = {};
      this.defaultImg = "";
      this.fmPosterShow = false;
    },
    creatPoster() {
      console.log('生成海报');
      if(this.posterItem.is_record){
        // 有封面就提示长按保存
        //#ifdef H5
          this.toast('请长按海报保存');
          // this.savePicture(this.posterList[this.showPosterInd].background_url);
        //#endif
        //#ifdef MP
          this.savePoster(this.posterItem.poster.background_url);
        //#endif
        return
      }
      /* uni.showLoading({
        title: '正在为您生成海报中'
      }); */
      let id = this.posterList[this.showPosterInd].id;
      this.post("/api/poster/generate",{id:this.posterList[this.showPosterInd].id},true).then(res=>{
          this.posterItem.poster.id = this.posterList[this.showPosterInd].id
          this.addRecord(res.data.path) //添加上传图片记录用
      })
      /* new Promise((resolve,reject) => {
        setTimeout(() => {
          this.posterShow = true;
          if(this.is_record) {
            this.posterList[this.showPosterInd].background_url = '';
          }
          this.defaultImg = this.posterItem?.poster?.background_url;
        })
        console.log('执行完成Promise');
        resolve();
      }) */
    },
    savePicture(base64) {
        var arr = base64.split(',');
        var bytes = atob(arr[1]);
        let ab = new ArrayBuffer(bytes.length);
        let ia = new Uint8Array(ab);
        for (let i = 0; i < bytes.length; i++) {
            ia[i] = bytes.charCodeAt(i);
        }
        var blob = new Blob([ab], { type: 'application/octet-stream' });
        var url = URL.createObjectURL(blob);
        var a = document.createElement('a');
        a.href = url;
        a.download = new Date().valueOf() + ".png";
        var e = document.createEvent('MouseEvents');
        e.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
        a.dispatchEvent(e);
        URL.revokeObjectURL(url);
    },
    //授权图片
    savePoster(img) {
      uni.showLoading({
        title: '正在下载中'
      });
      // 请求授权
      uni.downloadFile({//下载
          url: img, //图片下载地址
          success: res => {
            if (res.statusCode === 200) {
              uni.authorize({
                scope: 'scope.writePhotosAlbum',
                success() {
                  // 用户已经同意授权，可以保存图片
                  uni.saveImageToPhotosAlbum({
                    filePath: res.tempFilePath, // 图片文件路径，可以是网络图片路径或本地文件路径
                    success: function (res) {
                      // 保存成功
                      setTimeout(() => {
                        uni.showToast({
                          title: '图片保存成功',
                          duration:1500
                        });
                      },1000);

                       uni.hideLoading();
                    },
                    fail: function (err) {
                      console.log(err,'errerrerrerrerrerr');
                      // 保存失败
                      uni.showToast({
                        title: '图片保存失败',
                        icon: 'none',
                        duration:1500
                      });
                    }
                  });
                },
                fail() {
                  // 用户拒绝授权，无法保存图片
                  uni.showToast({
                    title: '授权失败，无法保存图片',
                    icon: 'none',
                    duration:1500
                  });
                  uni.getSetting({
                    success (res) {
                      // console.log('已知权限',res)
                      // 如果没有授权
                      if (!res.authSetting['scope.writePhotosAlbum']) {
                        // 则拉起授权窗口
                        uni.authorize({
                          scope: 'scope.writePhotosAlbum',
                          success() {
                            //点击允许后--就一直会进入成功授权的回调 就可以使用获取的方法了
                            this.savePoster(this.posterItem.poster.background_url);
                          },
                          fail (error) {
                            //点击了拒绝授权后--就一直会进入失败回调函数--此时就可以在这里重新拉起授权窗口
                            console.log('拒绝授权则拉起弹框', error)
                            uni.showModal({
                              title: '提示',
                              content: '若点击不授权，将无法保存图片',
                              cancelText: '不授权',
                              cancelColor: '#999',
                              confirmText: '授权',
                              confirmColor: '#f56c6c',
                              success (res) {
                                console.log(res)
                                if (res.confirm) {
                                  // 选择弹框内授权
                                  uni.openSetting({
                                    success (res) {
                                      console.log(res.authSetting)
                                    }
                                  })
                                } else if (res.cancel) {
                                  // 选择弹框内 不授权
                                  console.log('用户点击不授权')
                                }
                              }
                            })
                          }
                        })
                      } else {
                        // 有权限--直接保存
                        console.log('有权限  直接调用相应方法')
                        that.saveImg()

                      }
                    },
                    fail: (error) => {
                      console.log('调用微信的查取权限接口失败，并不知道有无权限!只有success调用成功才只知道有无权限', error)
                      uni.showToast({
                        title: error.errMsg,
                        icon: 'none',
                        duration: 1500,
                      })
                    }
                  })
                }
              });
            }
          },
          fail(err) {
            console.log(err,'errerr');
            uni.showToast({
              title: '下载失败',
              icon: 'none'
            });
          }
      })

    }
  }
}
</script>
<style lang="scss" scoped>
/* packageI/newPoster/newPoster.wxss */
.poster-popup-class {
  background-color: transparent !important;
}

.fmPoster {
  padding-top: 28rpx;
  width: 688rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fmPoster .minimg {
  width: 56rpx;
  height: 56rpx;
}

.fmPoster .contenBox {
  display: flex;
  flex-direction: column;
  position: relative;
}

.fmPoster .contenBox .closeIcon {
  position: absolute;
  right: -28rpx;
  top: -28rpx;
  background-image: url("https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/images%2FposterCenter%2Fclose%402x.png");
  background-repeat: no-repeat;
  background-size: cover;
  width: 56rpx;
  height: 56rpx;
  border-radius: 100%;
  z-index: 99;
}
.btnTxt {
  height: 72rpx;
  text-align: center;
  line-height: 72rpx;
  color: #fff;
  margin: 0 auto;
  margin-top: 40rpx;
}

.fmPoster .contenBox .fm {
  max-width: 560rpx;
  width:560rpx!important;
  overflow:hidden;
  height: 745.92rpx;
  border-radius: 16rpx;
}

.fmPoster .contenBox .btn {
  width: 328rpx;
  height: 72rpx;
  border-radius: 8rpx;
  background: #e95352;
  text-align: center;
  line-height: 72rpx;
  color: #fff;
  margin: 0 auto;
  margin-top: 40rpx;
}

.posterList {
  padding: 24rpx;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.posterList .posterLis {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 343.04rpx;
  border-radius: 32rpx;
  background: #fff;
  margin-bottom: 24rpx;
}

.posterList .posterLis image {
  width: 100%;
  height: 456.96rpx;
  border-radius: 32rpx 32rpx 0rpx 0rpx;
}

.posterList .posterLis .title {
  width: 100%;
  height: 56rpx;
  font-size: 28.16rpx;
  color: #333333;
  line-height: 56rpx;
  padding-left: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
}

.tabsNav::-webkit-scrollbar {
  display: none;
}

.childTabs .left::-webkit-scrollbar {
  display: none;
}

.newPosterIndex .searchTop {
  background: #fff;
}
.searchTop  {
  padding:20rpx 24rpx 0 24rpx;
}
// .searchTop .van-search{
//   padding-bottom: 0 !important;
// }

.newPosterIndex .rightBox::-webkit-scrollbar {
  height: 0.0313rem !important;
  width: 0.0313rem !important;
}

.newPosterIndex .rightBox {
  width:600rpx;
  max-height: 100%;
  background: #fff;
  // padding-bottom: 96rpx;
  padding:0 0 96rpx 0rpx;
}

.newPosterIndex .rightBox .title {
  font-weight: bold;
  font-size: 32rpx;
  color: #333333;
  text-align: left;
  padding: 24rpx;
}

.newPosterIndex .rightBox .btn {
  display: flex;
  justify-content: flex-end;
  margin-top: 64rpx;
  margin-right: 24rpx;
}

.newPosterIndex .rightBox .btn .cancel {
  width: 136rpx;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  border: 0.0313rem solid #e95352;
  border-radius: 8rpx;
  background: #fff;
  font-size: 28.16rpx;
  color: #e95352;
  margin-right: 40rpx;
}

.newPosterIndex .rightBox .btn .confirm {
  width: 136rpx;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  background: #e95352;
  border-radius: 8rpx;
  font-size: 28.16rpx;
  color: #ffffff;
}

.newPosterIndex .rightBox .tipsBox::-webkit-scrollbar {
  display: none;
}

.newPosterIndex .rightBox .tipsBox {
  max-height: 70vh;
  overflow-y: scroll;
  display: flex;
  margin-top: 24rpx;
  flex-wrap: wrap;
  padding-left: 24rpx;
}

.newPosterIndex .rightBox .tipsBox .tipslis {
  flex-shrink: 0;
  box-sizing: border-box;
  height: 64.96rpx;
  background: #f6f6f6;
  border: 0rem solid #e95352;
  border-radius: 8rpx;
  line-height: 64.96rpx;
  text-align: center;
  font-size: 24rpx;
  color: #666666;
  padding: 0 8rpx;
  margin-right: 14.4rpx;
  margin-bottom: 24rpx;
}

.newPosterIndex .rightBox .tipsBox .tipslis:nth-child(3n) {
  // margin-right: 0;
}

.newPosterIndex .rightBox .tipsBox .modeS {
  border: 0.0313rem solid #e95352;
  position: relative;
}

.newPosterIndex .rightBox .tipsBox .modeS .modeSImg {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 33.28rpx;
  height: 26.88rpx;
  background-image: url("https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/newPoster%2Fselect1.png");
  background-size: cover;
}

.newPosterIndex .childTabs {
  background: #fff;
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
}

.newPosterIndex .childTabs .left {
  flex: 1;
  display: flex;
  overflow-x: scroll;
}

.newPosterIndex .childTabs .left .tabChild {
  flex-shrink: 0;
  padding: 8rpx 24rpx;
  border-radius: 24.96rpx;
  background: #f6f6f6;
  margin-right: 32rpx;
  font-size: 24rpx;
  color: #999999;
}

.newPosterIndex .childTabs .left .mdalabel {
  background: #e95352;
  color: #fff;
}

.newPosterIndex .childTabs .left .tabChild:last-child {
  margin-right: 0;
}

.newPosterIndex .childTabs .right {
  width: 124.16rpx;
  flex-shrink: 0;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #999999;
}

.newPosterIndex .childTabs .right image {
  width: 24rpx;
  height: 24rpx;
  margin: 0;
}

.newPosterIndex .childTabs .right::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0.0313rem;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(84, 84, 84, 0.45) 100%);
}

.newPosterIndex .tabsNav {
  background: #fff;
  padding: 24rpx;
  overflow-x: scroll;
  display: flex;
  justify-content: flex-start;
  padding-bottom: 0;
}

.newPosterIndex .tabsNav .mda {
  color: #e95352;
}

.newPosterIndex .tabsNav .tabsNavLis:last-child {
  margin-right: 0;
}

.newPosterIndex .tabsNav .tabsNavLis {
  flex-shrink: 0;
  margin-right: 40rpx;
  font-size: 28.16rpx;
}

</style>
