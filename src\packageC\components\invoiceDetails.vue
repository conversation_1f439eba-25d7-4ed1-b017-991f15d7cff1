<!-- 未开票列表组件 -->
<template>
	<view>
		<block v-for="(item,index) in details " :key="index">
			<view class="order_item mb_20" >
				<view class="title d-bf">
					<view class="title_left d-c">
						<u-checkbox-group  
							v-if="nameState === '未开票'"
							>
						<u-checkbox 
								size="30"
								activeColor="#f14e4e"
								shape="circle"
								:name="item.id"
								:checked="item.checked"
								@change="firmSelectedAll(item,index)"
								>
						</u-checkbox>
						</u-checkbox-group>
						<text class="font_size12 c-666">订单号：{{item.order.order_sn}}</text>
					</view>
					<view class="title_right" v-if="item.order.status === 0">待付款</view>
					<view class="title_right" v-if="item.order.status === 1">待发货</view>
					<view class="title_right" v-if="item.order.status === 2">待收货</view>
					<view class="title_right" v-if="item.order.status === 3">已完成</view>
					<view class="title_right" v-if="item.order.status === -1">关闭</view>
				</view>
				<!-- 商品信息 -->
					<view class="order_detail mt_38">
						<view class="mb_40 d-bf">
							<view class="d-cf">
								<text class="iconfont icon-fontclass-dianpu"></text>
								<text class="font_size16 f-bold">{{item.order.title}}</text>
							</view>
							<view class="iconfont icon-member_right"></view>
						</view>
						<block v-for="(orderItem,orderIndex) in item.order.order_items" :key="orderIndex">
							<view class="detail_msg d-f" @click="navTo(`/packageA/myOrder/orderDetail/orderDetail?goods_id=${item.order.id}`)">
								<view class="detail_img mr_20">
									<image :src="orderItem.image_url"></image>
								</view>
								<view class="detail_wrap">
									<u--text :lines="2"
														color="#333"
														size="14" 
														lineHeight="36rpx" 
														:text="orderItem.title">
									</u--text>
									<view class="d-bc mt_18">
										<view class="c-666 font_size12 ">规格：{{orderItem.sku_title}} </view>
										<view class="font_size14 c-333">x {{orderItem.qty}}</view>
									</view>
									
									<view class="price d-ef">
										￥{{orderItem.amount.toFixed(2) || 0.00 }}
									</view>
								</view>
							</view>
						</block>
					</view>
					<view class="detail_info mt_34 c-90 font_size13	">
						<view class="mb_15">{{item.order.shipping_address.realname}} {{item.order.shipping_address.mobile}}</view>
						<view class="address">{{item.order.shipping_address.province}} {{item.order.shipping_address.city}} {{item.order.shipping_address.county}} {{item.order.shipping_address.town}}</view>
					</view>
					<view class="d-ef mt_16 pb_20 font_size14">
						<view class="price">共{{item.order.goods_count}}件商品  实付：<text style="font-size: 36rpx; font-weight: 700;">￥{{item.order && item.order.amount.toFixed(2) || 0.00 }}</text></view>
					</view>
					
					<u-line v-if="nameState == '未开票'"></u-line>
					<view class="item-footer d-ef" v-if="nameState == '未开票'">
						
						<view class="lookBtn" v-if="nameState == '未开票'" @click="createBatch(item)">申请开票</view>
						<view class="lookBtn" v-else>查看</view>
					</view>
			</view>
		</block>
		<!-- 组件做判断，不然多少数据就加载多少次组件 -->
		<invoice-popup
				v-if="orderBill.id || billBatchIds"
				:billShow="invoiceShow"
				:order_bill="orderBill"
				:setting="setting"
				@billClose="billClose"
				@BillConfirm="orderBillBtn"
		>
		</invoice-popup>
		<receiving-popup v-if="orderBill.id || billBatchIds"></receiving-popup>
		<address-from-popup v-if="orderBill.id || billBatchIds"></address-from-popup>
		<u-overlay :show="overBillShow" :z-index ="zIndex"></u-overlay>
	</view>
</template>

<script>

	import invoicePopup from '@/components/invoicePopup.vue';
	import receivingPopup from '@/components/receivingPopup/receivingPopup.vue';
	import addressFromPopup from '@/components/addressFromPopup/addressFromPopup.vue';
	import eventBus from '@/utils/eventBus';
	export default {
		components:{
			invoicePopup,
			receivingPopup,
			addressFromPopup,
		},
		name:"invoiceDetails",
		props:{
			details:{
				type:Array,
				default:[]
			},
			setting:{
				type:Object,
				default:{}
			},
			amount_type:{
				type:Number,
				default:1,
			},
			nameState:{
				type:String,
				default:''
			},
			billBatchIds:{
				type:Array,
				default:[]
			}
		},
		data() {
			return {
				zIndex:10070,
				invoiceShow:false,//发票显示隐藏
				overBillShow:false,// 遮罩层的显示隐藏
				goodsAddList:[], //地址列表
				settingBill:{}, //发票所有的设置
				orderBill:{  //发票信息
					account_type: 0,
					address_id: null,
					amount_type: 1,
					bank_account: "",
					company_code: "",
					company_name: "",
					detail_type: 0,
					email: "",
					id: 0,
					image: '',
					mobile: "",
					opening_bank: "",
					person_name: "",
					sign_address: "",
					sign_mobile: "",
					type: 0,
				},
				makeBatchUlr:''
			};
		},
		computed:{
		},
		created() {
			eventBus.$on('overlay', (val) => { //使用bus兄弟传值
				this.zIndex = val;
			})
			eventBus.$on('overlayBill', (val) => { //默认地址调用和发票调用不同
				this.overBillShow = val;
			})
		},
		destroyed() {
			eventBus.$off("overlay");
			eventBus.$off("overlayBill");
		},
		methods:{
			firmSelectedAll(item,index) { //选择的单选数据
				this.$emit('olidatedInvoice',item.id,index);
			},
			createBatch(items) {
				({  //解构赋值
					id: this.orderBill.id,
					type: this.orderBill.type,
					account_type: this.orderBill.account_type,
					amount_type: this.orderBill.amount_type,
					person_name: this.orderBill.person_name,
					mobile: this.orderBill.mobile,
					company_name: this.orderBill.company_name,
					company_code: this.orderBill.company_code,
					email: this.orderBill.email,
					sign_address: this.orderBill.sign_address,
					sign_mobile: this.orderBill.sign_mobile,
					opening_bank: this.orderBill.opening_bank,
					bank_account: this.orderBill.bank_account,
					detail_type: this.orderBill.detail_type,
					image: this.orderBill.image,
					address_id:this.orderBill.address_id
				} = items);
				
				this.orderBill.amount_type = this.amount_type; //订单发票，技术服务发票
				this.invoiceShow = true;
				this.overBillShow = true;
			},
			billClose(show,overlay) {
				this.invoiceShow = !show;
				this.overBillShow = !show;
			},
			orderBillBtn(bill, show) { //子组件自定义的事件传递过来
				if(this.billBatchIds.length > 0) {
					delete bill.id; //单独删除和批量删除字段不同
					bill.ids = [];
					bill.ids = this.billBatchIds;
					this.makeBatchUlr = '/api/bill/makeOutBatch';
				} else {
					this.makeBatchUlr = '/api/bill/makeOut';
				}
				this.post(this.makeBatchUlr, bill, true).then((res) => { //批量开发票
					if(res.code === 0) {
						let data = res.data;
						this.toast(res.msg);
						setTimeout(() => {
							this.$emit('refresh'); //刷新页面
						},1000)
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
				this.overBillShow = false;
				this.invoiceShow = false;
				this.overBillShow = false;
			},
		}
	}
</script>

<style lang="scss" scoped>
	.receiving-mask { /*自定义阴影*/
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
		position: fixed;
		top: 0rpx;
		left:0rpx;
		z-index: 10070;
		display: none;
	}
	.true {
		display: block;
	}
	.order_item {
		width: 100%;
		background-color: #fff;
		border-radius: 20rpx;
		padding: 28rpx 20rpx 0rpx 20rpx;
		box-sizing: border-box;
		.title {
			.title_left{
				.take {
					width: 70rpx;
					height: 34rpx;
					line-height: 34rpx;
					text-align: center;
					background-color: #fcbc07;
					border-radius: 10rpx;
					font-size: 24rpx;
					color: #fff;
				}
			}
			.title_right {
				font-size: 24rpx;
				color: #f14e4e;
			}
		}
		.order_detail {
			.icon-fontclass-dianpu {
				color: #f14e4e;
				font-size: 54rpx;
				margin-right: 13rpx;
			}
			.icon-member_right {
				color: #bdbdbd;
			}
			.detail_msg {
				margin-bottom: 20rpx;
				.detail_wrap{
					width:70%;
				}
				.detail_img {
					image {
						width: 140rpx;
						height: 140rpx;
					}
				}
				
			}
		}
		.item-footer {
			padding: 20rpx 0;
			.lookBtn {
				padding: 8rpx 36rpx;
				color: #f14e4e;
				font-size: 24rpx;
				border: solid 2rpx #f14e4e;
				border-radius: 28rpx;
			}
		}
	}
</style>
