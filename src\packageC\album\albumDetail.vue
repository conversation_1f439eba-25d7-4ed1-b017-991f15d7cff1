<template>
  <view>
    <view class="head f" v-if="albumItem.name">
      <view class="h40">
        <image
          class="thumbnail"
          v-if="albumItem.covers[0].src"
          :src="albumItem.covers[0].src"
        ></image>
      </view>
      <view class="f1">
        <view class="mb_12 con-tit">
          <template v-if="albumItem.name.length > 20">
            <text class="thumbtitle">
              {{ albumItem.name.slice(0, 20) + '...' }}
            </text>
          </template>
          <template v-else>
            <text class="thumbtitle">{{ albumItem.name }}</text>
          </template>
        </view>
        <view>
          <text class="grey-2">{{ albumItem.time_name }}发布</text>
        </view>
        <!-- <view class="f fac fw">
          <view
            class="mr_10 mb_10"
            v-for="(member, index) in albumItem.relations"
            :key="index"
          >
            <u-tag
              v-if="member.tag.name"
              :key="member.tag_id"
              :text="member.tag.name"
              type="warning"
              size="small"
            ></u-tag>
          </view>
        </view> -->
      </view>
    </view>
    <view class="head f fac fw">
      <view
        class="mr_10 mb_10"
        v-for="(member, index) in albumItem.relations"
        :key="index"
      >
        <u-tag
          v-if="member.tag.name"
          :key="member.tag_id"
          :text="member.tag.name"
          type="warning"
          size="small"
        ></u-tag>
      </view>
    </view>
    <!-- 自定义展开 start -->
    <!-- 存在问题：当文字只有一行时，无法隐藏折叠效果 -->
    <view class="f pt-30 bg-white">
      <view v-if="albumItem.describe" class="mt20 text-c">
        <strong class="w-94 p-x-3">描述：</strong>
        <view
          class="grey-3 lh24 w-94 mt-10 p-x-3"
          :class="{ unfold: true, fold: tapShow }"
        >
          {{ albumItem.describe }}
        </view>
        <view class="d-c mt-20 grey-3" @click="tapShow = !tapShow">
          {{ tapShow ? '收起' : '展开' }}
          <u-icon
            size="24"
            :name="tapShow ? 'arrow-up' : 'arrow-down'"
          ></u-icon>
        </view>
      </view>
    </view>
    <!-- 自定义展开 end -->
    <!-- uView readmore组件版 start -->
    <!-- 存在问题：组件内部用js计算生成高度，单位为px（无法通过样式穿透进行修改）；项目中高度单位采用rpx，导致冲突 -->
    <!-- <view class="pt-30 bg-white">
      <template>
        <u-read-more
          toggle
          openText="收起"
          closeText="展开"
          :showHeight="20"
          :fontSize="24"
          :shadowStyle="{
            backgroundImage: 'none',
          }"
        >
          <rich-text :nodes="content"></rich-text>
        </u-read-more>
      </template>
    </view> -->
    <!-- uView readmore组件版 end -->
    <view class="f bg-white" v-if="albumItem.product_count >= 0">
      <view class="con-count">
        <view class="item-count">
          <text class="count-num">{{ albumItem.product_count }}</text>
          <text class="count-txt">商品数量</text>
        </view>
        <view class="item-count">
          <text class="count-num">{{ albumItem.import_count }}</text>
          <text class="count-txt">累计导入</text>
        </view>
        <view class="item-count">
          <text class="count-num">{{ albumItem.browse_count }}</text>
          <text class="count-txt">累计访问</text>
        </view>
        <view class="item-count">
          <text class="count-num">{{ albumItem.sales_total }}</text>
          <text class="count-txt">累计销量</text>
        </view>
      </view>
    </view>
    <view class="con-product">
      <template v-for="item in albumsData">
        <view
          :key="item.id"
          class="item-product mb_20"
          @click="
            navTo(
              '/packageA/commodity/commodity_details/commodity_details?id=' +
                item.product.id,
            )
          "
        >
          <view class="text-c">
            <u-image
              width="350rpx"
              height="350rpx"
              :showLoading="true"
              :src="item.product.image_url"
            >
              <u-icon
                slot="error"
                size="40"
                color="#d0d0d1"
                name="photo"
              ></u-icon>
            </u-image>
          </view>
          <view class="bg-white">
            <view class="d-f pl_20 pr_20">
              <view class="pr_20" style="width: 100%">
                <text class="fs-2 ell line2">
                  {{ item.product.title }}
                </text>
                <view class="d-bf fs-0-5 c-8a mt_10">
                  <text>已售{{ item.product.sales }}</text>
                </view>
              </view>
            </view>
            <view class="d-bf d-cf mt_10 pl_20 pr_20">
              <view class="c-orange">
                <text class="fs-0-5">￥</text>
                <text class="fs-2">{{ toYuan(item.product.price) }}</text>
              </view>
            </view>
          </view>
        </view>
      </template>
      <view class="d-cc fs-1 c-5e5e5e mb-25 mt-25" style="width: 100%">
        <view v-if="isLastPage && albumsData.length != 0">暂无更多~</view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  name: 'AlbumDetail',
  data() {
    return {
      isLastPage: false,
      page: 1,
      pageSize: 10,
      total: 0,
      album_id: null,
      albumsData: {},
      albumItem: {},
      tapShow: false, // 展开简介
      content: `浔阳江头夜送客，枫叶荻花秋瑟瑟。主人下马客在船，举酒欲饮无管弦。醉不成欢惨将别，别时茫茫江浸月。
					忽闻水上琵琶声，主人忘归客不发。寻声暗问弹者谁，琵琶声停欲语迟。移船相近邀相见，添酒回灯重开宴。千呼万唤始出来，犹抱琵琶半遮面。转轴拨弦三两声，未成曲调先有情。弦弦掩抑声声思，似诉平生不得志。低眉信手续续弹，说尽心中无限事。轻拢慢捻抹复挑，初为《霓裳》后《六幺》。大弦嘈嘈如急雨，小弦切切如私语。嘈嘈切切错杂弹，大珠小珠落玉盘。间关莺语花底滑，幽咽泉流冰下难。冰泉冷涩弦凝绝，凝绝不通声暂歇。别有幽愁暗恨生，此时无声胜有声。银瓶乍破水浆迸，铁骑突出刀枪鸣。曲终收拨当心画，四弦一声如裂帛。东船西舫悄无言，唯见江心秋月白。
					沉吟放拨插弦中，整顿衣裳起敛容。自言本是京城女，家在虾蟆陵下住。十三学得琵琶成，名属教坊第一部。曲罢曾教善才服，妆成每被秋娘妒。五陵年少争缠头，一曲红绡不知数。钿头银篦击节碎，血色罗裙翻酒污。今年欢笑复明年，秋月春风等闲度。弟走从军阿姨死，暮去朝来颜色故。门前冷落鞍马稀，老大嫁作商人妇。商人重利轻别离，前月浮梁买茶去。去来江口守空船，绕船月明江水寒。夜深忽梦少年事，梦啼妆泪红阑干。
					我闻琵琶已叹息，又闻此语重唧唧。同是天涯沦落人，相逢何必曾相识！我从去年辞帝京，谪居卧病浔阳城。浔阳地僻无音乐，终岁不闻丝竹声。住近湓江地低湿，黄芦苦竹绕宅生。其间旦暮闻何物？杜鹃啼血猿哀鸣。春江花朝秋月夜，往往取酒还独倾。岂无山歌与村笛？呕哑嘲哳难为听。今夜闻君琵琶语，如听仙乐耳暂明。莫辞更坐弹一曲，为君翻作《琵琶行》。感我此言良久立，却坐促弦弦转急。凄凄不似向前声，满座重闻皆掩泣。座中泣下谁最多？江州司马青衫湿。`,
      content1: '展开简介',
    }
  },
  onLoad(options) {
    this.album_id = options.goods_id
    this.getAlbum()
  },
  onReachBottom(){
    if(!this.isLastPage){
       this.page += 1
       this.getAlbum(true)
    }
  },
  onShow() {
    this.getAlbum()()
    this.getAlbum()
  },
  methods: {
    load() {
      this.$refs.uReadMore.getAlbum()()
    },
    getAlbum(addList = false) {
      const api = '/api/productAlbumApi/findAlbum'
      const params = {
        id: Number(this.album_id),
        page: this.page,
        pageSize: this.pageSize
      }
      this.post(api, params, true)
        .then(res => {
          if (res.code === 0) {
            this.albumItem = res.data.album
            if (addList) {
              this.albumsData = [...this.albumsData,...res.data.pageResult.list]
            }else{
              this.albumsData = res.data.pageResult.list
            }
            this.total = res.data.pageResult.total
            this.isLastPage = this.albumsData.length >= this.total
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
  },
}
</script>
<style lang="scss" scoped>
.grey-2 {
  color: #9e9e9e;
}
.grey-3 {
  color: #6e6e6e;
}
.head {
  padding: 40rpx 30rpx 0 30rpx;
  background-color: #fff;
}
.f {
  display: flex;
}
.w-94 {
  width: 94vw;
}
.p-x-3 {
  padding: 0 3vw;
}

.thumbnail {
  margin-right: 30rpx;
  width: 213rpx;
  height: 120rpx;
}
.thumbtitle {
  line-height: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
}
.con-tag-2 {
  overflow: hidden;
  height: 48rpx;
}
.con-count {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
  .item-count {
    text-align: center;
    &:not(:last-child) {
      margin-right: 30rpx;
    }
    .count-num {
      font-size: 40rpx;
      color: #f11111;
    }
    .count-txt {
      margin-top: 10rpx;
      color: #444;
    }
  }
}
.con-count {
  display: flex;
  justify-content: space-around;
  margin: 30rpx;
  padding: 3vw;
  background: #fff7e9;
  width: 94vw;
  .item-count {
    display: flex;
    flex-direction: column;
    text-align: center;
    justify-content: space-between;
    .count-num {
      font-size: 40rpx;
      color: #f11111;
    }
    .count-txt {
      margin-top: 10rpx;
      color: #444;
    }
  }
}
.con-product {
  display: flex;
  flex-flow: wrap;
  padding: 3vw 2vw;
  .item-product {
    width: 47vw;
  }
  .item-product:nth-child(2n) {
    margin-left: 2vw;
  }
}
.line2 {
  overflow: hidden;
  height: 80rpx;
  line-height: 40rpx;
}
.unfold {
  overflow: hidden;
  height: 68rpx;
}
.fold {
  height: auto;
}
.p_10 {
  padding: 10rpx;
}
::v-deep .u-tag {
  padding: 6rpx 16rpx;
}
</style>
