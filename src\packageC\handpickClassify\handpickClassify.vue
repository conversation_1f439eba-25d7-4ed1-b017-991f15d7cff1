<template>
    <view>
        <view class="bg-white pl_15 pr-20">
            <u-tabs :list="list" keyName="title" ref="uTabs" lineWidth="0" lineHeight="0" :current="current"
                :activeStyle="{ color: '#f14e4e', fontWeight: 'bold', transform: 'scale(0.9)' }"
                :inactiveStyle="{ transform: 'scale(0.8)' }" style="width: 100%;" @change="changeType">
                <view slot="right" class="d-f">
                    <view class="fs-1-5 d-cc p-10 pl_20" style="color: #7a7a7a;" @click="goClassify(data_type)">
                        更多<view class="fs-3 ml-5 iconfont icon-member_right"></view>
                    </view>
                </view>
            </u-tabs>

        </view>
        <view class="d-bf m-20 ml-40 mr-30" v-if="data_type === 1">
            <view class="d-cc">
                <view v-for="(item, index) in list3" :key="index" :class="index == 0 ? '' : 'ml-80'" class="d-cc"
                    @click="screen(item)">
                    <view class="fs-1-5">
                        {{ item.name }}
                    </view>
                    <!-- #ifdef APP-PLUS || H5 -->
                    <view style="transform: scale(0.45);">
                        <u-icon name="arrow-up-fill" :color="item.sort == 'top' ? '#f14d4d' : '#959595'" />
                        <u-icon name="arrow-down-fill" :color="item.sort == 'button' ? '#f14d4d' : '#959595'" />
                    </view>
                    <!-- #endif -->
                    <!-- #ifdef MP-WEIXIN-->
                    <view class="d-cc-c ml-5 mt_2">
                        <u-icon name="arrow-up-fill" :color="item.sort == 'top' ? '#f14d4d' : '#959595'" size="13">
                        </u-icon>
                        <u-icon name="arrow-down-fill" :color="item.sort == 'button' ? '#f14d4d' : '#959595'" size="13">
                        </u-icon>
                    </view>
                    <!-- #endif -->
                </view>
            </view>
        </view>
        <!-- 商品 -->
        <template v-if="data_type === 1">
            <moveGoods ref="moveGoods" :list="list6"></moveGoods>
        </template>
        <!-- 共享专辑 -->
        <template v-else-if="data_type === 2">
            <moveAlbum ref="moveAlbum" :list="list6"></moveAlbum>
        </template>
        <!-- 供应商 -->
        <template v-else-if="data_type === 3">
            <moveSuppliers ref="moveSuppliers" :list="list6"></moveSuppliers>
        </template>
        <view class="d-cc fs-1 c-5e5e5e mb-25 mt-25">
            <view v-if="list6.length == total && list6.length != 0">暂无更多~</view>
            <view v-if="total == 0">暂无数据~</view>
        </view>
        <!-- 返回顶部 -->
        <toTop :flage="flage"></toTop>
    </view>
</template>

<script>
import moveGoods from '../../common/moveGoods/moveGoods.vue';
import moveAlbum from '../../common/moveAlbum/moveAlbum.vue';
import moveSuppliers from '../../common/moveSuppliers/moveSuppliers.vue';
import toTop from '../../common/toTop/toTop.vue';
export default {
    components: {
        toTop,
        moveGoods,
        moveAlbum,
        moveSuppliers
    },
    data() {
        return {
            id: null,
            flage: '', //返回顶部按钮是否显示
            user: uni.getStorageSync('user'),
            list: [],
            current: 0,
            page: 1,
            pageSize: 10,
            total: null,

            category_id: null, // tabs id
            stair: null, // 选中的tabs数据
            data_type: null, // 判断一级分类展示类型 1商品2专辑3供应商
            sort_by: null, // 二级分类id
            list3: [{ //二级分类
                name: '价格',
                sort: ''

            }, {
                name: '销量',
                sort: ''
            },
            {
                name: '利润',
                sort: ''
            }
            ],

            list6: [], // 展示的数据
            reachBottom: false, // 切换时不允许触发触底事件
        }
    },
    onLoad(e) {
        if (e.id) {
            this.id = parseInt(e.id)
        }
        this.gettingData();
    },
    // 加载更多
    async onReachBottom() {
        if (this.list6.length < this.total) {
            this.page = this.page + 1;
            this.merchandiseNews(this.stair)
        }
    },
    methods: {
        onPageScroll(e) { //根据距离顶部距离是否显示回到顶部按钮
            if (e.scrollTop > 10) { //当距离大于10时显示回到顶部按钮
                this.flage = true
            } else {
                this.flage = false
            }
        },
        // 获取tab 移动端列表
        gettingData() {
            this.get('/api/home/<USER>', {}, true).then(data => {
                this.list = data.data.wap_home;
                if (this.id) {
                    let index = this.list.findIndex(item => item.id === this.id)
                    if (index >= 0) {
                        this.data_type = this.list[index].data_type;
                        this.category_id = this.id;
                        this.stair = this.list[index];
                        this.current = index;
                        this.merchandiseNews(this.list[index])
                    }
                } else {
                    this.data_type = this.list[0].data_type
                    this.category_id = this.list[0].id
                    this.stair = this.list[0];
                    this.current = 0;
                    this.merchandiseNews(this.list[0])
                }

            })
        },
        // utabs切换数据类型
        changeType(index) {
            this.reachBottom = false
            this.data_type = index.data_type;
            this.category_id = index.id
            this.page = 1;
            this.stair = index;
            this.current = index.index;
            this.list6 = [];
            this.merchandiseNews(index)
        },
        // 商品信息展示
        merchandiseNews(stair) {
            //判断用户是否已经登录切换api
            let url = ''
            if (this.user) {
                // 数据类型为商品时的请求网址
                if (stair.data_type === 1 || stair.data_type === undefined || stair.data_type === null) {
                    url = "/api/product/getProductByWapHomeSettingIdLogin"
                }
                // 数据类型为共享商品专辑时的请求网址
                if (stair.data_type === 2) {
                    url = "/api/productAlbumApi/getAlbumListByApi"
                }
                // 数据类型为供应商时的请求网址
                if (stair.data_type === 3) {
                    url = "/api/supplier/getSupplierProductList"
                }
            } else {
                if (stair.data_type === 1 || stair.data_type === undefined || stair.data_type === null) {
                    url = "/api/product/getProductByWapHomeSettingId"
                }
                if (stair.data_type === 2) {
                    url = "/api/publicProductAlbumApi/getAlbumListByApi"
                }
                if (stair.data_type === 3) {
                    url = "/api/supplier/getSupplierProductList"
                }
            }


            let params = {}
            // 数据类型为商品时携带的参数
            if (stair.data_type === 1 || stair.data_type === undefined || stair.data_type === null) {
                params = {
                    wap_home_setting_id: this.category_id,
                    product_card_list_search: {
                        page: this.page,
                        pageSize: this.pageSize,
                        sort_by: this.sort_by
                    }
                }
            }
            // 数据类型为共享商品专辑时携带的参数
            if (stair.data_type === 2) {
                params = {
                    share_collection_sort: stair.share_collection_sort,
                    page: this.page,
                    pageSize: this.pageSize
                }
                // 0或者1 全部专辑 2 指定分类 3 指定专辑
                if (stair.share_collection_type === 3) {
                    params.album_ids = stair.share_collection_ids
                } else if (stair.share_collection_type === 2) {
                    params.tag_ids = stair.share_collection_tags
                }
            }
            // 数据类型为供应商时携带的参数
            if (stair.data_type === 3) {
                params = {
                    // category_id: stair.supplier_category_id,
                    sort_type: stair.supplier_sort_type,
                    page: this.page,
                    pageSize: this.pageSize
                }
                // 0或者1 全部供应商 2 指定分类 3 指定供应商
                if (stair.supplier_type === 3) {
                    params.id = stair.supplier_id
                } else if (stair.supplier_type === 2) {
                    params.category_id = stair.supplier_category_id
                }
            }
            // 数据类型为商品时的请求方法
            if (stair.data_type === 1 || stair.data_type === null || stair.data_type === undefined) {
                this.post(url, params, true, false, false).then(data => {
                    this.total = data.data.total
                    this.list6 = [...this.list6, ...data.data.list]
                    if (this.list6.length < this.total) {
                        this.reachBottom = true
                    }
                })
            }
            // 数据类型为共享商品专辑时的请求方法
            if (stair.data_type === 2) {
                this.get(url, params, true, false, false).then(data => {
                    this.total = data.data.total
                    this.list6 = [...this.list6, ...data.data.list]
                    if (this.list6.length < this.total) {
                        this.reachBottom = true
                    }
                })
            }
            // 数据类型为供应商时的请求方法
            if (stair.data_type === 3) {
                this.post(url, params, true, false, false).then(data => {
                    this.total = data.data.total
                    this.list6 = [...this.list6, ...data.data.list]
                    if (this.list6.length < this.total) {
                        this.reachBottom = true
                    }
                })
            }
        },
        screen(e) {
            let arry = []
            this.list6 = []
            this.page = 1
            for (var i = 0; i < this.list3.length; i++) {
                arry.push(i)
            }
            /* 筛选选中状态判断*/
            for (var i = 0; i < this.list3.length; i++) {
                if (this.list3[i].name === e.name) {
                    if (e.sort == 'top') {
                        this.$set(this.list3[i], 'sort', 'button')
                        for (var j = 0; j < arry.length; j++) {
                            if (arry[j] != i) {
                                this.$set(this.list3[j], 'sort', '')
                            }
                        }
                    } else {
                        this.$set(this.list3[i], 'sort', 'top')
                        this.$forceUpdate()
                        for (var j = 0; j < arry.length; j++) {
                            if (arry[j] != i) {
                                this.$set(this.list3[j], 'sort', '')
                            }
                        }
                    }
                }
            }
            /* 筛选传参判断 */
            if (e.name == "价格") {
                if (e.sort == "button") {
                    this.sort_by = 2
                } else {
                    this.sort_by = 3
                }
            } else if (e.name == "销量") {
                if (e.sort == "button") {
                    this.sort_by = 4
                } else {
                    this.sort_by = 5
                }
            } else if (e.name == "利润") {
                if (e.sort == "button") {
                    this.sort_by = 8
                } else {
                    this.sort_by = 9
                }
            }
            this.merchandiseNews(this.stair)
        },
        // 点击更多进行页面跳转
        goClassify(type) {
            // 跳转到分类页面
            if (type === 1 || type === null || type === undefined) {
                this.tabTo('/pages/classify/classify')
            }
            // 跳转到共享专辑商品页面
            if (type === 2) {
                uni.navigateTo({
                    url: '/packageC/album/albumList'
                })
            }
            // 跳转到供应商页面
            if (type === 3) {
                uni.navigateTo({
                    url: '/packageD/shopList/shopList'
                })
            }
        },
    }
}
</script>

<style scoped></style>