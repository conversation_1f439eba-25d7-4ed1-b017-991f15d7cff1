<!-- 申请售后 -->
<template>
	<view id="refund">
		<view class="return-goods">
			<view class="return-goods-item d-f">
				<view class="goods-img">
					<image :src="order_item.image_url"></image>
				</view>
				<view class="goods-content d-be">
					<view class="goods-title font_size15 c-2c">{{order_item.title}}</view>
					<view class="goods-options c-66">x{{order_item.qty}}</view>
					<!-- 处理判断第三个值order_item.amount.toFixed(2) || -->
					<view class="goods-price" v-if="order_item.amount">￥{{order_item.amount ||  0.00}}</view>
				</view>
			</view>
		</view>
		<u-line></u-line>

		<view class="infoMain">
			<!-- 申请类型 -->
			<view class="info" >
				<view class="mode d-bf">
					<view>申请类型：</view>
					<view class="d-cf" @click="popupOpen('type')">
						<view class="c-8c">{{refundName}}</view>
						<view class="iconfont icon-advertise-next"></view>
					</view>
				</view>
			</view>
			<view class="info" v-if="refundValue === 0">
				<view class="mode d-bf">
					<view>收货状态：</view>
					<view class="d-cf" @click="popupOpen('take')">
						<view class="c-8c">{{takeName || '请选择'}}</view>
						<view class="iconfont icon-advertise-next"></view>
					</view>
				</view>
			</view>
			<view class="info" v-if="refundValue === 0 ||  refundValue === 1">
				<view class="d-bf cause">
					<view>申请原因：</view>
					<view class="d-cf" @click="popupOpen('apply')">
						<view class="c-8c">{{  receivedName || '请选择'  }}</view>
						<view class="iconfont icon-advertise-next"></view>
					</view>
				</view>
				<view class="cause sum">
					<view class="d-bf">
						<view >退款数量：{{formData.num }}</view>
						<u-number-box v-model="formData.num" :min="1" :max="maxNum"></u-number-box>
					</view>
					<view class="c-8a font_size12 mb_10" style="line-height:30rpx;">最多可换{{maxNum}}{{order_item.unit}}</view>
				</view>
				<view class="sum d-cf" v-if="refundValue === 0 ||  refundValue === 1 && formData.amount">
					<view >退款金额：</view>
					<view class="c-orange pl-20">{{formData.amount || 0.00}}元</view>
				</view>
			</view>
			<view class="prompt" v-if="refundValue === 0 ||  refundValue === 1 && formData.amount">
				提示：您的退款最大金额为{{formData.amount || 0.00}}元
			</view>
			<view class="info" v-if="refundValue === 0 ||  refundValue === 1">
				<view class="sum d-cf" v-if="refundValue === 0 ||  refundValue === 1 && formData.technical_services_fee">
					<view >技术服务费：</view>
					<u--input
					v-model="formData.technical_services_fee"
					type="digit"
					border="none"
					clearable
				></u--input>
					<!-- <view class="c-orange pl-20">{{formData.technical_services_fee || 0.00}}元</view> -->
				</view>
			</view>
			<view class="prompt" v-if="refundValue === 0 ||  refundValue === 1 && formData.technical_services_fee">
				提示：您的技术服务费最大金额为{{technical_services_fee || 0.00}}元
			</view>
			<view class="info" v-if="refundValue === 0 ||  refundValue === 1">
				<view class="sum d-cf" v-if="refundValue === 0 ||  refundValue === 1 && formData.freight">
					<view >运费：</view>
					<u--input
					v-model="formData.freight"
					type="digit"
					border="none"
					clearable
				></u--input>
					<!-- <view class="c-orange pl-20">{{formData.technical_services_fee || 0.00}}元</view> -->
				</view>
			</view>
			<view class="prompt" v-if="refundValue === 0 ||  refundValue === 1 && formData.freight">
				提示：您的运费最大金额为{{freight || 0.00}}元
			</view>
		</view>
		<!--申请说明 -->
		<view class="explain">
			<view class="title">申请说明：</view>
			<view class="explain-content">
				<textarea
									v-model="formData.description"
									placeholder-class="apply"
									placeholder="请输入申请售后服务的原因"
									placeholderStyle="color: #8c8c8c;font-size:24rpx;background-color: #F5F5F5;">
				</textarea>
			</view>
		</view>
		<view class="explain">
			<view class="title">上传图片凭证：</view>
			<view class="imgUploader" style="padding:25rpx" >
				<u-upload
						width="140rpx"
						height="140rpx"
						:fileList="refundImages"
						@afterRead="afterRead"
						@delete="deletePic"
						accept="image"
						name="1"
						multiple
						:maxCount="10"
						:previewFullImage="true"
					></u-upload>
			</view>
		</view>
		<view class="mb40"></view>
		<view class="submit-refund" @click="refundForm">提交申请</view>

		<u-popup :show="typeShow" @close="close" @open="open">
				<view class="popup-return-type">
					<u-navbar
							title="选择售后类型"
							@rightClick="rightClick"
							:autoBack="false"
					>
					<view
							class="u-nav-slot"
							slot="left"
					>
					</view>
					</u-navbar>
						<view class="return-type-list">
							<block v-for="(item,index) in refundTypes" :key="index">
								<view class="return-type-item d-cf" @click="refundType(item.value,item.name)">
									<view class="icon iconfont lefticon " :class="item.icon">
									</view>
									<view class="return-content flex_max">
										<view class="return-title">{{item.name}}</view>
										<view class="return-desc">{{item.desc}}</view>
									</view>
									<view class="next">
										<view class="iconfont icon-advertise-next"></view>
									</view>
								</view>
							</block>
						</view>
				</view>
		</u-popup>

		<!-- 收货状态 -->
		<u-popup :show="takeShow" :round="10" mode="bottom" @close="popupClose('take')" @open="takeCpen" :closeable="true">
			<view class="classify">
					<view class="classify-title d-c">
						<view class="c-gray3 font_size32" >收货状态</view>
					</view>
					<view class="classify-content">
							<u-radio-group
									placement="column"
									v-model="takeGroup"
									iconPlacement="right"
							>
									<u-radio
											size="40"
											activeColor="#f15353"
											labelSize="28rpx"
											labelColor="#202020"
											v-for="(item, index) in takeValues"
											@change="takeChange(item.name,item.value)"
											:key="index"
											:label="item.name"
											:name="item.value"
									>
									</u-radio>
							</u-radio-group>
					</view>
					<view class="popup-btn d-cc" @click="receivingBtn">确定</view>
			</view>
		</u-popup>

		<!--申请原因 -->
		<u-popup :show="applyShow" :round="10" mode="bottom" @close="popupClose('apply')" @open="takeCpen" :closeable="true">
			<view class="classify">
					<view class="classify-title d-c">
						<view class="c-gray3 font_size32" >退款原因</view>
					</view>
					<view class="classify-content">
							<u-radio-group
									placement="column"
									iconPlacement="right"
									v-model="refundValues"
							>
									<u-radio
											size="40"
											activeColor="#f15353"
											labelSize="28rpx"
											labelColor="#202020"
											v-for="(item, index) in reasons"
											:key="index"
											:label="item"
											:name="item"
											@change="refundChange(item,index)"
									>
									</u-radio>
							</u-radio-group>
							<view v-if="receivedName == '其他' || reasonTypeIndex == 5">
								<u--textarea v-model="formData.reason" placeholder="" height="140rpx"></u--textarea>
							</view>
					</view>
					<view class="popup-btn d-cc" @click="reasonsBtn">确定</view>
			</view>
		</u-popup>

		<u-toast ref="uToast" type="error"></u-toast>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				mid:'',
				orderId:0,
				order_item:{},
				maxNum:0,
				technical_services_fee: 0,
				freight: 0,
				formData: {
					num:0,
					refund_way:1,
					order_item_id:0,
					amount:0,
					freight:0,
					technical_services_fee:0,
					is_received:0,
					reason_type:null, //申请原因
					refund_type:null, //退货还是退款
					description:'',
					reason:'',
					detail_images:[]
				},
				refundImages:[],
				typeShow:true,
				takeShow:false,
				applyShow:false,
				backShow: false,
				applyValue:'',
				refundName: '',
				reasonTypeName:'',

				reasonTypeIndex:0,
				takeName: '',
				takeNameCheck:'未收到货',
				takeValueCheck:0,
				backName: '自行寄回',
				refundValue: 0,
				takeGroup:0,
				refundValues: '',
				receivedName:'',
				rests: '',
				takeValues: [
						{
							name:'未收到货',
							value:0
						},
						{
							name:'已收到货',
							value:1
						}
				],
				refundTypes: [
						{
								name: "退款(仅退款不退货)",
								value: 0,
								desc: "未收到货或者不用退货只退款",
								icon: "icon-fontclass-daizhifu",
						},
						{
								name: "退款退货",
								value: 1,
								desc: "已收到货，需要退款退货",
								icon: "icon-fontclass-daishouhuo",
						}
				],
				reasons: {},
				support_batch: false,
			}
		},
		computed:{
			refundAmount(){
				let price = this.formData.num * this.order_item.price || 0
				return this.toYuan(price)
			}
		},
		onLoad(options) {
			if(options.mid) {
				this.mid = options.mid;
				this.mid == 7 ?this.typeShow = false:this.typeShow = true
			}
			if((options.orderId??'') !== '') {
				this.orderId = parseInt(options.orderId);
				this.detail(this.orderId);
			}
			if((options.id??'') !== '') {
				this.formData.order_item_id = parseInt(options.id);
				this.refundDetail(options.id);
			}
			if((options.refund??'') !== '') {
				this.formData.amount = parseFloat(options.refund);
			}
			if((options.freight??'') !== '') {
				this.formData.freight = this.toYuan(parseInt(options.freight));
				this.freight = this.toYuan(parseInt(options.freight));
			}
			if((options.technical_services_fee??'') !== '') {
				this.formData.technical_services_fee = this.toYuan(parseInt(options.technical_services_fee));
				this.technical_services_fee = this.toYuan(parseInt(options.technical_services_fee));
			}
		},
		methods: {
			// 新增图片
			async afterRead(event) {
				// 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.refundImages.length
				lists.map((item) => {
					this.refundImages.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let item = this.refundImages[fileListLen];
					this.refundImages.splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: result
					}))
					fileListLen++
				}
			},
			deletePic(event) { //删除图片
				this.refundImages.splice(event.index, 1);
				this.formData.detail_images.splice(event.index, 1);
			},
			uploadFilePromise(url) { //上传图片
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: this.api.host + '/api/common/upload',
						filePath: url,
						name:'file',
						formData: {
							file: url
						},
						success: (res) => {
							console.log(res);
							setTimeout(() => {
								resolve(res.data.data);
								let data = JSON.parse(res.data);
								// this.form.avatar = data.data.file.url;
								this.formData.detail_images.push(data.data.file.url);
							}, 1000)
						}
					});
				})
			},
			close() {
				this.show = false;
			},
			refundDetail(id) { //商品信息
				this.get(`/api/order/getOrderItem?order_item_id=${id}`,{}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						this.order_item = data.order_item;
						this.order_item.amount = this.toYuan(this.order_item.amount);
						this.formData.amount = this.order_item.amount;
						this.formData.num = this.order_item.qty
						this.formData.technical_services_fee = this.toYuan(this.order_item.technical_services_fee)
						this.technical_services_fee = this.toYuan(this.order_item.technical_services_fee)
						this.maxNum = this.formData.num

						this.get(`/api/order/get?id=${data.order_item.order_id}`).then(r=>{
							this.formData.freight = this.toYuan(r.data.order.freight)
							this.freight = this.toYuan(r.data.order.freight)
						})
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			detail(id) {  //获取售后订单详情,编辑数据
				this.get(`/api/afterSales/get?id=${id}` ,{}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						let afterSales = data.after_sales;
						let { //结构赋值用来修改
								order_item_id,
								amount,
								reason_type,
								is_received,
								description,
								reason,
								detail_images
							} = data.after_sales;
						this.formData = {
							order_item_id,
							amount,
							is_received,
							reason_type,
							description,
							reason,
							detail_images
						};
						this.formData.amount =	this.toYuan(this.formData.amount);
						this.formData.detail_images.forEach(item => {  //回显售后上传图片
							this.refundImages.push({
								url:item
							})
						});
						if(afterSales.type) { //回显的数据
							this.refundName = '退款退货'
						} else {
							this.refundName = '退款(仅退款不退货)'
						}
						if(is_received) { //回显的数据
							this.takeName = '已收到货';
						} else {
							this.takeName = '未收到货';
						}
						this.takeGroup = is_received; //默认选择的值
						this.takeValueCheck = is_received; //收货状态
						this.formData.refund_type = afterSales.type; //0退款 1退货
						this.formData.reason_type = afterSales.reason_type; //售后原因
						// this.receivedName = afterSales.refund_reason_name;
						this.receivedName = afterSales.refund_reason_name;
						this.refundValues = afterSales.refund_reason_name;
						console.log(this.formData.reason_type);
						this.refundReason();
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			refundReason() { //查询退货类型
				this.get(`/api/afterSales/reason/list?after_sale_type=${this.refundValue}&is_received=${this.formData.is_received}` , {}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						this.reasons = data.reasons;
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			receivingBtn() {  //是否收到货确定拿值
				this.takeName = this.takeNameCheck;
				this.formData.is_received = this.takeValueCheck;
				this.takeShow = false;
			},
			reasonsBtn() {  //选择退款原因
				this.formData.reason_type = parseInt(this.reasonTypeIndex);
				this.receivedName = this.reasonTypeName;
				this.applyShow = false;
			},
			refundChange(name,index) {
				this.reasonTypeIndex = index;
				this.reasonTypeName = name;
			},
			refundType(value,name) { //选择售后类型
				this.refundName = name;
				this.refundValue = value;
				this.formData.refund_type = value;
				this.typeShow = false;
				//选择售后类型清除售后原因
				this.receivedName = '';
				this.refundValues = '';
				// this.refundReason();
			},
			takeChange(name, value) { //选择收货状态的时候获取值，要中转一下
				this.takeValueCheck = value;
				this.takeNameCheck = name;
			},
			popupClose(type) {
				if(type === 'apply') {
					this.applyShow = false;
					// this.receivedName  = '';
				} else if (type === 'back') {
					this.backShow = false;
				} else if (type === 'take') {
					this.takeShow = false;
					// this.takeName = this.takeValues[this.takeValue].name;
				}
			},
			popupOpen(type) {
				if(type === 'type') {
					this.typeShow = true;
				} else if (type === 'take') {
					this.takeShow = true;
				} else if (type === 'apply') {
					this.applyShow = true;
					this.refundReason();
				} else if (type === 'back') {
					this.backShow = true;
				}
			},
			refundForm() { //提交申请售后

				if(this.formData.reason_type === null) { //判断是新增还是编辑
					uni.$u.toast('请选择退款原因');
				} else if (this.formData.description == '') {
					uni.$u.toast('请输入描述原因');
				}else if(!this.isNaN(this.toFen(this.formData.technical_services_fee))){
					uni.$u.toast('技术服务费金额有误');
				}else if(this.toFen(this.formData.technical_services_fee) > this.toFen(this.technical_services_fee)){
					uni.$u.toast('技术服务费已超出最大金额');
				}else if(this.toFen(this.formData.technical_services_fee) < 0){
					uni.$u.toast('技术服务费不能是负数');
				}else if(!this.isNaN(this.toFen(this.formData.freight))){
					uni.$u.toast('运费金额有误');
				}else if(this.toFen(this.formData.freight) > this.toFen(this.freight)){
					uni.$u.toast('运费已超出最大金额');
				}else if(this.toFen(this.formData.freight) < 0){
					uni.$u.toast('运费不能是负数');
				}else{
					this.formData.amount = this.toFen(this.refundAmount);
					this.formData.freight = this.toFen(this.formData.freight);
					this.formData.technical_services_fee = this.toFen(this.formData.technical_services_fee);
					if(this.orderId) {
						this.formData.id = this.orderId;
						this.post('/api/afterSales/save', this.formData, true).then((res) => {
							if(res.code === 0) {
								let data = res.data;
								this.toast(res.msg);
								setTimeout(() => {
									this.backRefresh();
								},1000)
								console.log(this.formData);
							} else {
								this.toast(res.msg);
								console.log(this.formData);

							}
						}).catch((Error) => {
							console.log(Error);
							console.log(this.formData);
						})
					}	else {
						this.post('/api/afterSales/create', this.formData, true).then((res) => {
							if(res.code === 0) {
								let data = res.data;
								this.toast(res.msg);
								setTimeout(() => {
									this.backRefresh();
								}, 1000)

								console.log(this.formData);
							} else {
								this.toast(res.msg);
								setTimeout(() => {
									// uni.redirectTo({
									// 	url:'/packageA/myOrder/myOrder'
									// })
								}, 1000)
								console.log(this.formData);

							}
						}).catch((Error) => {
							console.log(Error);
							console.log(this.formData);
						})
					}

						this.formData.amount = this.toYuan(this.formData.amount);
						this.formData.description = '';
						this.formData.detail_images = [];
						this.refundImages = [];
				}
			}
			//afterType
		},
	}
</script>
<style scoped>
	.popup-return-type ::v-deep .u-navbar--fixed {
		position: relative;
	}
	.explain ::v-deep .apply {

	}
	.classify-content ::v-deep .u-radio {
		margin-bottom: 53rpx;
	}
</style>
<style lang="scss" scoped>
	page {

	}
	.popup-return-type {
		height: 100vh;
		.return-type-list {
			background-color: #fff;
			padding: 16rpx 32rpx 16rpx 32rpx;
			.return-type-item {
				height: 128rpx;
				.return-content {
					padding-left: 32rpx;
					.return-title {
						font-size: 26rpx;
						color: #222;
					}
					.return-desc {
						margin-top: 0.2rem;
						font-size: 24rpx;
						margin-top: 6.4rpx;
						color: #666;
					}
				}
				.lefticon {
					font-size: 48rpx;
				}
				.next {
					.icon-advertise-next {
						font-size: 48rpx;
					}
				}
			}
		}
	}
	.return-goods {
		padding: 32rpx 32rpx 0;
		margin-bottom: 32rpx;
		background: #fff;
		.return-goods-item {
			padding-bottom: 32rpx;
			.goods-img {
				image {
					width: 140rpx;
					height: 140rpx;
				}
			}
			.goods-content {
				height: 140rpx;
				padding-left: 20rpx;
				.goods-price {
					font-size: 22rpx;
					color: #f14e4e;
				}
			}
		}
	}
	.infoMain {
		background-color: #fff;
		.info {
			line-height: 88rpx;
			margin-left:28rpx;
			padding-right: 28rpx;
			background-color: #fff;
			.mode {
				border-bottom: 2rpx solid #ebebeb;
			}
			.cause {
				border-bottom: 2rpx solid #ebebeb;
			}
			.icon-advertise-next {
				font-size: 36rpx;
				color: #999;
				margin-left: 16rpx;
			}

		}
		.prompt {
			line-height: 60rpx;
			padding: 0 28rpx;
			font-size: 24rpx;
			color: #8c8c8c;
			background-color: #F5F5F5;
		}
	}
	.explain {
		padding: 0 28rpx 28rpx 28rpx;
		background-color: #fff;
		.title {
			line-height: 82.5rpx;
		}
		.explain-content {
			background-color: #F5F5F5;
			padding: 0 28rpx 28rpx 0rpx;
			color: #8c8c8c;
			font-size: 24rpx;
			border-radius:8rpx;
			textarea {
				width: 100%;
				height: 120rpx;
				background-color: #F5F5F5;
				color: #8c8c8c;
				font-size: 24rpx;
				border-radius:2rpx;
				padding: 12rpx;
			}
		}
	}
	.submit-refund {
		height: 96rpx;
		line-height: 96rpx;
		font-size: 32rpx;
		width: 80%;
		margin: 0 auto 20rpx auto;
		background: #f15353;
		color: #fff;
		text-align: center;
		border-radius: 60rpx;
	}
	/*弹出框样式*/
	.classify {
		padding: 40rpx 0rpx 0rpx 0rpx;
		.classify-title {
			width: 100%;
			position: relative;
			padding-bottom: 20rpx;
			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				height: 1px;
				width: 100%;
				background-color: #ebedf0;
				-webkit-transform: scaleY(0.5);
				-moz-transform: scaleY(0.5);
				transform: scaleY(0.5);
			}
		}
		.classify-content {
			margin-top: 30rpx;
			padding: 0 64rpx;
		}
		.popup-btn {
			height: 70rpx;
			margin: 64rpx;
			font-size: 28rpx;
			color: #fff;
			background-color: #f14e4e;
			border-radius: 10rpx;
		}
	}


</style>
