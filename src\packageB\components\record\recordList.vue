<!-- 提现记录 -->
<template>
	<view>
		<view class="wallet-tab">
			<!-- <u-tabs :list="tabsList" @change="handleTabChange" :current="current"
					lineColor="#f56c6c"
					ellipsis="false"
					:activeStyle="{
						color: '#f14e4e',
					}" 
					:inactiveStyle="{
						color: '#0c0d0e',
					}"
					itemStyle="height: 70rpx;text-align:center;background:#fff;font-size:26rpx; padding:0 30rpx;"
					lineWidth="26rpx"
			></u-tabs> -->
			<view class="f wallet-tabs">
				<template v-for="item in tabsList">
					<view class="on"  v-if="item.key === current">
						{{ item.name }}
					</view>
					<view v-else @click="handleTabChange(item)">
						{{ item.name }}
					</view>
				</template>
			</view>
		</view>
		<view class="wallet-list" v-if="!this.$isEmpty.isEmpty(this.recordList)">
			<block v-for="(item,index) in recordList" :key="index" >
				<view class="item d-bf" @click="recordDetail(item)">
					<view class="d-f-c font_size12 c-29">
						<view class="mb_30 " v-if="item.withdrawal_type === 1">站内余额</view>
						<view class="mb_30 " v-if="item.withdrawal_type === 2">收入余额</view>
						<view class="font_size11 c-8a">{{item.created_at}}</view>
					</view>
					<view class="d-f-c text-r">
						<view class="mb_30 c-orange">{{item.income_amount}}</view>
						<view class="state" 
							:class="'state' + item.withdrawal_status" 
							v-if="item.withdrawal_status === 0">待审核</view>
						<view class="state stateRemit" v-if="item.remit_status === 0">待打款</view>
						<view class="state stateRemit3" v-if="item.remit_status === 3">打款中</view>
						<view class="state stateRemit1" v-if="item.remit_status === 1">已打款</view>
					</view>
				</view>
			</block>
		</view>
		<view v-else class="bg-white white-pb">
			<u-empty mode="data">
			</u-empty>
		</view>
		<u-popup :show="recordShow" @close="close" @open="open">
			<view class="popup-main">
					<view class="title d-cc">
						<view>提现详情</view>
						<view class="title_close d-f" @click="close">
							<view class="iconfont icon-close11 "></view>
						</view>
					</view>
					<view class="content">
						<view class="attribute d-bf2 mb_54">
							<view class="c-66">提现类型</view>
							<view class="c-20" v-if="recordItem.withdrawal_type === 1">站内余额</view>
							<view class="c-20" v-if="recordItem.withdrawal_type === 2">收入余额</view>
						</view>
						<view class="attribute d-bf2 mb_54">
							<view class="c-66">提现编号</view>
							<view class="c-20">{{recordItem.order_sn}}</view>
						</view>
						<view class="attribute d-bf2 mb_54">
							<view class="c-66">提现方式</view>
							<view class="c-20" v-if="recordItem.withdrawal_mode === 1">手动提现</view>
							<view class="c-20" v-if="recordItem.withdrawal_mode === 2">汇聚提现</view>
						</view>
						<view class="attribute d-bf2 mb_54">
							<view class="c-66">提现时间</view>
							<view class="c-20">{{recordItem.created_at}}</view>
						</view>
						<view class="attribute d-bf2 mb_54">
							<view class="c-66">申请金额</view>
							<view class="c-20" v-if="recordItem.withdrawal_amount">
								￥{{recordItem.withdrawal_amount}}
							</view>
						</view>
						<view class="attribute d-bf2 mb_54">
							<view class="c-66">手续费</view>
							<view class="c-20" v-if="recordItem.poundage_amount">
								￥{{recordItem.poundage_amount}}
							</view>
						</view>
						<view class="attribute d-bf2 mb_54">
							<view class="c-66">劳务税</view>
							<view class="c-20" v-if="recordItem.service_tax">
								￥{{recordItem.service_tax}}
							</view>
						</view>
						<view class="attribute d-bf2 mb_54">
							<view class="c-66">打款金额</view>
							
							<view class="c-20" v-if="recordItem.income_amount">
								￥{{recordItem.income_amount}}
							</view>
						</view>
					</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		name:'recordList',
		props:{
			recordList:Array,
			statur:Number
		},
		data() {
			return {
				recordShow:false,
				recordItem:{},
				tabsList:[
					{name: '全部',key:null},
					{name: '待审核',key: 1},
					{name: '待打款',key: 2},
					{name: '打款中',key: 3},
					{name: '已完成',key: 4},
					{name: '已驳回',key: 5},
					{name: '已失效',key: 6},
				],
				current: null
			}
		},
		mounted(){
			this.current = this.statur
		},
		methods: {
			recordDetail(item) {
				this.recordShow = true;
				this.recordItem = item;
				console.log(this.recordItem.income_amount);
			},
			close() {
				this.recordShow = false;
			},
			handleTabChange(res){
				this.current = res.key
				this.$emit('handleTabChange',res.key)
			},
		}
	}
</script>

<style lang="scss" scoped>
	.wallet-list {
		.item {
			padding: 30rpx 26rpx;
			background-color: #fff;
			border-bottom: 1px solid #f2f2f2;
			&:last-of-type {
				border-bottom: none;
			}
			.state {
				padding: 6rpx 10rpx;
				text-align: center;
				border-radius: 4rpx;
				font-size: 20rpx;
			}
			.state0 {  //待审核
				background-color: #f4ecff;
				color: #7b3dd2;
			}
			.stateRemit {//待打款
				background-color: #fff2f2;
				color: #f15151;
			}
			.stateRemit1 {
				background-color: #ecf6e5;
				color: #5fa92c;
			}
			.stateRemit3 {
				background-color: #e5f0f6;
				color: #089bec;
			}
			
		}
	}
	.popup-main {
		.title {
			padding: 40rpx 0 50rpx 0;
			position: relative;
			color: #202020;
			text-align: center;
			line-height: 36rpx;
			font-size: 34rpx;
		}
		.title_close {
			position: absolute;
			// width: 50rpx;
			height: 50rpx;
			right:30rpx;
			align-items: flex-end;
			// text-align: right;
		}
		.content {
			margin: 0 30rpx;
			.attribute {
				font-size: 28rpx;
				
			}
		}
	}

	.wallet-tab {
		// width:100%;
		padding: 0rpx 4rpx 0rpx 0rpx;
		border-bottom: 1px solid #f2f2f2;
		background-color: #fff;
		
		.wallet-tabs{
			height: 70rpx;
			width: 100%;
			align-items: center;
			overflow-x: scroll;
            overflow-y: hidden;
            white-space:nowrap;

			view {
				height: 40rpx;
				font-size: 26rpx;
				color: #292929;
				margin: 0 10rpx;
				padding: 5rpx 20rpx;
				border-radius: 25rpx;
			}

			.on {
				
				background-color:#EF5452;
				color: rgba(255, 255, 255, 1);
			}
		}
	}
</style>
