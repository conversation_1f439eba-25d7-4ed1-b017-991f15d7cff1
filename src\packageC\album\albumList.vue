<template>
  <view>
    <!-- 搜索框与一级tab切换 start -->
    <view class="head">
      <u-row
        justify="space-between"
        gutter="40"
        customStyle="margin-bottom: 20rpx"
      >
        <u-col span="12">
          <u-search
            height="70"
            searchIconSize="40"
            color="#666666"
            bgColor="#eff0f1"
            v-model="searchValue"
            :placeholder="searchPlaceholder"
            :showAction="false"
            @custom="getAlbumServe"
            @search="getAlbumServe"
          ></u-search>
        </u-col>
        <!-- <u-col span="3">
          <u-button
            type="error"
            size="small"
            shape="circle"
            text="设置"
            @click="navTo('/packageD/smallShop/productSetting')"
          ></u-button>
        </u-col> -->
      </u-row>
      <u-row gutter="40" justify="space-between">
        <u-col span="12">
          <view class="con-tabs">
            <u-tabs
              lineWidth="0"
              :list="tagsTab"
              :currentTagIndex="currentTagIndex"
              :activeStyle="{
                color: '#F11111',
                transform: 'scale(1.05)',
              }"
              :inactiveStyle="{
                color: '#606266',
                transform: 'scale(1)',
              }"
              @change="onChangeTag"
            >
              <view slot="right" class="ml_30 f fac" @click="goClassify">
                更多
                <u-icon name="arrow-right" size="28"></u-icon>
              </view>
            </u-tabs>
          </view>
        </u-col>
      </u-row>
    </view>
    <!-- 搜索框与一级tab切换 end -->
    <!-- 专辑列表 start -->
    <view class="con-album">
      <template v-if="albumsData.length > 0">
        <AlbumList :list="albumsData" :status="loadingStatus"></AlbumList>
      </template>
      <template v-else>
        <view class="empty">
          <u-empty
            mode="data"
            marginTop="100rpx"
            textSize="28rpx"
            iconSize="150"
          ></u-empty>
        </view>
      </template>
    </view>
    <view class="d-cc fs-1 c-5e5e5e mb-25 mt-25">
      <view v-if="isLastPage && albumsData.length != 0">暂无更多~</view>
    </view>
    <!-- 专辑列表 end -->
  </view>
</template>
<script>
import AlbumList from '../components/collectionList.vue'
// import AlbumList from '../smallShop/components/albumList.vue'

export default {
  components: {
    AlbumList,
  },
  data() {
    return {
      isLastPage: false,
      page: 1,
      pageSize: 15,
      total: 0,
      productSetting: {},
      searchValue: '',
      searchPlaceholder: '搜索专辑',
      tag_id: null,
      tag_name: '',
      currentTagIndex: null, // 标签tabs切换索引
      currentAlbumIndex: 0, // 专辑tabs切换索引
      tagsTabApi: '', // 标签tabs接口
      albumsDataApi: '', // 专辑列表接口
      importAllAlbum: '/api/smallShop/album/importAllAlbum', // 导入全部
      importAlbum: '/api/smallShop/album/importAlbum', // 导入/批量导入
      checkboxValue: [], // 已选checkbox
      // 标签tabs数据源
      tagsTab: [
        // { id: null, name: '' }
      ],
      // 专辑tabs数据源
      albumsTab: [{ name: '已选专辑' }, { name: '专辑库' }],
      // 专辑列表数据源
      albumsData: [],
      loadingStatus: [],
    }
  },
  onShow() {
    this.getInit()
    // this.getProductSetting()
  },
  onReachBottom() {
    if (!this.isLastPage) {
      this.page = this.page + 1
      this.getAlbumServe(true)
    }
  },
  methods: {
    // 跳转分类页
    goClassify() {
      this.navTo('/pages/classify/classify')
    },
    async getProductSetting() {
      const { data } = await this.get(
        '/api/smallShop/setting/getShopProductSetting',
      )
      this.productSetting = data.setting.value
    },
    onChangeTag(index) {
      this.currentTagIndex = index.index // 标签tabs切换索引
      this.tag_id = this.tagsTab[this.currentTagIndex].id
      this.tag_name = this.tagsTab[this.currentTagIndex].name
      this.getInit()
    },
    onChangeAlbum(index) {
      this.currentAlbumIndex = index.index // 专辑tabs切换索引
      this.page = 1
      this.getInit()
    },
    getInit() {
      this.getAlbumApi()
      this.getAlbumServe()
      this.getTags()
    },
    getAlbumApi() {
      this.albumsDataApi = '/api/productAlbumApi/getAlbumListByApi'
      this.tagsTabApi = '/api/productAlbumApi/getAllTag'
    },
    getAlbumServe(addList = false) {
      const params = {
        page: this.page,
        pageSize: this.pageSize,
      }
      if (this.searchValue) {
        params.album_name = this.searchValue
      }
      // if (this.tag_id) {
      //   params.tag_id = this.tag_id
      // }
      if (this.tag_name && this.tag_name !== '全部') {
        params.tag_name = this.tag_name
      }
      this.get(this.albumsDataApi, params, true)
        .then(res => {
          if (res.code === 0) {
            // this.albumsData = res.data.list
            if (addList) {
              this.albumsData = [...this.albumsData, ...res.data.list]
            } else {
              this.albumsData = res.data.list
            }
            this.total = res.data.total
            this.isLastPage = this.albumsData.length >= this.total
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    getTags() {
      this.get(this.tagsTabApi, {}, true)
        .then(res => {
          if (res.code === 0) {
            this.tagsTab = res.data.tags
            const all = {
              id: null,
              name: '全部',
            }
            this.tagsTab.unshift(all)
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    onCheckSome(n) {
      this.checkboxValue = []
      this.albumsData.map(element => {
        if (n.includes(element.id)) {
          this.checkboxValue.push(element.id)
        }
        return this.checkboxValue
      })
    },
    // 导入全部
    onClickImportAll() {
      const params = {
        price_proportion: 10000,
      }
      // 协议价 agreement_price => 2 建议零售价 origin_price => 0 指导价 guide_price => 1  营销价 market_price=> activity_price =>3
      if (this.productSetting.is_activity_price) {
        params.price_proportion = this.productSetting.activity_price_ratio * 100
        params.price_type = 3
      }
      if (this.productSetting.is_agreement_price) {
        params.price_proportion =
          this.productSetting.agreement_price_ratio * 100
        params.price_type = 2
      }
      if (this.productSetting.is_guide_price) {
        params.price_proportion = this.productSetting.guide_price_ratio * 100
        params.price_type = 1
      }
      if (this.productSetting.is_origin_price) {
        params.price_proportion = this.productSetting.origin_price_ratio * 100
        params.price_type = 0
      }
      this.getImportServe(this.importAllAlbum, params)
    },
    // 批量导入
    onClickImportSome() {
      if (this.productSetting.tip) {
        this.$refs.albumPricePopup.init(2, this.checkboxValue)
        return
      }
      const params = {
        album_ids: this.checkboxValue,
      }
      const resData = this.getProportionAndType()
      params.price_proportion = resData.price_proportion
      params.price_type = resData.price_type
      // 协议价 agreement_price => 2 建议零售价 origin_price => 0 指导价 guide_price => 1  营销价 market_price=> activity_price =>3
      /* if (this.productSetting.is_activity_price) {
        params.price_proportion = this.productSetting.activity_price_ratio * 100
        params.price_type = 3
      }
      if (this.productSetting.is_agreement_price) {
        params.price_proportion =
          this.productSetting.agreement_price_ratio * 100
        params.price_type = 2
      }
      if (this.productSetting.is_guide_price) {
        params.price_proportion = this.productSetting.guide_price_ratio * 100
        params.price_type = 1
      }
      if (this.productSetting.is_origin_price) {
        params.price_proportion = this.productSetting.origin_price_ratio * 100
        params.price_type = 0
      } */
      this.getImportServe(this.importAlbum, params)
    },
    // 单独导入
    onClickImportSingle(id) {
      uni.$u.throttle(() => {
        if (this.productSetting.tip) {
          this.$refs.albumPricePopup.init(1, id)
          this.$refs.albumPricePopup.formData.activity_price_ratio =
            this.productSetting.activity_price_ratio < 100
              ? 100
              : this.productSetting.activity_price_ratio
          this.$refs.albumPricePopup.formData.agreement_price_ratio =
            this.productSetting.agreement_price_ratio < 100
              ? 100
              : this.productSetting.agreement_price_ratio
          this.$refs.albumPricePopup.formData.guide_price_ratio =
            this.productSetting.guide_price_ratio < 100
              ? 100
              : this.productSetting.guide_price_ratio
          this.$refs.albumPricePopup.formData.origin_price_ratio =
            this.productSetting.origin_price_ratio < 100
              ? 100
              : this.productSetting.origin_price_ratio
          if (this.productSetting.is_activity_price) {
            this.$refs.albumPricePopup.takeGroup = 'is_activity_price'
          }
          if (this.productSetting.is_agreement_price) {
            this.$refs.albumPricePopup.takeGroup = 'is_agreement_price'
          }
          if (this.productSetting.is_guide_price) {
            this.$refs.albumPricePopup.takeGroup = 'is_guide_price'
          }
          if (this.productSetting.is_origin_price) {
            this.$refs.albumPricePopup.takeGroup = 'is_origin_price'
          }
          return
        }
        const params = {
          album_ids: [id],
          // price_proportion: 10000,
          // price_type:0
        }
        const resData = this.getProportionAndType()
        params.price_proportion = resData.price_proportion
        params.price_type = resData.price_type
        // 协议价 agreement_price => 2 建议零售价 origin_price => 0 指导价 guide_price => 1  营销价 market_price=> activity_price =>3
        /* if (this.productSetting.is_activity_price) {
        params.price_proportion = this.productSetting.activity_price_ratio * 100
        params.price_type = 3
      }
      if (this.productSetting.is_agreement_price) {
        params.price_proportion =
          this.productSetting.agreement_price_ratio * 100
        params.price_type = 2
      }
      if (this.productSetting.is_guide_price) {
        params.price_proportion = this.productSetting.guide_price_ratio * 100
        params.price_type = 1
      }
      if (this.productSetting.is_origin_price) {
        params.price_proportion = this.productSetting.origin_price_ratio * 100
        params.price_type = 0
      } */
        this.getImportServe(this.importAlbum, params)
      }, 1000)
    },
    getProportionAndType() {
      const params = {
        price_proportion: 10000,
        price_type: 0,
      }
      // 协议价 agreement_price => 2 建议零售价 origin_price => 0 指导价 guide_price => 1  营销价 market_price=> activity_price =>3
      if (this.productSetting.is_activity_price) {
        params.price_proportion = this.productSetting.activity_price_ratio * 100
        params.price_type = 3
      }
      if (this.productSetting.is_agreement_price) {
        params.price_proportion =
          this.productSetting.agreement_price_ratio * 100
        params.price_type = 2
      }
      if (this.productSetting.is_guide_price) {
        params.price_proportion = this.productSetting.guide_price_ratio * 100
        params.price_type = 1
      }
      if (this.productSetting.is_origin_price) {
        params.price_proportion = this.productSetting.origin_price_ratio * 100
        params.price_type = 0
      }
      return params
    },
    // popup回调导入
    pricePopupSave(params) {
      this.getImportServe(this.importAlbum, params)
    },
    getImportServe(api, params) {
      this.post(api, params, true)
        .then(res => {
          this.toast('导入成功')
          if (api === this.importAlbum) {
            if (res.data.album_ids) {
              this.albumsData.forEach((item, index) => {
                res.data.album_ids.forEach(item2 => {
                  if (item.id === item2) {
                    this.$set(
                      this.albumsData[index],
                      'import_count',
                      this.albumsData[index].import_count + 1,
                    )
                  }
                })
              })
            }
          } else {
            setTimeout(() => {
              this.page = 1
              this.albumsData = []
              this.getInit()
            }, 500)
          }
          this.$refs.albumPricePopup.handleClose()
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    onDeletes() {
      if (this.checkboxValue.length) {
        this.onDelete(null)
      } else {
        this.toast('请选择要删除的专辑')
      }
    },
    onDelete(id) {
      const params = {
        album_ids: [],
      }
      if (this.checkboxValue.length === 0) params.album_ids[0] = id
      if (this.checkboxValue.length !== 0) params.album_ids = this.checkboxValue
      const api = '/api/smallShop/album/removeAlbum'
      this.delete(api, params, true)
        .then(res => {
          this.getInit()
          this.toast(res.msg)
          this.checkboxValue = []
        })
        .catch(Error => {
          console.log(Error)
        })
    },
  },
}
</script>
<style scoped>
::v-deep .u-tabs__wrapper__nav__item__text.data-v-0de61367 {
  font-size: 28rpx;
}
::v-deep .u-button {
  font-size: 28rpx;
}
::v-deep .font_size12 {
  font-size: 28rpx;
}
::v-deep .item-count {
  display: flex;
  flex-direction: column;
}
</style>
<style lang="scss" scoped>
.head {
  padding: 40rpx 30rpx 0 30rpx;
  background-color: #fff;
  // background-image: linear-gradient(#f15353, #f5f5f5);
}
.con-tabs {
  margin-left: -34rpx;
  padding-bottom: 12rpx;
  padding-left: 20rpx;
  padding-right: 10rpx;
}
.con-btn {
  display: flex;
  justify-content: flex-end;
}
.btn-txt {
  height: 68rpx;
}
.con-album {
  margin-top: 36rpx;
  padding: 0 40rpx;
}
.f {
  display: flex;
}
.fw {
  flex-wrap: wrap;
}
.fac {
  align-items: center;
}
</style>
