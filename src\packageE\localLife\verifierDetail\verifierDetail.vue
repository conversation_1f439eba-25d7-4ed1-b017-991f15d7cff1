<template>
    <view>
      <scroll-view class="scroll-Y" scroll-y="true" @scrolltolower="scrolltolower">
        <!-- 搜索框 -->
        <view class="search-box " style="padding-top: 20rpx;">
          <view class="box">
            <view class="f fac pl-25 pr-25 pt-25">
              <view>
                <u-input clearable placeholder="手机号" v-model="tel" customStyle="height: 40rpx;"></u-input>
              </view>
              <view class="ml-20">
                <u-input clearable placeholder="订单编号" v-model="order_sn" customStyle="height: 40rpx;"></u-input>
              </view>
            </view>
            <view class="f fac pl-25 pr-25 pt-25">
              <view class="f1 mr_10">
                <u-input clearable placeholder="商品名称" v-model="product_title" customStyle="height: 40rpx;"></u-input>
              </view>
              <view class="f1 ml_10">
			   	<view class="searchBtn" @click="search">搜索</view>
			   </view>
            </view>
          </view>
        </view>
        <view class="bg-white p-25 f fac fjsb radius15 white-box" @click="datefun">
          <view class="fs-2 f-bold">选择核销时间</view>
          <view class="f fac" style="">
            <view v-if="dateEnd.name === '终止时间'" style="color: #999;">{{ dateStart.name }} - {{ dateEnd.name }}</view>
            <view v-else>{{ dateStart.name }} - {{ dateEnd.name }}</view>
            <view class="fs-3 ml-5 iconfont icon-member_right" style="color: #aaaab3"></view>
          </view>
        </view>
        <template v-for="item in verificationList">
          <view class="product-box" :key="item.id">
            <view class="pl-25 pr-25 date">{{ formatDateTime(item.updated_at)  }}</view>
            <view class="f pl-25 pr-25">
              <view>
                <u-image :src="item.order_items[0].image_url" radius="16rpx" width="172rpx" height="172rpx"></u-image>
              </view>
              <view class="ml-25 mt-10">
                <view class="title">
                  {{ item.order_items[0].title }}
                </view>
                <view class="mt-40 fs-2 red">￥ {{ toYuan(item.order_items[0].amount) }}</view>
              </view>
            </view>
            <view class="pl-25 pr-25 text-box">
              <view class="f fac text">
                <view class="text-name">订单编号</view>
                <view class="text-num">{{ item.order_sn }}</view>
              </view>
              <view class="f fac text">
                <view class="text-name">手机号</view>
                <view class="text-num">{{ item.verification.tel | telFilters }}</view>
              </view>
              <view class="f fac">
                <view class="text-name">券码</view>
                <view class="text-num">{{ item.code }}</view>
              </view>
            </view>
          </view>
        </template>
      </scroll-view>
  
      <!-- 时间选择器 -->
      <u-datetime-picker ref="datetimePicker" confirmColor="#f15353" :show="dateStart.show" v-model="dateStart.date" title="请选择起始时间" mode="date"
        :formatter="formatter" itemHeight="100" @cancel="dateStartCancel" @confirm="confirmStartDate"></u-datetime-picker>
      <u-datetime-picker ref="datetimePicker" confirmColor="#f15353" :show="dateEnd.show" v-model="dateEnd.date" :minDate="minDate"
        title="请选择终止时间" mode="date" :formatter="formatter" itemHeight="100" @cancel="dateEndCancel"
        @confirm="confirmDateEnd"></u-datetime-picker>
    </view>
  </template>
  
  <script>
  export default {
    name: 'verifierDetail',
  
    data() {
      return {
        store_id: null,
        //  搜索条件
        tel: '',
        order_sn: '',
        product_title: '',
        start_at: null,
        end_at: null,
  
        // 时间选择器开关
        dateStart: {
          show: false,
          date: '',
          name: '起始时间',
        },
        dateEnd: {
          show: false,
          date: '',
          name: '终止时间'
        },
        minDate: null,
  
        // 数据列表
        verificationList: [],
        page: 1,
        pageSize: 10,
        total: null,
      }
    },
  
    onLoad(e) {
      if (e.store_id) {
        this.store_id = parseInt(e.store_id)
      }
      this.getDetailsList()
    },
    filters: {
      // 商品状态
      telFilters: function (tel) {
        let str = '';
        if (tel !== '') {
          str = tel.substring(0, 3) + '****' + tel.substring(7)
        }
        
        return str;
      }
    },
    methods: {
      // 初始 确认时间选择器
      confirmStartDate(data) {
        this.minDate = data.value + 86400000
        this.dateStart.date = data.value
        this.dateEnd.show = true
      },
      // 结束 确认时间选择器
      confirmDateEnd(data) {
        this.dateStart.name = this.formatDateTime(this.dateStart.date / 1000, 3);
        this.dateEnd.name = this.formatDateTime(data.value / 1000, 3);
        this.dateEnd.date = data.value;
        this.start_at = this.dateStart.date / 1000;
        this.end_at = this.dateEnd.date / 1000;
        this.dateStart.show = false;
        this.dateEnd.show = false;
      },
      // 打开时间选择器
      datefun() {
        this.dateStart.show = true
      },
      // 关闭时间选择器
      dateStartCancel() {
        this.dateStart.show = false
      },
      // 关闭时间选择器
      dateEndCancel() {
        this.dateEnd.show = false
      },
      // 时间
      formatter(type, value) {
        if (type === 'year') {
          return `${value}年`
        }
        if (type === 'month') {
          return `${value}月`
        }
        if (type === 'day') {
          return `${value}日`
        }
        return value
      },
      // 获取数据
      async getDetailsList() {
        let params = {
          store_id: parseInt(this.store_id),
          page: this.page,
          pageSize: this.pageSize,
        }
        let res = await this.get('/api/localLife/front/getVerificationDetailsVerificationList', params);
        if (res.code === 0) {
          this.verificationList = res.data.list;
          this.total = res.data.total
        }
      },
      // 搜索获取数据
      async search() {
        this.page = 1;
        this.pageSize = 10;
        let params = {
          store_id: parseInt(this.store_id),
          page: this.page,
          pageSize: this.pageSize,
          tel: this.tel,
          order_sn: this.order_sn,
          product_title: this.product_title,
        }
        if (this.start_at) {
          params.start_at = this.start_at
        }
        if (this.end_at) {
          params.end_at = this.end_at
        }
  
        let res = await this.get('/api/localLife/front/getVerificationDetailsVerificationList', params);
        if (res.code === 0) {
          this.verificationList = res.data.list;
          this.total = res.data.total
        }
      },
      // 获取下一页数据
      async scrolltolower() {
        if (this.verificationList.length < this.total) {
          this.page = ++this.page;
          let params = {
            store_id: parseInt(this.store_id),
            page: this.page,
            pageSize: this.pageSize,
            tel: this.tel,
            order_sn: this.order_sn,
            product_title: this.product_title,
          }
          if (this.start_at) {
            params.start_at = this.start_at
          }
          if (this.end_at) {
            params.end_at = this.end_at
          }
          let res = await this.get('/api/localLife/front/getVerificationDetailsVerificationList', params);
          if (res.code === 0) {
            this.verificationList = [...this.verificationList,...res.data.list];
            this.total = res.data.total
          }
        }
      },
    },
  }
  </script>
  
  <style scoped lang="scss">
  .box {
    background-color: #FFFFFF;
    border-radius: 16px 16px 16px 16px;
    width: 702rpx;
    margin: 0 auto 20rpx;
    box-sizing: border-box;
    padding-bottom: 30rpx;
    padding-top: 5rpx;
  }
  
  .white-box {
    width: 702rpx;
    border-radius: 16px 16px 16px 16px;
    background: #FFFFFF;
    margin: 20rpx auto;
    box-sizing: border-box;
  }
  
  .product-box {
    width: 702rpx;
    background-color: #ffffff;
    margin: 0 auto;
    margin-top: 20rpx;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    padding-bottom: 40rpx;
    box-sizing: border-box;
  
    .date {
      font-weight: 500;
      color: #00001C;
      line-height: 34rpx;
      padding-top: 30rpx;
      padding-bottom: 30rpx;
    }
  }
  
  .red {
    color: #f15353;
    font-weight: bold;
  }
  
  .title {
    color: #00001c;
    font-size: 28rpx;
    font-weight: bold;
  }
  
  .text-box {
    margin-top: 80rpx;
  
    .text {
      margin-bottom: 40rpx;
      font-size: 26rpx;
    }
  
    .text-bot {
      font-size: 26rpx;
    }
  
    .text-name {
      width: 190rpx;
      color: #6E6E79;
      font-weight: 400;
    }
  
    .text-num {
      color: #00001C;
      font-weight: 400;
    }
  }
  
  .scroll-Y {
    height: 100vh
  }

  .searchBtn {
	text-align: center;
	padding: 15rpx 0;
	color: #ffffff;
	background-color: #F15353;
	border-radius: 40rpx;
 }
  </style>