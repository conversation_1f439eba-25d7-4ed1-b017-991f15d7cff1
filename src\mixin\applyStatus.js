export const applyStatus = {
  data() {
    return {
      apply_status: 1, // 小商店状态：1-可申请
      tip: '',
      level_ids: [], // 会员等级
    }
  },
  methods: {
    async getApplyStatus() {
      const { code, data } = await this.get(
        '/api/smallShop/apply/getApplyStatus',
        {},
        true,
        false,
        false
      )
      let retData = {
        apply_status:1,
        tip:'',
        level_ids: [],
      }
      if (code === 0) {
        retData.apply_status = data.apply_status
        // this.apply_status = data.apply_status
        this.tip = data.tip
        retData.level_ids = data.level_ids
      }
      return retData;
    },
  },
}
