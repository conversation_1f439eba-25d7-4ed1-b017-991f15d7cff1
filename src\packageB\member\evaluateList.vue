<!-- 多商品售后 -->
<template>
	<view>
		<view class="m-20">
			<view class="mb-10">订单编号：{{orderDetail.order_sn}}</view>
			<u-line></u-line>
			</view>
		
		<!--商品名字和信息 -->
		<view class="goods-box">
			
			<block v-for="(goodsItem,goodsindex) in orderDetail.order_items" :key="goodsindex">
				<view class="goods d-f">
					<view class="img mr_32">
						<image :src="goodsItem.image_url"></image>
					</view>
					<view class="goods_right d-be">
						<view>
							<u--text :lines="2"
										color="#333"
										size="28" 
										lineHeight="36rpx" 
										:text="goodsItem.title">
							</u--text>
							<view class="c-888 mt_20">x {{goodsItem.qty}}</view>
						</view>
						<view class="d-ef">￥{{goodsItem.amount?goodsItem.amount:'0.00'}}</view>
						<view class="evaluate d-ef mt-20" >
							<view class="evaluate-btn" 
										@click="applyAfter(goodsItem.id,goodsItem.amount,goodsItem.refund_status)"
										v-if="goodsItem.after_sales &&
													goodsItem.after_sales.status === 2"
							>
								待商家收货
							</view>
<!-- 							<view class="evaluate-btn"
										@click="applyAfter(goodsItem.id,goodsItem.amount,goodsItem.refund_status)"
										v-else-if="goodsItem.after_sales &&
													goodsItem.after_sales.after_sales_audit &&
													goodsItem.after_sales.after_sales_audit.status === -1 &&
													goodsItem.after_sales.status === -1"
							>
								已关闭
							</view> -->
							<view class="evaluate-btn"
										@click="applyAfter(goodsItem.id,goodsItem.amount,goodsItem.refund_status)"
										v-else-if="goodsItem.after_sales &&
													goodsItem.after_sales.after_sales_audit &&
													goodsItem.after_sales.after_sales_audit.status === -1 && 
													goodsItem.after_sales.status !== -1"
							>
								已驳回
							</view>
							<view class="evaluate-btn"
										@click="applyAfter(goodsItem.id,goodsItem.amount,goodsItem.refund_status)"
										v-else-if="goodsItem.after_sales &&
													goodsItem.after_sales.status === 3"
							>
								待退款
							</view>
							<view class="evaluate-btn"
										@click="applyAfter(goodsItem.id,goodsItem.amount,goodsItem.refund_status)"
										v-else-if="goodsItem.refund_status === 2"
							>
								售后中
							</view>
							<view class="evaluate-btn"  
										@click="applyAfter(goodsItem.id,goodsItem.amount,goodsItem.refund_status)"
										v-else
							>
								申请售后
							</view>
						</view>
					</view>
				</view>
			</block>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				refundId:0,
				orderDetail:{}
			}
		},
		onLoad(options) {
			if((options.refundId??'') !== '') {
				this.refundId = parseInt(options.refundId);
			}
		},
		onShow() {
			uni.$on('refreshData',() => { //返回上一页刷新数据
				this.detail(this.refundId);
			});
			this.detail(this.refundId);
		},
		onUnload() {
		  // 移除监听事件  
		   uni.$off('refreshData');  
		},
		methods: {
			detail(id) {
				this.get(`/api/order/get?id=${id}`, {}, true).then((res) => {
					console.log(res);
					if(res.code === 0) {
						let data = res.data;
						this.orderDetail = data.order;
						this.orderDetail.amount = this.toYuan(data.order.amount);
						let  order_items = this.orderDetail.order_items;
						for(let i in order_items) {
							order_items[i].amount = this.toYuan(order_items[i].amount)
							this.goosAmount = order_items[i].amount
						}
						
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			applyAfter(refundId,refund,stuats) {  //查询售后状态跳转不同
				if(stuats === 2) {
					uni.navigateTo({
						url:`/packageB/member/aftersales?afterSaleId=${refundId}&order=order` 
					})
				} else {
					uni.navigateTo({
						url:`/packageB/member/refund?id=${refundId}` 
					})
				}
				
			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		background-color: #fff;
	}
	.goods-box {
		background-color: #fff;
		// border-radius: 20rpx;
		// margin: 20rpx;
		padding-bottom: 20rpx;
		.goods {
			padding: 20rpx 20rpx 20rpx 32rpx;
			.img{
				width: 140rpx;
				height: 140rpx;
				image {
					width: 140rpx;
					height: 140rpx;
				}
			}
			.goods_right {
				width: 100%;
			}
			.evaluate {
				.evaluate-btn {
					color: #f15353;
					border-radius: 30rpx;
					// margin-right: 20rpx;
					padding: 8rpx 20rpx;
					border: 2rpx solid #f15353;
				}
			}
		}
		
	}
</style>
