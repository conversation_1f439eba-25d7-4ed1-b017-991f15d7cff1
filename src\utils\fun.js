// url转对象
export function getUrlParametersAll(url) {
    if (!url.includes('?')) return url
    let result = {}
    if (url.indexOf('?') !== -1) {
        let str = url.substr(url.indexOf('?') + 1)
        let strs = str.split('&')
        for (let i = 0; i < strs.length; i++) {
            result[strs[i].split('=')[0]] = decodeURI(strs[i].split('=')[1])
        }
    }
    return result
}
// 处理富文本图片
export function repairRichText(html) {
    let detail_images = html
    detail_images = detail_images.replace(
        /<img/g,
        '<img style="max-width:100%;height:auto;display:block"',
    )
    detail_images = detail_images.replace(/figure/g, 'div')
    return detail_images
}
