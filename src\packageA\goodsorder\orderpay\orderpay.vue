<!-- 支付页 -->
<template>
  <view>
    <!--支付详情 -->
    <view class="pay-box">
      <view class="pay-money">
        <view class="top">支付金额:</view>
        <view class="mid">￥{{ amount || 0.0 }}</view>
        <view class="bottom">支付流水号:{{ pay_sn }}</view>
      </view>
    </view>

    <view class="mb40"></view>

    <view id="payBtnList">
      <view class="title d-cf">
        <view class="line"></view>
        <view>支付方式</view>
      </view>
      <view class="mb_96">
        <u-radio-group
          v-if="amount != 0 || item.code == 2"
          v-model="payRadioList"
          placement="column"
          v-for="(item, index) in payList"
          :key="index"
        >
          <view class="mod_btns d-bf">
            <view class="mod_btn d-cf">
              <text class="iconfont icon-pay_yue" v-if="item.code === 2"></text>
              <text class="iconfont icon-pay_yue" v-if="item.code === 3"></text>
              <text
                class="iconfont icon-pay_default"
                v-if="item.code === 1"
              ></text>
              <text
                class="iconfont icon-pay_wechat"
                v-if="item.code === 9999"
              ></text>
              <text
                class="iconfont icon-pay_default"
                v-if="item.code === 11"
                style="font-size: 60rpx"
              ></text>
              <text
                class="iconfont icon-pay_wechat"
                v-if="item.code === 12"
                style="font-size: 52rpx"
              ></text>
              <text
                class="iconfont icon-pay_wechat"
                v-if="item.code === 10"
                style="font-size: 60rpx; margin-right: 22rpx"
              ></text>
              <text
                class="iconfont icon-pay_wechat"
                v-if="item.code === 9998"
                style="font-size: 60rpx; margin-right: 22rpx; color: #008000"
              ></text>
              <text
                class="iconfont icon-pay_default"
                v-if="item.code === 7000"
                style="font-size: 60rpx"
              ></text>
              <text
                class="iconfont icon-pay_wechat"
                v-if="item.pay_type === 'WECHAT'"
                style="font-size: 60rpx"
              ></text>
              <text
                class="iconfont icon-pay_default"
                v-if="item.pay_type === 'QUICKPAY'"
                style="font-size: 60rpx"
              ></text>
              <text
                class="iconfont icon-pay_alipay"
                v-if="item.pay_type === 'ALIPAY'"
                style="font-size: 60rpx"
              ></text>
              <text
                class="iconfont icon-pay_default"
                v-if="item.pay_type === 'MINIAPP_PAY'"
                style="font-size: 60rpx"
              ></text>
              <text
                class="iconfont icon-pay_default"
                v-if="item.pay_type === 'CASH'"
                style="font-size: 60rpx"
              ></text>
              <text
                class="iconfont icon-pay_default"
                v-if="item.pay_type === 'AGGR_CASHIER'"
                style="font-size: 60rpx"
              ></text>
              <view>
                <text>{{ item.name }}</text>
                <view v-if="item.code === 2">
                  可用:
                  <text class="c-orange">
                    {{ balanceTypeOne.purchasing_balance || 0.0 }}
                  </text>
                  元
                </view>
                <view v-if="item.code === 1">
                  可用:
                  <text class="c-orange">
                    {{ balanceTypeOne.converge_balance || 0.0 }}
                  </text>
                  元
                </view>
              </view>
            </view>
            <u-radio
              @change="radioChange(item.code, item.url, item.type, item.pay_type)"
              size="40"
              activeColor="#f15353"
              :name="item.code"
            ></u-radio>
          </view>
        </u-radio-group>
      </view>
    </view>
    <view class="newSubTn" @click="$clicks(payment)">立即支付</view>
    <u-modal
      :show="payShow"
      :showCancelButton="true"
      :asyncClose="true"
      :content="content"
      @confirm="payConfirm"
      @cancel="paycancel"
      ref="uModal"
    >
      <view class="slot-content">
        <rich-text :nodes="content"></rich-text>
      </view>
    </u-modal>
    <payment-popup
      :paymentShow="paymentShow"
      :payImg="payImg"
      @payClose="payClose"
    ></payment-popup>
    <web-view v-if="ju_he_href" :src="ju_he_href"></web-view>
  </view>
</template>

<script>
import paymentPopup from '@/components/paymentPopup.vue'
export default {
  components: {
    paymentPopup,
  },
  data() {
    return {
      // 基本案列数据
      payList: [],
      orderIds: [],
      balanceList: [], // 余额数据
      balanceTypeOne: {
        // 余额显示
        type: null,
        purchasing_balance: 0, // 站内余额
        converge_balance: 0, // 汇聚余额
      },
      pollingState: '', // 轮询
      payShow: false,
      paymentShow: false,
      urlResult: '',
      payImg: '',
      // u-radio-group的v-model绑定的值如果设置为某个radio的name，就会被默认选中
      payRadioList: 2,
      amount: 0,
      pay_info_id: 0,
      pay_type: 0, // 1-汇聚余额 2-站内余额 -3-采购端库存支付 10-微信支付 7000-拉卡拉收银支付 9998-汇聚plus支付 9999-汇聚微信支付
      pay_url: null, // 请求地址
      pay_sn: '',
      content: '是否支付',
      title: '支付',
      currentPlatform: '', // 当前平台
      ju_he_type: '',
      ju_he_pay_type: '',
      app_id: '',
      ju_he_href: '', // 聚合拉卡拉支付地址
    }
  },
  onShow() {
    const orderIds = uni.getStorageSync('orderIds')
    if ((orderIds ?? '') !== '') {
      this.orderIds = orderIds
    }
    this.cashier()
    this.getUserBalance()
    this.getWechatAppid()
    /* const { referrerInfo } = wx.getEnterOptionsSync();
    console.log('asdsadasdas',referrerInfo, "==在 referrerInfo 中可获取交易小程序返回过来的参数=="); */
  },
  onLoad() {
    // #ifdef H5
    let meta_referrer = document.querySelector('#meta_referrer')
    meta_referrer.setAttribute('content', 'unsafe-URL')
    // #endif
    this.payListData()
  },
  methods: {
    // 获取H5调用微信JS配置
    async getWechatAppid() {
      const res = await this.get('/api/wechatofficial/wechatAppid')
      if (res.code === 0) {
        if (res.data.is_open === 1) {
          this.app_id = res.data.app_Id
        }
      } else {
        this.toast('请先登录')
      }
    },
    paycancel() {
      this.payShow = false
    },
    payConfirm() {},
    radioChange(code, url, type,pay_type) {
      this.pay_type = code
      this.pay_url = url
      this.ju_he_type = type
      this.ju_he_pay_type = pay_type
    },
    payClose() {
      this.paymentShow = false
      clearInterval(this.pollingState)
    },
    cashier() {
      uni.removeStorageSync('redirect_page')
      this.post(
        '/api/trade/cashier',
        {
          order_ids: this.orderIds,
        },
        true,
      )
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            // this.payList = data;
            this.amount = this.toYuan(data.pay_info.amount)
            this.pay_info_id = data.pay_info.id
            this.pay_sn = data.pay_info.pay_sn
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          // console.log(Error);
        })
    },
    wechatPayBlend(pay_type) {
      // 微信小程序支付/h5/公众号支付
      const json = {
        pay_type,
        pay_info_id: this.pay_info_id,
      }

      // #ifdef H5
      console.log(this.obtainingDeviceInformation(['platform']))
      if (this.obtainingDeviceInformation(['platform']).platform == 'ios') {
        json.type = 'iOS'
        // json.pay_type = 11
      } else if (
        this.obtainingDeviceInformation(['platform']).platform == 'android'
      ) {
        json.type = 'Android'
        // json.pay_type = 11
      } else {
        json.type = 'Android'
        // json.pay_type = 11
      }
      // #endif
      const that = this
      this.post('/api/finance/wechatPayment', json, true, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            // 支付后的跳转链接
            let jumpUrl
            let json = {}
            if (pay_type == 11) {
              // 微信H5支付
              window.location.href =
                data.h5_url +
                '&redirect_url=' +
                document.location.protocol +
                '//' +
                window.location.host +
                '/h5/?menu%23/packageA/myOrder/myOrder'
              return
            }
            if (pay_type == 12) {
              // 公众号支付
              const pageUrl = window.location.href.replace('#', '?menu#')
              const config = {
                appId: data.pay.appId,
                beta: false,
                debug: false,
                nonceStr: data.pay.nonceStr,
                signature: data.pay.paySign,
                timestamp: data.pay.timeStamp,
                url: pageUrl,
              }
              window.jweixin.config(config)
              json = {
                appId: data.pay.appId,
                nonceStr: data.pay.nonceStr,
                package: data.pay.package,
                timestamp: data.pay.timeStamp,
                paySign: data.pay.paySign,
                signType: data.pay.signType,
              }
              window.jweixin.chooseWXPay({
                appId: json.appId,
                timestamp: json.timestamp, // 支付签名台生成签
                nonceStr: json.nonceStr, // 支付签名随机串，不长于 32 位
                package: json.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=***）
                signType: json.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
                paySign: json.paySign, // 支付签名
                success: res => {
                  // 支付成功后的回调函数
                  if (res.errMsg == 'chooseWXPay:ok') {
                    that.toast('支付成功')
                    setTimeout(() => {
                      uni.redirectTo({
                        url: '/packageA/myOrder/myOrder',
                      })
                    }, 1000)
                  }
                },
                cancel: res => {
                  // 支付取消
                  that.toast('支付取消')
                },
                fail: res => {
                  that.toast('支付失败')
                  setTimeout(() => {
                    uni.redirectTo({
                      url: '/packageA/myOrder/myOrder',
                    })
                  }, 1000)
                },
              })
              return
            }
            if (pay_type == 10) {
              // 微信小程序支付
              uni.requestPayment({
                provider: 'wxpay',
                timeStamp: data.timeStamp,
                nonceStr: data.nonceStr,
                package: data.package,
                signType: data.signType,
                paySign: data.paySign,
                success: function (res) {
                  that.toast('支付成功')
                  setTimeout(() => {
                    uni.redirectTo({
                      url: '/packageA/myOrder/myOrder',
                    })
                  }, 1000)
                },
                fail: function (err) {
                  that.toast('支付失败')
                  setTimeout(() => {
                    uni.redirectTo({
                      url: '/packageA/myOrder/myOrder',
                    })
                  }, 1000)
                },
              })
            }
          } else {
            that.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    getPayStatus() {
      // 获取支付状态
      this.post('/api/finance/getPayStatus', {
        pay_sn: this.pay_sn,
      }).then(res => {
        if (res.data.pay_status == 1) {
          clearInterval(this.pollingState) // 停止轮询
          this.paymentShow = false
          this.showText('充值成功')
        }
      })
    },
    getUserBalance() {
      // 获取收入
      this.post('/api/finance/getUserBalance', {}, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            this.balanceList = data
            for (let i = 0; i < this.balanceList.length; i++) {
              if (this.balanceList[i].type === 1) {
                this.balanceTypeOne.type = this.balanceList[i].type
                this.balanceTypeOne.converge_balance =
                  this.balanceList[i].purchasing_balance // 汇聚余额，要单独取出来
              } else if (this.balanceList[i].type === 2) {
                this.balanceTypeOne.purchasing_balance =
                  this.balanceList[i].purchasing_balance // 站内余额
              }
            }
            this.balanceTypeOne.converge_balance = this.toYuan(
              this.balanceTypeOne.converge_balance,
            )
            this.balanceTypeOne.purchasing_balance = this.toYuan(
              this.balanceTypeOne.purchasing_balance,
            )
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          // console.log(Error);
        })
    },
    payListData() {
      this.platformToJudge()
      console.log(this.currentPlatform)
      // 修改支付方式
      let params = {}
      // #ifdef  MP-WEIXIN
      params.platform = 'miniProgram'
      // #endif
      // #ifdef H5
      params.platform = 'h5'
      // #endif
      this.post('/api/payment/getPayment', params, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data.PaymentList
            this.payRadioList = data[0].code
            data.forEach((item, index) => {
              /**
               * item.type.indexOf(this.currentPlatform) != -1  
               * 因后台改为勾选展示支付方式 type类型判断形式暂时注释,日后有问题与后台共同再做修改
               *  */
                this.payList.push(item)
              // 不同端显示不同支付方式
              /* if (
                item.type.indexOf(this.currentPlatform) != -1 ||
                item.type == 'all'
              ) {
                this.payList.push(item)
              } */
            })
            this.pay_type = this.payList && this.payList.length ? this.payList[0].code : 0 ;
            this.ju_he_type = this.payList && this.payList.length ? this.payList[0].type : 0 ;
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          // console.log(Error);
        })
    },
    wechatPay() {
      const amount = this.toFen(this.amount)
      let _payType = null
      // #ifdef  H5
      _payType = 11
      // #endif
      // #ifdef  MP-WEIXIN
      _payType = 4
      // #endif
      const that = this
      if (_payType) {
        this.post(
          '/api/finance/wechatPay',
          {
            amount,
            pay_sn: this.pay_sn,
            pay_type: _payType,
            pay_code: 3001,
          },
          true,
        )
          .then(res => {
            if (res.code === 0) {
              const data = res.data
              if (_payType === 11) {
                // 微信H5支付
                window.location.href =
                  data.rc_Result +
                  '&redirect_url=' +
                  document.location.protocol +
                  '//' +
                  window.location.host +
                  '/h5/?menu%23/packageA/myOrder/myOrder'
                return
              }
              if (_payType === 4) {
                // 微信小程序支付
                const _data = JSON.parse(data.rc_Result)
                uni.requestPayment({
                  provider: 'wxpay',
                  timeStamp: _data.timeStamp,
                  nonceStr: _data.nonceStr,
                  package: _data.package,
                  signType: _data.signType,
                  paySign: _data.paySign,
                  success: function (res) {
                    that.toast('支付成功')
                    setTimeout(() => {
                      uni.redirectTo({
                        url: '/packageA/myOrder/myOrder',
                      })
                    }, 1000)
                  },
                  fail: function (err) {
                    that.toast('支付失败')
                    setTimeout(() => {
                      uni.redirectTo({
                        url: '/packageA/myOrder/myOrder',
                      })
                    }, 1000)
                  },
                })
              }
              // this.toast(res.msg);
              /* this.payImg = data.rd_Pic;
							this.urlResult = data.rc_Result;
							let url = data.rc_Result;
							this.paymentShow = true;
							this.pollingState = setInterval(this.getPayStatus, 2000, res.data.r2_OrderNo); //轮询获取充值状态 */
            } else {
              this.toast(res.msg)
            }
          })
          .catch(Error => {
            console.log(Error)
          })
      } else {
        this.toast('系统错误')
      }
    },
    StationPay() {
      let amount = this.toFen(this.amount)
      this.post(
        '/api/finance/balanceDeduction',
        {
          amount,
          pay_info_id: this.pay_info_id,
          pay_type: this.pay_type,
        },
        true,
        true,
      )
        .then(res => {
          if (res.code === 0) {
            let data = res.data
            this.toast(res.msg)
            setTimeout(() => {
              uni.redirectTo({
                url: '/packageA/myOrder/myOrder',
              })
            }, 1000)
          } else {
            this.toast(res.msg)
            setTimeout(() => {
              uni.redirectTo({
                url: '/packageA/myOrder/myOrder',
              })
            }, 1000)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    payment() {
      if (this.pay_type == 1 || this.pay_type === 2 || this.pay_type === -3  || this.pay_type === 3) {
        this.StationPay()
      } else if (this.pay_type === 9999) {
        this.wechatPay()
      } else if (this.pay_type === 10) {
        this.wechatPayBlend(this.pay_type)
      } else if (this.pay_type === 12) {
        this.wechatPayBlend(this.pay_type)
      } else if (this.pay_type === 11) {
        this.wechatPayBlend(this.pay_type)
      } else if (this.pay_type === 7000) {
        this.lakalaPay()
      } else if (this.pay_type === 9998) {
        this.wechatPayPlus()
      }else if (this.ju_he_type === '30000') {
        this.juHePay()
      } else {
        this.toast('请选择支付方式')
      }
    },
    // 拉卡拉支付
    lakalaPay() {
      const params = {
        amount: this.toFen(this.amount),
        pay_sn: this.pay_sn.toString(),
        pay_type: 7000,
      }
      this.post('/api/' + this.pay_url, params, true).then(res => {
        // #ifdef H5
        // window.open(res.data.url, '_blank')

        window.location.href = res.data.url
        // window.location.protocol +
        // '//' +
        // window.location.host +
        // '/h5/?menu#/packageB/webView/webView?src=' +
        // res.data.url
        // #endif
        // #ifdef MP-WEIXIN
        wx.navigateToMiniProgram({
          appId: 'wx889424d565967811',
          path: `payment-cashier/pages/checkout/index?source=WECHATMINI&counterUrl=${encodeURIComponent(
            res.data.url,
          )}`,
          envVersion: 'trial',
          // release: 正式版  trial: 体验版
          success(res) {
            // 打开成功
            console.log('打开成功')
            uni.redirectTo({
              url: '/packageA/myOrder/myOrder',
            })
          },
          fail(err) {
            console.log('打开失败', err)
          },
        })
        // #endif
      })
    },
    // 汇聚plus支付
    wechatPayPlus() {
      const params = {
        amount: this.toFen(this.amount),
        pay_sn: this.pay_sn.toString(),
        pay_type: 9998,
      }
      this.post('/api/' + this.pay_url, params, true).then(res => {
        const appId = JSON.parse(res.data.rc_Result).tradeAppid
        console.log('sadasdas', appId)
        wx.navigateToMiniProgram({
          appId: appId, // 交易小程序的 APPID（申请为商户配置）
          path: '/pages/payIndex/payIndex',
          // 传参
          extraData: {
            trx_no: res.data.r7_TrxNo, // 交易流水号，必填
            order_amount: res.data.r3_Amount, // 交易金额，必填
          },
          envVersion: 'release',
          success(res) {
            // 打开成功
            console.log('跳转小程序成功！', res)
            uni.redirectTo({
              url: '/packageA/myOrder/myOrder',
            })
          },
          fail(err) {
            console.log('打开失败', err)
          },
        })
      })
    },
    // 聚合支付
    async juHePay() {
      // let herf = window.location.origin + '/h5/?menu#' + '/packageA/myOrder/myOrder'    
      const data = {
        amount: this.toFen(this.amount),
        pay_info_id: this.pay_info_id,
        pay_type: this.pay_type,
        // M - 公众号支付  M-H5 - H5网页支付 W - 微信小程序支付
        // #ifdef H5
        type: 'M',
        // #endif
        // #ifdef MP-WEIXIN
        type: 'W'
        // #endif
      }
      const res = await this.post('/api/finance/aggregatePaymentPay', data)
      if (this.ju_he_pay_type === 'WECHAT') {
        if (res.code === 0) {
          // #ifdef H5
          let json = {}
          if (this.app_id) {
            const config = {
              appId: res.data.fields.app_id,
              nonceStr: res.data.fields.nonce_str,
              signature: res.data.fields.pay_sign,
              timestamp: res.data.fields.time_stamp,
              jsApiList: ["chooseWXPay"] ,
              beta: false,
              debug: false,
              // url: herf,
            }
            console.log('wqewqeqwewqeqweqweqweq', config);
            jweixin.config(config)
            json = {
              appId: res.data.fields.app_id,
              nonceStr: res.data.fields.nonce_str,
              package:  res.data.fields.package,
              timestamp: res.data.fields.time_stamp,
              paySign: res.data.fields.pay_sign,
              signType: res.data.fields.sign_type,
            }
            jweixin.chooseWXPay({
              appId: json.appId,
              timestamp: json.timestamp, // 支付签名台生成签
              nonceStr: json.nonceStr, // 支付签名随机串，不长于 32 位
              package: json.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=***）
              signType: json.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
              paySign: json.paySign, // 支付签名
              success: res => {
                // 支付成功后的回调函数
                if (res.errMsg == 'chooseWXPay:ok') {
                  this.toast('支付成功')
                  setTimeout(() => {
                    uni.redirectTo({
                      url: '/packageA/myOrder/myOrder',
                    })
                  }, 1000)
                }
              },
              cancel: res => {
                // 支付取消
                this.toast('支付取消')
              },
              fail: res => {
                this.toast('支付失败')
                setTimeout(() => {
                  uni.redirectTo({
                    url: '/packageA/myOrder/myOrder',
                  })
                }, 1000)
              },
            })
            return
          } else {
            window.location.href = res.data.fields.counter_url
          }
          // #endif
          // #ifdef MP-WEIXIN
          if(res.data.fields.miniapp_app_id && res.data.fields.jump_type === 2) {
            //微信小程序支付
            wx.navigateToMiniProgram({
            appId: res.data.fields.miniapp_app_id,
            path: res.data.fields.counter_url,
            envVersion: 'trial',
            // release: 正式版  trial: 体验版
            success(res) {
              // 打开成功
              console.log('打开成功')
              uni.redirectTo({
                url: '/packageA/myOrder/myOrder',
              })
            },
              fail(err) {
                console.log('打开失败', err)
              },
            })
          } else {
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: res.data.fields.time_stamp,
              nonceStr: res.data.fields.nonce_str,
              package: res.data.fields.package,
              signType: res.data.fields.sign_type,
              paySign: res.data.fields.pay_sign,
              success: function (res) {
                that.toast('支付成功')
                setTimeout(() => {
                  uni.redirectTo({
                    url: '/packageA/myOrder/myOrder',
                  })
                }, 1000)
              },
              fail: function (err) {
                that.toast('支付失败')
                setTimeout(() => {
                  uni.redirectTo({
                    url: '/packageA/myOrder/myOrder',
                  })
                }, 1000)
              },
            })
          }
          // #endif
        } else {
          this.toast(res.msg)
        }
      } else if (this.ju_he_pay_type === 'ALIPAY') {
        window.location.href = res.data.fields.counter_url
      } else if (this.ju_he_pay_type === 'MINIAPP_PAY') {
        wx.navigateToMiniProgram({
          appId: res.data.fields.miniapp_app_id, // 交易小程序的 APPID（申请为商户配置）
          path: res.data.fields.counter_url,
          envVersion: 'release',
          success(res) {
            // 打开成功
            console.log('跳转小程序成功！', res)
            uni.redirectTo({
              url: '/packageA/myOrder/myOrder',
            })
          },
          fail(err) {
            console.log('打开失败', err)
          },
        })
      } else if (this.ju_he_pay_type === 'AGGR_CASHIER') {
        // #ifdef H5
        window.location.href = res.data.fields.counter_url
        // #endif
        // #ifdef MP-WEIXIN
        wx.navigateToMiniProgram({
          appId: res.data.fields.miniapp_app_id, // 交易小程序的 APPID（申请为商户配置）
          path: res.data.fields.counter_url,
          envVersion: 'release',
          success(res) {
            // 打开成功
            console.log('跳转小程序成功！', res)
            uni.redirectTo({
              url: '/packageA/myOrder/myOrder',
            })
          },
          fail(err) {
            console.log('打开失败', err)
          },
        })
        // #endif
      }  else if (this.ju_he_pay_type === 'CASH') {
        // #ifdef H5
        window.location.href = res.data.fields.counter_url
        // #endif
        // #ifdef MP-WEIXIN
        this.ju_he_href = res.data.fields.counter_url
        // #endif
      } else {
        window.location.href = res.data.fields.counter_url
      }
    },
    platformToJudge() {
      // 0PC 1H5 2微信小程序 3微信公众号
      // #ifdef H5
      if (window.innerWidth > 768) {
        this.currentPlatform = '0'
      } else {
        this.currentPlatform = '1'
      }
      // #endif

      // #ifdef MP-WEIXIN
      this.currentPlatform = '2'
      // #endif

      if (this.checkWenxin()) {
        this.currentPlatform = '3'
      }
    },
    getPhoneEnv() {
      const u = navigator.userAgent
      const isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1 // g
      const isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) || this.isApp() // ios终端或者云打包
      const isIpad =
        u.indexOf('Intel Mac OS') > -1 &&
        u.indexOf('Chrome') === -1 &&
        u.indexOf('Safari') === -1 &&
        u.indexOf('Firefox') === -1
      if (isAndroid) {
        return '2'
      } else if (isIOS || isIpad) {
        return '1'
      } else {
        return '3'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.pay-box {
  background: #fff;
  margin: 18rpx 32rpx;

  .pay-money {
    margin: 0 auto;
    padding: 40rpx 0;
    text-align: center;
    border-radius: 10rpx;

    .top {
      color: #999;
      font-size: 12px;
    }

    .mid {
      color: #f76d6d;
      font-size: 48rpx;
      margin: 32rpx 0;
    }

    .bottom {
      font-size: 12px;
      color: #999;
    }
  }
}

#payBtnList {
  margin: 0 32rpx 80rpx 32rpx;
  padding: 24rpx 0;
  border-radius: 10rpx;
  background-color: #fff;
  overflow: hidden;

  .title {
    color: #f87070;
    font-size: 28rpx;
    margin-bottom: 32rpx;

    .line {
      height: 28rpx;
      width: 6rpx;
      background-color: #f87070;
      display: inline-block;
      margin-right: 28rpx;
    }
  }

  .mod_btns {
    border-bottom: 1px solid #f4f4f4;
    text-align: left;
    margin: 0 10rpx;
    padding: 28rpx 0 28rpx 18rpx;

    .icon-pay_yue {
      font-size: 56rpx;
      color: #ff7433;
      margin-right: 32rpx;
    }

    .icon-pay_otherpay {
      font-size: 56rpx;
      color: #ffba00;
      margin-right: 32rpx;
    }

    .icon-pay_wechat {
      font-size: 56rpx;
      color: green;
      margin-right: 32rpx;
    }

    .icon-pay_remittance {
      font-size: 56rpx;
      color: #ff692f;
      margin-right: 32rpx;
    }

    .icon-pay_default {
      font-size: 56rpx;
      color: #2f9cff;
      margin-right: 32rpx;
    }

    .icon-pay_alipay {
      color: #29a1f7;
      font-size: 56rpx;
      margin-right: 32rpx;
    }
  }
}

.newSubTn {
  width: 80%;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  position: fixed;
  bottom: 24rpx;
  left: 10%;
  border-radius: 8px;
  background-color: #f15353;
  color: #fff;
}
</style>
