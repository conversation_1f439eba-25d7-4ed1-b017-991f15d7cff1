<!-- 订单评论详情 -->
<template>
	<view>
		<view class="appraiseList">
			<block v-for="(item,index) in commentList" :key="index">
				<view class="appraise">
					<view class="user d-cf">
						<view class="userimg">
							<u-avatar :src="item.avatar"></u-avatar>
						</view>
						<view class="name">
							<view>{{item.nickname}}</view>
							<view class="level-name">{{item.level}}</view>
						</view>
						<view class="user-time">
							{{item.created_at}}
						</view>
					</view>
					<view class="product-attr">{{item.content}}</view>
					<view>
						<view style="margin: 16rpx 0px;">晒图:</view>
						<view class="product-img d-wrap">
							<block v-for="(imgItem,imgIndex) in item.commentImg" :kye="imgIndex">
								<image :src="imgItem"></image>
							</block>
							
						</view>
					</view>
				</view>
				<view class="score">
					<view class="d-bf ">
						<view class="pjallTitle">商品评分:</view>
						<uni-rate v-model="item.level" active-color="red" :readonly="true" />
					</view>
				</view>
				<view class="pjgoods">
					<view class="goods d-f">
						<view class="img">
							<image :src="item.image_url"></image>
						</view>
						<view class="warp d-f">
							<view class="inner">
								<view class="name">{{item.title}}</view>
								<view class="option">规格: {{item.product_attr}}</view>
							</view>
							<view class="price">
								<view class="product_price">￥{{item.order_item.amount.toFixed(2) || 0.00}} </view>
								<view class="product_price">×{{item.order_item.qty}}</view>
							</view>
						</view>
					</view>
				</view>
			</block>
		</view>
		<view class="comment-all" v-if="!$isEmpty.isEmpty(CommentChildList)">
			<view class="tilse">
				<text class="iconfont icon-ht_list_line_allmessage"></text>
				<text>全部评论</text>
			</view>
				<block v-for="(item,index) in commentList" :key="index">
					<view class="comment-appraise">
						<view class="user d-bf">
							<view class="d-cf">
								<view class="userimg">
									<u-avatar :src="commentItem.avatar"></u-avatar>
								</view>
								<view class="name">
									<view>{{item.nickname}}</view>
									<view class="level-name">{{item.user.user_level.name}}</view>
								</view>
							</view>
							
							<view class="user-time">
								{{item.created_at}}
							</view>
						</view>
						<view class="product-attr">{{item.content}}</view>
					</view>
					<view class="reply-comment d-bf">
						<view class="pjtime">{{item.created_at}}</view>
						<view class="replyOn" @click="replyOn(item.nickname,item.id)">回复</view>
					</view>
				</block>
				
				<!-- 子评论 -->
				<view class="comments-child">
					<block v-for="(ChildItem,ChildIndex) in CommentChildList" :key="ChildItem.id">
						<view class="comment-appraise child-bg">
							<view class="user d-bf">
								<view class="d-cf">
									<view class="userimg">
										<u-avatar :src="ChildItem.avatar"></u-avatar>
									</view>
									<view class="name">
										<view>{{ChildItem.nickname}}</view>
										<!-- <view class="level-name">{{ChildItem.user.user_level.name}}</view> -->
									</view>
								</view>
								
								<view class="user-time">
									{{ChildItem.created_at}}
								</view>
							</view>
							<view class="product-attr">
								 {{ChildItem.parent_comment.nickname}} 回复 {{ChildItem.user.nickname}} :{{ChildItem.content}}
							</view>
							<view class="product-time d-bf">
								<view class="pjtime">{{ChildItem.parent_comment.created_at}}</view>
								<view class="replyOn" @click="childReplyOn(ChildItem.user.nickname,ChildItem.id)">回复</view>
							</view>
						</view>
					</block>
				</view>
		</view>
		<view class="comment-gh"></view>
		<view class="callback d-cc">
			<u--input
			    :placeholder="`回复@昵称:${this.nickname}`"
			    border="surround"
					v-model="formData.content"
			  ></u--input>
				<view class="replyBtn" @click="replyComment">提交</view>
				<view class="share-btn" open-type="share" @click="$store.commit('upShadeShow',true)">分享</view>
				
		</view>
		<shade></shade>
	</view>
</template>

<script>
	import shade from "../../common/shade/shade.vue";
	export default {
		components:{
			shade
		},
		data() {
			return {
				formData:{
					content:'',  //评价内容
					avatar: '', //用户头像
					nickname: '', //昵称
					type: 2, //1评论2回复
					commentId: 0, //被回复的评论id
					first_comment_id:0 //评论ID
				},
				commentId:0,//ID固定不变
				commentImg:[],
				nickname:'',
				childPage:{
					page:1,
					pageSize:20
				},
				user:{}, //用户信息
				commentItem:{},
				// replyValue:'',
				pages: {
					productId:0,
					page:1,
					pageSize:5
				},
				commentList:[],
				CommentChildList:[],
			}
		},
		onLoad(options) {
			console.log(options);
			if((options.productId??'') !== '') {
				this.pages.productId = parseInt(options.productId);
			}
		},
		onShow() {
			this.commentDetail();
			
		},
		onShareAppMessage() {
			let str = ''
            if (uni.getStorageSync('user')) {
              let pid = parseInt(uni.getStorageSync('user').id);
              str = "&invite_code=" + pid
            }
			return {
				title: "评论详情",
				path: '/packageB/member/orderCommentDetails?item='+this.commentList + str
			}
		},
		onShareTimeline() { // 分享朋友圈
			let query = ''
            if (uni.getStorageSync('user')) {
              let pid = parseInt(uni.getStorageSync('user').id);
              query = "&invite_code=" + pid
            }
			return {
				title: "评论详情",
				path: '/packageB/member/orderCommentDetails?item='+this.commentList,
				query
			};
		},
		onUnload(){
			this.$store.commit('upShadeShow',false)
		},
		methods: {
			commentDetail() { //商品详情
				this.get(`/api/comment/list?productId=${this.pages.productId}&page=${this.pages.page}&pageSize=${this.pages.pageSize}`, {}, true).then((res) => {
					console.log(res);
					if(res.code === 0) {
						console.log(res);
						let data = res.data;
						// let list = res.data.list;
						let commentList = data.list;
						this.formData.commentId = parseInt(commentList[0].id);
						this.commentId = parseInt(commentList[0].id);
						this.formData.first_comment_id = parseInt(commentList[0].id);
						for(let i in commentList) {
							commentList[i].created_at = this.formatDateTime(commentList[i].created_at,6);
							commentList[i].order_item.amount = this.toYuan(commentList[i].order_item.amount);
							let commentImg = [];
							commentImg = commentList[i].imageUrls.split(',');
							this.$set(commentList[i], 'commentImg', commentImg);
						}
						
						this.commentList = commentList;
						console.log(this.commentList);
						this.replayCommentChild();
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			replyComment() { //回复内容
				if((this.formData.content??'') === '') {
					this.showText("请输入回复内容")
					return;
				}
				console.log(this.formData);
				this.formData.avatar = this.user.avatar;
				this.formData.nickname = this.user.nickname;
				this.post('/api/comment/createComment', this.formData, true).then(res => {
					 console.log(res)
					 this.formData.content = "";
					 this.toast(res.msg);
					 this.replayCommentChild();
				})
			},
			replayCommentChild() { //子订单列表
				this.get(`/api/comment/getCommentByCommentId?commentId=${this.commentId}&page=${this.childPage.page}&pageSize=${this.childPage.pageSize}`, {}, true).then((res) => {
					console.log(res);
					if(res.code === 0) {
						let data = res.data;
						// let CommentChildList = data.list
						this.CommentChildList = data.list;
						console.log(this.CommentChildList);
						for(let i in this.CommentChildList) {
							this.CommentChildList[i].created_at = this.formatDateTime(this.CommentChildList[i].created_at,6);
							this.CommentChildList[i].parent_comment.created_at = this.formatDateTime(this.CommentChildList[i].parent_comment.created_at,6);
						}
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			getIndex() { //获取用户最新头像和昵称
				this.post('/api/center/index', {}, true).then(data => {
					this.user = data.data.user
				})
			},
			replyOn(name,id) { //主订单回复
				this.nickname = name;
				this.formData.commentId = parseInt(id);
			},
			childReplyOn(name,id) { //回复  子订单回复内容
				this.nickname = name;
				this.formData.commentId = parseInt(id);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.appraiseList {
		background-color: #fff;
		.appraise {
			border-bottom: 2rpx solid #e8e8e8;
			background: #fff;
			padding: 20rpx 0;
			margin: 0 32rpx;
			.user {
				.userimg {
					margin-right: 20rpx;
				}
				.user-time {
					color: #919191;
					text-align: right;
					flex:3;
				}
			}
			.product-attr {
				line-height: 48rpx;
				margin-top: 20rpx;
				font-size: 28rpx;
			}
			.product-img {
				image {
					width: 160rpx;
					height: 160rpx;
					margin: 0 16rpx 16rpx 0;
				}
			}
		}
		.score {
			margin: 10rpx 32rpx;
			
		}
		.pjgoods {
			padding: 20rpx 0;
			background: #fff;
			border-top: 2rpx solid #e8e8e8;
			.goods {
				background: #fafafa;
				.img {
					width: 136rpx;
					height: 136rpx;
					padding: 28rpx;
					display: inline-block;
					image {
						width: 136rpx;
						height: 136rpx;
					}
				}
				.warp {
					width: 70vw;
					padding: 20rpx 32rpx 0 0;
					.inner {
						width: 70%;
						-webkit-box-sizing: border-box;
						box-sizing: border-box;
						padding: 0 20rpx;
						text-align: left;
						.name {
							text-align: left;
							color: #333;
							margin-bottom: 20rpx;
							font-size: 28rpx;
						}
						.option {
							color: #8c8c8c;
							font-size: 24rpx;
							flex:1;
						}
					}
					.price {
						width: 30%;
						text-align: right;
						box-sizing: border-box;
						color: #333;
						.product_price {
							margin-bottom: 20rpx;
							font-size: 14px;
						}
						.option {
							color: #8c8c8c;
							font-size: 24rpx;
							flex:1;
						}
					}
				}
			}
		}
	}
	.comment-all   {
		margin: 20rpx 0 100rpx 0;
		background: #fff;
		padding: 20rpx;
		.comment-appraise {
			border-bottom: 2rpx solid #e8e8e8;
			background: #fff;
			padding: 20rpx 0;
			margin: 0 32rpx;
			.user {
				.userimg {
					margin-right: 20rpx;
				}
				.user-time {
					color: #919191;
					text-align: right;
					flex:3;
				}
			}
			.product-attr {
				text-align: left;
				margin-top: 20rpx;
				line-height: 96rpx;
				font-size: 28rpx;
			}
			.product-time {
				padding: 20rpx 0;
				.pjtime {
					color: #919191;
					text-align: left;
				}
			.replyOn {
					padding: 2rpx 20rpx;
					border: 2rpx solid #bfcbd9;
					border-radius: 25rpx;
				}
			}
		}
		.child-bg {
			background: #efedf5;
			padding: 20rpx 20rpx 20rpx 60rpx;
			margin: 0;
		}
		.tilse {
			text-align: left;
			line-height: 64rpx;
			border-bottom: 2rpx solid #e8e8e8;
		}
	}
	.reply-comment {
		padding:20rpx 0;
		.pjtime {
			color: #919191;
			text-align: left;
		}
		.replyOn {
			padding: 2rpx 20rpx;
			border: 2rpx solid #bfcbd9;
			border-radius: 25rpx;
		}
	}
	.comments-child {
		
	}
	.comment-gh {
		height: 116rpx;
		clear: both;
	}
	.callback {
		position: fixed;
		bottom: 0;
		width: 100%;
		background: #fff;
		padding: 20rpx 0 20rpx 10rpx;
		.replyBtn {
				margin: 0 20rpx;
				line-height: 60rpx;
				text-align: center;
				border: 2rpx solid #dd191d;
				border-radius: 5rpx;
				background: #e84e40;
				color: #fff;
				flex:0.2;
		}
		.share-btn {
			color: #6c6b6b;
			margin: 0 20rpx;
			text-align: center;
			line-height: 60rpx;
			flex:0.2;
		}
	}
</style>
