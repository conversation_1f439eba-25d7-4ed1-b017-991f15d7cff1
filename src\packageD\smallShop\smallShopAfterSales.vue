<!-- 小商店售后订单 -->
<template>
  <view id="evaluate">
    <template>
      <u-tabs
        :list="listTab"
        lineWidth="60rpx"
        :current="currentIndex"
        :lineHeight="listTab.length"
        lineColor="#fff"
        :scrollable="false"
        keyName="title"
        ellipsis="false"
        :activeStyle="{
          color: '#f14e4e',
          fontSize: '30rpx',
        }"
        :inactiveStyle="{
          color: '#0c0d0e',
          fontSize: '30rpx',
        }"
        @change="tabsTap"
        itemStyle="height: 88rpx;text-align:center;background:#fff;border:none"
      ></u-tabs>
    </template>
    <view v-if="shopList.length > 0">
      <block v-for="(item, index) in shopList" :key="index">
        <view class="shop">
          <view class="title d-bc">
            <view>售后编号: {{ item.after_sale_sn }}</view>
            <view
              class="title_right"
              v-if="
                item.status === 0 &&
                item.after_sales_audit &&
                item.after_sales_audit.status === -1
              "
            >
              {{ item.after_sales_audit.status_name }}
            </view>
            <view class="title_right" v-else>{{ item.status_name }}</view>
          </view>
          <view class="shopping_box d-c">
            <view class="shop_box_left">
              <image :src="item.small_shop_order_item.image_url"></image>
            </view>
            <view class="shop_box_right">
              <text style="color: #333; font-size: 20rpx">
                {{ item.small_shop_order_item.title }}
              </text>
              <view class="mt-20 shop_standard">
                <text class="font_size12">
                  {{ item.small_shop_order_item.sku_title }}
                </text>
              </view>
            </view>
          </view>
          <view class="refund d-bc">
            <view>退款</view>
            <view>
              退款金额: ¥
              <text style="font-size: 36rpx">{{ item.amount || 0.0 }}</text>
            </view>
          </view>
          <view class="gourl">
            <view
              class="gourl-btn"
              @click="
                navTo(
                  `/packageD/smallShop/smallShopAfterDetail?afterSaleId=` +
                    item.id,
                )
              "
            >
              查看详情
            </view>
          </view>
        </view>
      </block>
      <view class="pb-20"></view>
      <view v-if="shopList.length > 0" class="d-cc fs-1 c-5e5e5e pb-25 mt-25">
        暂无更多~
      </view>
    </view>
    <view class="empty" v-else>
      <u-empty
        mode="order"
        marginTop="100rpx"
        textSize="28rpx"
        iconSize="150"
      ></u-empty>
    </view>
  </view>
</template>
<script>
export default {
  data() {
    return {
      listTab: [
        {
          title: '全部',
        },
        {
          title: '已通过',
        },
        {
          title: '已驳回',
        },
        {
          title: '已关闭',
        },
      ],
      currentIndex: 0, // tabs切换索引
      aftersaleStatu: '', // 售后订单状态
      total: null,
      formData: {
        page: 1,
        pageSize: 10,
        after_sale_sn: '',
        type: 0,
        order_sn: 0,
      },
      shopList: [],
      orderListApi: '', // 数据列表请求api
    }
  },
  onLoad() {},
  onShow() {
    this.afterSalesList()
  },
  onReachBottom() {
    const allTotal = Number(this.formData.page) * Number(this.formData.pageSize)
    const allTotalInt = Math.ceil(allTotal)
    if (allTotalInt < this.total) {
      this.status = 'loading' // 加载中状态
      // 当前条数小于总条数 则增加请求页数
      this.formData.page++
      this.afterSalesList()
    } else {
      this.status = 'nomore' // 加载完状态
      // console.log('已加载全部数据')
    }
  },
  onPullDownRefresh() {
    this.shopList = []
    this.formData.page = 1
    // 调用获取数据方法
    this.afterSalesList()
    setTimeout(() => {
      // 结束下拉刷新
      uni.stopPullDownRefresh()
    }, 1000)
  },
  methods: {
    tabsTap(index) {
      this.currentIndex = index.index // tabs切换索引
      switch (
        this.currentIndex // 通过 1   驳回-1  关闭 -2
      ) {
        // 全部
        case 0:
          this.aftersaleStatu = ''
          break
        // 已通过
        case 1:
          this.aftersaleStatu = 1
          break
        // 已驳回
        case 2:
          this.aftersaleStatu = -1
          break
        // 已关闭
        case 3:
          this.aftersaleStatu = -2
          break
        default:
          break
      }
      this.afterSalesList()
    },
    afterSalesList() {
      // 订单列表
      this.orderListApi = `/api/smallShop/afterSales/list`
      const params = {
        page: this.formData.page,
        pageSize: this.formData.pageSize,
      }
      if (this.aftersaleStatu !== '') {
        params.status = this.aftersaleStatu
      }
      this.get(this.orderListApi, params, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            const newlist = data.list
            this.total = data.total
            for (const i in newlist) {
              // newlist[i].small_shop_order_item.amount = this.toYuan(newlist[i].small_shop_order_item.amount); //?.可选链的使用
              newlist[i].amount = this.toYuan(newlist[i].amount) // ?.可选链的使用
            }
            this.shopList = []
            this.shopList.push(...newlist)
            console.log('this.shopList', this.shopList)
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
  },
}
</script>

<style lang="scss">
#evaluate {
  .shop {
    background: #fff;
    margin: 20rpx;
    border-radius: 10rpx;
    .title {
      line-height: 72rpx;
      padding: 0 24rpx;
      .title_right {
        color: #f15353;
        margin-right: 2rpx;
        font-size: 28rpx;
      }
    }
    .shopping_box {
      margin-top: 30rpx;
      padding-bottom: 30rpx;
      .shop_box_left {
        margin: 0 20rpx;
        image {
          width: 140rpx;
          height: 140rpx;
          background-color: #666666;
          border-radius: 8rpx;
        }
      }
      .shop_box_right {
        width: 100%;
        padding: 0 20rpx;
        box-sizing: border-box;
        .shop_standard {
          width: 317rpx;
          height: 46rpx;
          // padding: 12rpx 18rpx 12rpx 20rpx;
        }
      }
      .calculate {
        padding: 0 30rpx 0 0;
      }
    }
    .refund {
      width: 100%;
      box-sizing: border-box;
      line-height: 72rpx;
      font-size: 28rpx;
      padding: 0 24rpx;
    }
    .gourl {
      display: block;
      border-top: 2rpx solid #ebebeb;
      text-align: right;
      height: 78rpx;
      padding-right: 28rpx;
      overflow: hidden;
      .gourl-btn {
        display: inline-block;
        border: 2rpx solid #f15353;
        border-radius: 30rpx;
        padding: 8rpx 16rpx;
        color: #f15353;
        text-align: center;
        margin: 9.6rpx 0 0 9.6rpx;
      }
    }
  }
}
</style>
