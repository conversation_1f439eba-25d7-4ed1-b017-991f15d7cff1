<template>
    <!-- 团队分销商和小商店 -->
    <view>
        <!-- 选项卡切换 -->
        <view class="d-f pt_20 bg-white">
            <view v-for="(item, index) in list" :key="index" @tap="oneMenuCut(item, index)"
                :style="index == 0 ? 'padding-left: 162rpx;' : 'padding-left: 250rpx;padding-right:162rpx'">
                <view :style="item.show == true ? 'color: #f14e4e;' : 'color: #2f2f2f;'" class="fs-2">{{ item.name }}
                </view>
                <view style="width: 75rpx;height: 5rpx;background-color: #f14e4e;" v-show="item.show == true"
                    class="mt_22 ml-5"></view>
            </view>
        </view>
        <template v-if="total > 0">
            <view class="p-25 f fac fjsb radius15" @click="distributorsAndShops">
                <view class="fs-1-5 sum">总数：{{ total }}</view>
            </view>

            <view v-for="item in tablerList" :key="item.id" class="bg-white mb_20 p-25 radius15 vip_card">
                <view class="fs-2 f fac">
                    <view class="f-bold">会员昵称</view>
                    <view class="vipNum f fac fjc ml-15">{{ item.user_info.nickname }}</view>
                </view>
                <view class="fs-1 f fac fjsb mt-30">
                    <view>{{ formatDateTime(item.created_at) }}</view>
                    <view v-if="switching === 'distribution'">名称：{{ item.distributor_level_info.name }}</view>
                    <view v-else>小商店名称：{{ item.small_shop.title }}</view>
                </view>
            </view>
        </template>
        <view v-else class="mt-20 fs-1 d-c">暂无数据~</view>
    </view>
</template>

<script>
export default {
    name: 'distributorsAndShops',
    data() {
        return {
            list: [{
                name: '分销商',
                show: true,
            }, {
                name: '小商店',
                show: false
            }],
            switching: 'distribution',
            tablerList: [],
            total: null,
        }
    },
    onShow() {
        this.tablerListFun()
    },
    methods: {
        oneMenuCut(item, index) { //菜单栏切换
            /* 颜色切换 */
            if (item.show == false) {
                this.$set(this.list[index], 'show', true)
                for (var i = 0; i < this.list.length; i++) {
                    if (i != index) {
                        this.$set(this.list[i], 'show', false)
                    }
                }
            }
            /* 选项卡内容切换 */
            console.log(index, 'index');
            if (index == 0) {
                this.switching = 'distribution'
            } else {
                this.switching = 'shop'
            }
            this.tablerList = []
            this.tablerListFun()
        },
        tablerListFun() {
            if (this.switching === 'distribution') {
                this.get('/api/institution/teamDistributorList', {
                    page: 1,
                }).then(res => {
                    if (res.code === 0) {
                        this.tablerList = res.data.list
                        console.log(res.data);
                        this.total = res.data.total
                    }
                })
            } else {
                this.get('/api/institution/teamShopList', {
                    page: 1
                }).then(res => {
                    if (res.code === 0) {
                        this.tablerList = res.data.list
                        console.log(res.data);
                        this.total = res.data.total
                    }
                })
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.sum {
    color: rgba(16, 16, 16, 0.72);
}

.vip_card {
    margin: 0 20rpx 20rpx;
}

.vipNum {
    height: 46rpx;
    padding: 0 20rpx;
    background-color: rgba(239, 84, 82, 0.14);
    color: rgba(239, 84, 82, 1);
    border-radius: 23rpx;
}
</style>